# 更新日志

<details>
<summary>稳定版 更新日志</summary>

## 稳定版 v4.1.0

### 更新内容 v4.1.0

- 前端适配 `<think></think>` 标签格式的深度思考显示（`reasoning_content` 格式需开发版）
- 剔除使用外部联网插件，改用 API 方式调用联网搜索
- 联网插件已支持多种方式（需自行登录以下网站，获取对应的 Key）：
  - [智谱 web-search-pro](https://bigmodel.cn)
  - [博查 web-search](https://open.bochaai.com)
  - [Tavily](https://app.tavily.com/home)

### 注意事项 v4.1.0

- 升级前请做好数据库备份，本次升级项目结构有所调整，整合包迁移至 `AIWebQuickDeploy` 文件夹下，使用本地存储的文件，需自行迁移
- 本次升级内容较少，思考推理模型搭配其他模型混合调用、联网搜索详情显示、UI 优化等升级本次不予合并进稳定版

## 稳定版 v4.0.0

### 开源说明 (v4.0.0)

- v4.0.0 版本源码已开源，支持二次开发。
- 部分不再维护的页面已移除，如有多页面需求可继续使用旧版或开发版（赞助获取）。

### 更新内容 (v4.0.0)

- **本地存储**：支持本地数据存储，生成的视频和音乐默认保存到 `/public/file`。
- **知识库问答**：新增关键词匹配功能，可通过内容预设提升问答效果。
- **Midjourney 绘图**：支持 `describe` 功能，实现以图生文。
- **Flux 绘画**：新增内置插件 `flux-draw`，需添加模型后启用。
- **Suno 音乐生成**：支持歌词调整、音乐类型和风格选择。
- **TTS 音色**：新增 OpenAI 语音朗读人音色选择功能。
- **CogVideoX 视频生成**：新增内置插件 `cog-video`，需添加模型后启用。
- **积分显示优化**：积分超过 99,999 时显示为"无限额度"。
- **多图分析**：支持单次最多上传 4 张图片进行识别。
- **更多更新**：功能细节请自行探索。

### 注意事项 (v4.0.0)

- 本地存储功能需配置跨域访问和文件读写权限，更新迁移时请备份 `/public/file`。

</details>

## 开发版 v5.1.3

### ✨ 新增功能 v5.1.3

- **模型附加参数配置**：新增模型附加参数设置功能，支持在 API 调用时自动合并预设参数，可用于配置联网搜索控制、思考推理模式、温度值等高级参数，提升模型调用的灵活性和个性化配置能力
- **多媒体解析功能升级**：将原有的图片解析选项升级为多媒体解析配置，新增视频内容解析支持，可在模型设置中灵活选择图片或视频解析调用方式
- **BFL 绘画格式支持**：新增对 Black Forest Labs (BFL) 绘画格式的完整支持，扩展 Flux 系列模型的兼容性

### ⚡️ 体验优化 v5.1.3

- **标题及提问建议生成优化**：重构标题生成、提问建议生成算法，智能截取用户提问或 AI 回复的前 200 个字符作为参考内容，提升建议生成速度并降低 tokens 消耗。
- **系统提示词时间优化**：优化系统提示词中的时间精确度，将时间戳精度从分钟级调整为小时级，提高相同时间段内的模型缓存命中率，降低特定模型 API 调用成本
- **统一绘画比例选择**：统一各类绘画功能的比例选择
- 精简更新管理端模型选择列表
- 优化模型，头像匹配逻辑
- **管理端模型列表优化**：精简更新管理端模型选择列表
- **模型头像匹配逻辑优化**：优化模型与头像的智能匹配算法，提升头像显示的准确性和一致性

### 🐛 问题修复 v5.1.3

- **浏览器朗读按钮显示修复**：修复在浏览器不支持语音合成的情况下仍显示朗读按钮的问题。
- **配置缓存实时性修复**：修复由于缓存机制导致的配置更新延迟问题，解决部分配置保存后无法及时生效、需要重启服务才能应用的问题
- **界面显示问题修复**：修复 MCP 工具调用展示和提问建议组件宽度溢出的问题

## 开发版 v5.1.2

### ✨ 新增功能 v5.1.2

- **Mermaid 流程图快速选择**：新增流程图类型快速选择功能，支持通过点击操作快速添加不同的流程图类型。
- **收藏应用提问模板**：新增对话侧边栏收藏应用支持唤起提问模板弹窗功能，需在应用管理中预先配置模板内容。
- **文档分析增强**：新增侧边文档阅读功能，支持在右侧面板实时阅读文档（目前支持 PDF、Word、Excel、Ppt、markdown 及普通 txt）内容，并基于文档内容快捷提问。
- **S3 存储协议支持**：新增对 Amazon S3 兼容存储协议的完整支持，提供更灵活的文件存储解决方案。
- **豆包智能绘图插件**：新增豆包智能绘图插件，全面支持`通用2.1文生图`、`通用2.0 Pro图生图`以及`Inpainting涂抹编辑`功能，系统将根据用户输入内容和需求场景智能选择最适合的绘图模式，为用户提供更精准、高效的 AI 绘图体验。"
- **Flux-Kontext 图生图插件**：新增 `black-forest-labs/flux-kontext` 图生图插件，图像生成质量媲美 `gpt-image-1`，渲染速度显著提升，支持快速图像编辑和变换，推荐配置为全局编辑模型。
- **绘画编辑预览模型配置**：后台模型管理新增绘画编辑预览模型配置选项，支持自定义开启侧边编辑预览功能并指定绘画编辑专用模型。需在创意模型中配置对应的编辑模型，支持`gpt-image-1`、`doubao-image`、`black-forest-labs/flux-kontext-pro`或自行填写，可自行选择，是否支持蒙版编辑。

### ⚡️ 体验优化 v5.1.2

- **设置中心权限管理**：新增登录状态检测机制，限制未登录用户访问账户管理和会员中心等敏感功能页面。
- **流程图插件优化**：优化流程图插件内置提示词算法，扩展支持多种流程图类型（包括时序图、甘特图、思维导图、架构图等），提升图表生成的准确性和多样性。
- **图片编辑蒙版优化**：优化图片编辑弹窗中的蒙版选择交互体验，新增撤销和重做选项。
- **用户端编译优化**：优化用户端编译，结构化编译后产物。
- **绘画插件类型调用重构**：重构绘画插件类型调用选择机制，支持在模型设置中为创意模型灵活配置绘画类型。全面兼容 `DALL-E`、`GPT-Image-1`、`Midjourney`、`Chat 对话正则提取`、`豆包绘画`以及`replicate`等格式，用户可根据实际需求自由选择绘画方案，解除模型固定限制，提升绘画功能的配置灵活性。

### 🐛 问题修复 v5.1.2

- **HTML 预览标题修复**：修复启用 HTML 预览并关闭后网站标题重置为 "99AI" 的问题，已定向到设置的网站名称。
- **公告自动弹出修复**：修复开启自动打开公告功能后错误弹出用户协议页面的问题。
- **Mermaid 流程图渲染修复**：修复 Mermaid 流程图渲染异常导致页面样式错乱的问题，确保流程图正常显示且不影响页面布局。
- **侧边预览全屏背景修复**：修复侧边预览全屏模式下的背景显示问题，提升全屏预览的视觉体验。
- **插件卡片开关样式修复**：修复插件卡片中切换开关在部分浏览器下出现的样式错位问题，确保开关组件在不同浏览器环境中的显示一致性。

## 开发版 v5.1.1

**发布日期：** 2025-05-23

### ✨ 新增功能 v5.1.1

- **Mermaid 流程图插件**：新增 Mermaid 流程图插件，可在对话中创建、渲染和编辑各类流程图。
- **代码导出增强**：代码编辑预览区域现已支持多种导出格式：
  - PDF 格式（便于文档分享）
  - PNG/SVG 格式（便于图片展示）
  - HTML 格式（便于网页嵌入）
- **适配 SUNO4.5 模型**：SUNO 版本适配 suno4.5，增强 AI 音乐生成能力。
- **图片尺寸选择**：图片编辑预览区域新增尺寸选择功能，支持自定义输出尺寸，满足不同场景需求。
- **输入框全屏模式**：新增全屏编辑按钮，切换后将最大化可用编辑空间，显著提升长文本编辑和预览体验。
- **模型思考类型配置**：在管理端模型设置中新增"模型思考类型"选项，管理员可为每个模型单独配置推理方式，包括直接输出模式（不进行额外思考推理，快速响应）、全局推理模式（使用全局统一的思考推理逻辑）、高级推理模式（运用模型本身的高级推理能力）。
- **Claude-4 模型支持**：后台模型管理新增 Claude-4 系列模型选项。
- **嘟噜支付集成**：后台支付系统新增"嘟噜支付"作为新的支付方式选项，为用户提供更多便捷的支付渠道。
- **HTML 内容渲染控制**：后台显示设置新增 HTML 内容渲染开关选项，可控制是否在页面中渲染 HTML 标签。注意：开启后可能影响页面样式，建议仅在确有需要时启用。

### ⚡️ 体验优化 v5.1.1

- **界面视觉升级**：对用户端界面进行全面的视觉和交互细节调整，采用更加清爽、现代化的设计风格，提升整体用户体验。
- **思维导图编辑集成**：思维导图插件的编辑功能已无缝集成至侧边栏，操作更加便捷直观。
- **文件上传体验升级**：将分散的多个上传入口整合为统一的上传按钮，全面支持文件拖拽上传操作。
- **滚动条样式统一**：对应用内各处滚动条样式进行统一优化，提升界面的视觉一致性和整体美观度。
- **移动端设置中心优化**：设置中心在移动设备上已优化为二级菜单模式，改善移动端用户的导航和使用体验。
- **首页文案优化**：对首页欢迎语文案进行调整，使表达更加友好、清晰，提升首次使用印象。
- **配置变量规范化**：修正部分后台配置变量名的拼写错误，提升系统规范性。如发现原有配置失效，请在后台管理界面重新设置相关配置项。
- **水印显示增强**：优化页面水印显示逻辑，新增用户昵称信息展示，提供更加个性化和直观的用户身份标识体验。

### 🐛 问题修复 v5.1.1

- **对话错误反馈优化**：修复对话过程中发生错误（如积分不足、网络异常等）时用户界面无响应反馈的问题。
- **后台应用列表分页修复**：修复后台管理界面中应用列表分页功能无法正常切换选择的问题。
- **网站图标配置修复**：修复后台系统配置中网页 ICO 图标设置无法正常生效的问题。

### ⚠️ 重要提示 v5.1.1

- **依赖更新**：本版本对部分依赖项进行了精简和升级，升级后请务必在项目根目录执行 `pnpm install` 命令重新安装依赖，确保所有功能正常运行。
- **稳定性说明**：开发版本测试可能尚未覆盖所有使用场景，可能存在功能或显示不稳定的情况。建议谨慎选择升级时机，升级前做好完整的数据和配置备份，优先在测试环境验证功能。
- **反馈渠道**：如在使用过程中遇到任何问题，欢迎通过以下渠道反馈：GitHub Issues（项目私有代码库的 Issues 区）或后台反馈（后台管理界面 → 更新日志页面 → 问题反馈入口），我们会及时跟进处理您的反馈和建议。

## 开发版 v5.0.4[2025-05-11]

- 插件及模型新增 `gpt-image-1` 模型说明及预选项。
- 调整 docker 配置，根目录执行

```bash
docker-compose up -d
```

本地启动服务。机器码会自动填充到`.env.docker`中，在授权站获取到授权码后填回到该文件即可，docker 更新无需重新 build，仅需更新根目录下相关文件，应用会自动更新。（注意：重新 build 机器码会改变）

- 修复代码块复制点击无效的问题。
- 修复旧版本升级，因数据库同步问题导致的聊天内容不显示的问题。

## 开发版 v5.0.3[2025-05-10]

- `.env`新增 weChatApiUrlToken=https://api.weixin.qq.com/cgi-bin/token 配置。
- 更新日志模块新增问题反馈表单。

## 开发版 v5.0.2[2025-05-10]

- 修复数据库同步报错的问题

## 开发版 v5.0.1 [2025-05-09]

### 更新内容 v5.0.1

- 调整授权机制，用户可通过[授权管理系统](https://auth.lightai.cloud/)自行生成授权码
- 修复 MCP 服务配置保存导致的服务错误问题，建议重新配置 MCP 服务
- 调整 MCP 服务重试机制，限制最多重连 5 次，有效减少内存消耗
- 优化数据库结构：`chatlog`表的`content`和`fileVectorResult`字段已升级为`mediumtext`，支持更大存储容量（升级前务必备份数据库，系统将尝试自动调整，若失败需先停止项目再手动修改）
- 后端新增单 IP 调用频率限制（测试功能，后续将新增后台配置选项）

- 更统一的用户端 UI 结构，提升整体使用体验
- 调整应用广场页面显示，新增应用背景图及对话预设弹窗
- 新增`html`右侧预览功能，支持即时查看编辑效果
- 新增`mermaid`流程图预览编辑器（更多代码预览运行功能即将推出）
- 后台显示设置新增流式对话缓存控制开关，开启后会对 AI 对话进行缓存输出，优化输出平滑性；关闭则完全依赖 API 流式输出

- 修复提问编辑时的编辑高度显示问题
- 修复开启百度敏感词过滤后无法保存 AI 对话的问题
- 修复默认模型排序未按预期排序的问题
- 后台用户管理新增用户昵称模糊搜索功能
- 新增微信公众号迁移功能，可按需使用

- 新增`gpt-image-1`绘图和编辑插件支持，包括右侧预览功能和全图片调用`gpt-image-1`进行二次编辑的能力

### 注意事项 v5.0.1

- 开发版测试有限，可能存在功能或显示不稳定情况，请谨慎升级并做好相关备份
- 如遇问题可通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈

## 开发版 v4.3.4 [2025-04-21]

### 更新内容 v4.3.4

- MCP 新增 `sse` 格式支持，预兼容 `StreamableHTTP` 格式。（测试版，不稳定，建议优先使用 `stdio` 格式）。
- 插件新增 `seedream-3.0`（即梦 3）支持（dalle 格式）。
- 用户端设置页面初步适配移动端。

### 注意事项 v4.3.4

- 开发版测试有限，可能存在功能或显示不稳定情况，请谨慎升级并做好相关备份。
- 如遇问题可通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈。

## 开发版 v4.3.3 [2025-04-20]

### 更新内容 v4.3.3

- 后台新增微信公众号自定义菜单设置（测试功能），提升公众号管理灵活性。
- 对话模型地址新增版本兼容，链接中带类似 `/v1` 则自动使用对应 API 版本，增强多平台适配能力。
- 取消隐藏模型设置中 创意模型/特殊模型 的某些设置，提高可配置性。
- 修复后台插件设置无法上传图片的问题，恢复插件配置完整功能。
- 修复微信公众账号自动回复报错的问题，增强微信集成稳定性。
- 修复未使用微信登录，登录页面报错的问题，提升非微信用户登录体验。
- 修复设置中心修改密码失效的问题，确保账户安全设置正常工作。

### 注意事项 v4.3.3

- 开发版测试有限，可能存在功能或显示不稳定情况，请谨慎升级并做好相关备份。
- 如遇问题可通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈，我们将及时跟进解决。

## 开发版 v4.3.2 [2025-04-16]

### 更新内容 v4.3.2

- 修复验证码登录页，滑动弹窗被遮挡导致的登录实效问题。
- 修复 MCP 服务新建、编辑配置失效的问题。
- 修复管理端无法上传图片的问题
- 给文件分析添加一定的容错机制
- 设置中心-账户管理剔除用户名显示，只保留可编辑的昵称显示。
- 用户端流式新增两档更快的缓存输出，减少极快模型的输出等待。

### 注意事项 v4.3.2

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。有问题可用通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈。

## 开发版 v4.3.1 [2025-04-15]

### 更新内容 v4.3.1

- 优化登录逻辑，使用验证码登录替代注册。
- 管理端新增版本说明及更新日志显示。
- 思考模型新增 `grok-3-mini-latest`模型兼容，默认使用 `requestConfig.reasoning_effort = 'high'` 参数。
- 思考模型新增 `gemini-2.5-pro-exp-03-25` 模型兼容。
- 思考流新增 联网、MCP 工具、文件分析、图片分析 结果调用支持。
- 重构文件解析、图片解析。对于不支持图片的模型，开启全局解析将使用图片解析模型解析图片；
  对于普通模型，使用文件向量解析，将调用向量模型，提取相关文本，完成文件解析功能。（注意配置文件字符限制以控制文本解析上限）
  - 图片解析支持`逆向格式`、`GPT Vision`、`全局解析`
  - 文件解析支持`逆向格式`、`向量解析`
- 限制文件分析最多 5 个文件，图片解析最多 4 张，单个文件限制最大 10M，单用户一小时最大上传数限制 100 个（包含图片/文件）。
- Midjourney 新增 `--v 7` 选项。`-- 7` 积分扣除为基础系数乘 8，`--draft`为基础系数乘 2。
- 新增豆包 `seededit` 图像编辑插件支持（dall-e 格式），效果不如 `gpt-4o-image`，但更稳定。
- 修复模型管理-用户端显示，未按预期仅参与用户端显示的问题。
- 修复 MCP 配置 保存、更新卡顿问题，新增配置一键导出功能。
- 应用新增 `flowith`，怎么用先自己摸索。

### 注意事项 v4.3.1

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。有问题可用通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈。
- 个人中心移动端有待适配，一些显示项目的隐藏后续再调整。
- 次版本调整的内容比较多，依然有一些已知的问题有待后续修复，升级前请做好备份。

## 开发版 v4.2.4

### 更新内容 v4.2.4

- 新增旧版对话兼容逻辑，当数据库迁移不成功，content 值为空时，调用旧的数据。
- 绘画插件新增等待绘图占位框，优化用户体验。
- 优化用户端报错提醒。
- 优化当模型设置发生变化时，新建对话继承模型时无法应用新设置的逻辑。
- 兼容 `deepseek-r1`、`deepseek-reasoner` 作为普通模型时思考流及结果展示，修复这类模型作为普通模型回复被截断的问题。
- MCP 工具新增循环判断，支持一次调用多个工具。（需全局模型支持 FC 工具）

### 注意事项 v4.2.4

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。有问题可用通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈。
- GPT 新绘画极不稳定，且上游变动频繁，插件模式可能无法到达预期效果，可考虑选择普通模型调用。

## 开发版 v4.2.3

### 更新内容

- 修复后端历史消息读取排序错乱的问题
- 兼容思考流 `<think></think>` 标签，修复刷新后，思考内容不展示的问题。
- 优化 `MCP 工具` 展示逻辑，用户端新增工具开关，开启后将从已配置的 `MCP 工具` 中自动选择可用工具并调用(需全局模型支持 fc 调用)
- gpt 绘画新增 `gpt-4o-image` 参数。

### 注意事项 v4.2.3

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。有问题可用通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈

## 开发版 v4.2.2

### 更新内容 v4.2.2

- 修复 luma 绘图拓展上传图片报错的问题。
- 调整 `gpt-4o-all` 适配逻辑，精简中间 Url 获取。
- 修复生成回答建议的时候，无法读取回答内容的问题。
- 优化编辑及重新生成逻辑，保留图片项。
- 修复 AI 回复后，部分功能需刷新后才能使用的问题。
- 修复首页默认状态下，插件选择区域宽度显示异常的问题。
- 修复一些状态下，计费异常的问题。
- 优化后台对话记录展示，合并所有内容到对话记录中，同时增加详情弹窗。

### 注意事项 v4.2.2

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。有问题可用通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈

## 开发版 v4.2.1

### 更新内容 v4.2.1

- 重构部分回复结构，优化流式回复传递及缓存方式，修复回复卡顿问题
- 对话数据库结构有较大调整，升级前务必做好备份
- 调整 AI 音乐、AI 视频部分前端 UI，采用卡片化设计
- 优化前端卡密兑换提醒，增强用户体验
- 新增 `grok-2-image` 绘画插件支持
- 新增 `GPT-4o` 绘图插件支持
- 模型设置新增模型预设设置，可选择附加模式、覆盖模式作用于模型普通 system 预设
- 侧边栏"我的应用"新增一键取消收藏选项
- 新增用户积分小于 10 时，左下积分显示部分自动调整成会员中心，引导用户购买套餐
- 插件应用新增外链管理，支持右上角显示
- 修复固定应用点击没反应的问题
- 修复批量生成卡密无法选择套餐类型的问题
- 修复微信扫码登录重复验证二维码的问题

### 注意事项 v4.2.1

- 此版本数据库结构有较大变动，升级前务必做好备份工作。
- 该次升级进度提前，许多地方尚未完全优化测试，介意勿升。
- 升级过程中遇到问题，可以在群内友善交流。

## 开发版 v4.1.4

### 更新内容 v4.1.4

- 水一个版本号
- 修复 MCP 在某些场景下启动不成功的问题
- 优化流式回复逻辑，增强用户体验

## 开发版 v4.1.3

### 更新内容 v4.1.3

- 修复应用新建及调用中出现的问题。
- 修复对话列表点选框被遮挡的样式问题。
- 优化代码预览逻辑，将代码预览按钮迁移到代码块右上角，增强用户体验。
- 重构部分对话逻辑，使用 OpenAI SDK 方式调用对话。
- 新增 Python 代码运行功能。
- 新增 MCP 服务适配，可在 插件应用-MCP 配置 中自行设置可用的 MCP 服务（测试功能）。
- 新增 `gemini-2.0-flash-exp-image` 模型选择。

### 注意事项 v4.1.3

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。有问题可用通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈
- 此次更新需运行 `pnpm install` 安装新的依赖，MCP 支持为测试功能，请谨慎使用涉及个人数据及隐私的工具。

## 开发版 v4.1.2

### 更新内容 v4.1.2

- 修复新建应用报错的问题
- 修复 SunoMusic 音频生成成功后，用户端持续刷新的问题。
- 修复 @ 应用预选框样式显示错误的问题。
- 优化固定模型应用深度思考、联网搜索继承显示问题。
- 优化用户专属应用逻辑，套餐中可单独设置套餐包含的应用标签。可在应用分类中，自行设置，用户无权限的会员应用是否可见、可用。

### 注意事项 v4.1.2

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。
- 专属应用目前处于测试优化阶段，有问题可用通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈

## 开发版 v4.1.1

### 更新内容 v4.1.1

- 调整 Luma 视频服务请求超时时间，将 Luma 视频服务请求超时时间从 20 秒延长至 30 秒，提高长时间视频生成请求的容错性
- 优化网络搜索服务配置和结果处理，调整 Tavily API 搜索参数，增加最大结果数至 10
- 联网搜索拓展图片内容获取，支持在回答中合适位置，显示图片内容。（依赖搜索 API 及对话模型能力，注意有时候图会裂开）
- 优化阿里云 OSS 上传安全性配置，指定返回 URL 为 HTTPS
- 优化代码预览页复制策略，在一些移动端在复制失败时提供友好的用户提示和手动复制选项
- 本地敏感词管理新增批量添加功能，支持一次性添加多个敏感词（单次上限 1000 个）
- 新增微信内静默登录获取更新用户昵称，头像功能。
- 单个应用新增多分类（标签）支持，应用分类新增会员属性，会员应用限付费会员使用。

### 注意事项 v4.1.1

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。
- 此版本对应用及分类数据库有所调整，理论上可以向上兼容升级，不排除因数据库版本问题丢失部分数据的可能性。

## 开发版 v4.0.6

### 更新内容 v4.0.6

- 修复因前端错误处理导致的一些反馈异常问题
- 修复绘画，音乐，视频生成可能出现的不自动刷新的问题
- 修复开启连续绘画后，上下文处理错误问题
- 取消后台认证设置未填非必要信息无法保存的限制
- 为提高用户体验，音视频存储取消仅本地存储限制
- 当后台选择隐藏朗读按钮时，将调用浏览器原生文本朗读进行朗读。（测试功能）

### 注意事项 v4.0.6

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。
- 因项目为空闲时间独立维护，出现问题属正常现象，对 bug 比较敏感的小伙伴请勿第一时间更新。

## 开发版 v4.0.5

### 更新内容 v4.0.5

- 新增代码预览页分享功能
- 新增 [Tavily](https://app.tavily.com/home) 联网支持，需自行登录网站，获取对应的 Key
- 联网搜索新增多 Key 支持，多个 Key 用英文逗号隔开，搜索时随机调用。
- 实现 luma-video 视频延长拓展能力。
- 增加一些页面过渡动画，让页面过渡更流畅。调整美化 UI
- 重构部分后端回复逻辑，优化回复流畅度

### 注意事项 v4.0.5

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。
- 近期对项目底层内容调整较大，可能存在一些新的 bug，有问题可通过[私有库 issues](https://github.com/AIWeb-Team/AIWeb/issues)反馈

## 开发版 v4.0.4

### 更新内容 v4.0.4

- 优化移动端底部边距，适配 iOS 设备的底部安全区域，确保内容不会被滑动条遮挡
- 修复一些用户端样式显示问题。
- 允许最多 3 台设备同时登录，暂未开放后台自定义（测试功能）。
- 联网新增[博查 web-search](https://open.bochaai.com/)（非免费，需自行申请 KEY）
- 优化后端联网搜索逻辑，未搜索到结果不会直接返回空白。

### 注意事项 v4.0.4

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。
- 此版本无功能行更新，主要是修改前面的一些 bug，个人中心页暂时隐藏，新版聚合设置页即将上线。

## 开发版 v4.0.3

### 更新内容 v4.0.3

- 优化深度思考兼容逻辑，上下文传参剔除 AI 回答部分，节省 tokens 消耗
- 思考模型（R1）兼容识图功能（需全局模型支持）
- 暂时取消依赖外部联网插件，改用智谱 web-search-pro（限免中），优化联网搜索体验和 UI 展示
- 修复管理段套餐无法删除的问题
- 显示设置新增显示卡密兑换开关，开启后将在购买页面显示卡密兑换按钮，可按需开启
- 调整主页面布局，调整输入框 UI

### 注意事项 v4.0.3

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。
- UI 调整是个长期工作，有问题可在群内反馈，共同打磨优化。
- 联网搜索功能后期计划适配更多 API，或优化外置联网插件，当前方案可先白嫖[web-search-pro 地址](https://www.bigmodel.cn/invite?icode=oxNMS2FHOlZn63EUMmv8u3HEaazDlIZGj9HxftzTbt4%3D)

## 开发版 v4.0.2

### 更新内容 (v4.0.2)

- 新增 `deepseek-r1` 相关思考模型思考过程显示.
- 自动兼容 `<think></think>` 标签及官方`reasoning_content`格式。
- 调整客户端联网搜索，深度搜索位置，需在 模型管理-模型配置 单个模型中开启。(联网插件配置移动到 模型管理-基础配置)

### 注意事项 (v4.0.2)

- 开发版测试有限，可能存在 功能/显示 不稳定情况，请谨慎升级，做好相关备份。
- 深度思考按钮开启后，将混合调用思考模型（需在 模型管理-基础配置 中配置思考模型）的思考加当前模型的正常回答，相比纯思考模型能力提升有待测试，另可直接在模型配置中增加思考模型。

## 开发版 v4.0.1

- 更新版本号。
- 为方便开发，多页面功能已精简，后续不再维护，有相关需求可继续使用历史版本。
- 版本号说明： v*.*.0 版本为测试后的稳定版，其他版本为日常开发版，可能存在因测试不充分存在的 bug，出现功能性 bug 可以在群内反馈。

## 开发版 v3.8.3

- 修复微信支付官方渠道电脑端调用支付失败的问题
- 修复重复上传同一文件点击无反应的问题
- 修复 flux 插件调用失败的问题，新增 `flux-dev` 、 `flux-pro` 模型支持，之前的 `flux-draw` 需手动修改成`flux`。
- 新增识图单次多图像分析（最多 4 张）支持。
- 模型全局设置新增 base64 格式转换开关。开启后，识图时将使用 base64 格式，对于本地/存储桶 链接 API 端无法访问时建议开启。
- 风控管理-风控安全配置 新增敏感词过滤开关。

## 开发版 v3.8.2

- 新增 `o1-mini`、`o1-preview` 适配，自动剔除各种附加参数及预设，不支持流式。(仅作为测试功能，用户端会感觉等很久，然后答案一下子跳出来)

## 开发版 v3.8.1

- 修复 数据管理-对话记录 在特殊情况下显示错乱的问题。
- 风控管理-身份验证设置 新增实名认证设置，可自行设置是否开启强制实名认证及手机号验证。
- 用户触发自定义敏感词改为弹窗提醒方式，并会将 AI 回答中的敏感词，替换为`*`。
- 修复移动端，应用广场弹窗显示不全的问题。
- 访问管理-用户协议配置 新增用户协议设置，开启将在注册/登录页强制要求勾选相关内容。
- 规范化重命名一些文件名称，对于大小不敏感的系统，可能需要重新拉取仓库。

## 开发版 v3.8.0

- 新增 CogVideoX（参数：cog-video）、Flux 绘画（参数：flux-draw）插件，均为内置插件，需先适添加模型，然后添加对应插件。
- 后台模型设置添加 `gpt-4o-2024-08-06`、`gpt-4o-mini-2024-07-18` 模型。
- 模型管理-全局设置新增 TTS 音色（OpenAI 语音朗读人）选择。
- 取消支付确认弹窗，调整扫码页面 UI 显示。
- MJ 绘画新增 `6.1` 模型。
- 模型积分超过 99999 默认显示无限额度。

## 开发版 v3.7.7

- 调整水印显示内容，改为 6 位数的字母+数字。（计算方式为用户 `ID✖️123+1亿` 转 36 进制取后六位）
- 修复对话列表中没对话情况下，无法提问及新建对话的问题。
- 强制欢迎页全屏显示。

## 开发版 v3.7.6

- 新增自定义欢迎页，跳转样式参考：

  ```html
  <button class="button" onclick="goToChat()">开始对话</button>
  <script>
    function goToChat() {
      window.parent.postMessage("goToChat", "*");
    }
  </script>
  ```

- 对于上条回复未完成的，限制新的提问。
- 水印样式调整为 @用户名+ID 形式。
- 修复特殊场景下，新对话按钮失效的问题。

## 开发版 v3.7.5

- 修复以图生文上传图片后，无法提交的问题。
- 剔除默认显示的新建对话页，限制新建对话的作用范围，当当前页面已经为新对话的情况，禁止继续新建对话。
- 修复注册提醒 "验证 ID 不能为空" 的问题。
- 后台显示设置添加 `显示全局水印` 选项，开启后将在对话页显示用户名水印。
- 后台插件显示-基础显示，新增 `隐藏插件` 选项，开启后，用户端将隐藏插件功能。
- 内置插件参数不在支持列表内，将以插件参数作为模型，调用对应模型。
- 继续优化调整暗黑模式 UI。

## 开发版 v3.7.4

- 精简一些未使用的依赖（更新后建议重新安装下依赖）。
- 修复新版易支付 `mapi` 接口不支持 get 调用的问题。
- 取消数字验证码，改为滑块验证，简化注册流程。
- 修复 SunoMusic 编辑歌词后不生效及一些场景下，只显示音乐封面及标题显示的问题。
- 修复 MJ 绘画扣费未 ✖️4 的问题。
- MJ 适配 `describe` ，支持以图生文。
- 修复 MJ 因开启提示词优化出现的 链接/参数 丢失的问题（一般中转自带翻译，不用开）。
- 暗黑模式配色调整（有不少地方的颜色还有待调整，只是个初版，介意请延时升级）。
- 后台显示设置新增 隐藏首页默认预设 设置，可自行选择开启。

## 开发版 v3.7.3

- 修复 `gpts` 因调用次数计算错误导致的无法调用的问题。
- 调整 `gpts` 上传控制显示。
- 修复使用应用，图标无法实时加载的问题。
- 修复用户对话宽度超出的问题。

## 开发版 v3.7.2

- 本地存储新增一些文件安全性检测。
- 调整购买套餐弹窗样式。
- 修复百度统计 token 过期自动刷新不正常的问题。
- 优化后端日志显示，新增 MJ 任务提交错误反馈。
- 修复绘画页区域重绘无效的问题。
- 开启本地存储后，默认将生成的视频及音乐也保存到本地。
- 修复应用弹窗标题自动截断导致的显示不全的问题（需自行控制标题长度，太长会造成显示错乱）。
- 当模型不存在时，将自动切换至全局模型，不再提醒"当前调用模型的 key 未找到"。
- 后台新增 `gpt-4o-mini` 模型。
- 调整内置的文件分析逻辑，选择后对于非图片的文本格式，将读取文件内容，作为 system 传给 AI，图片格式按 4o 格式传图。增加容错截断机制，不再依赖外部 API，读取速度更快。（建议搭配 token 关联计费及 `gpt-4o-mini` 使用）

## 开发版 v3.7.1

- 后台新增本地存储（开启后将优先使用本地存储方式保存数据，有些场景需开启跨域访问，可能需额外自行解决读写权限问题。
  文件存储目录为 `/public/file`，更新迁移时请做好数据维护及备份。）
- 开启插件的时，左上角的名称及使用的积分类型将根据插件类型实时调整实时变动。
- 修复无法使用第三方自定义插件的问题。
- 对话页应用广场改为弹窗方式，右上角新增关闭按钮，点击标题直接回到对话页。
- 新增更多的预设提问（如果预设中出现中出现一些不合适的问题，或者图标不显示的问题，可在群内反馈）。
- 修复签到页星期显示，改为中文。
- 微调一些 UI 显示。
- 将图形验证改为纯数字形式。
- 修复生成标题失败导致的程序崩溃问题。
- 调整后台模型频率显示设置限制。

## 开发版 v3.7.0

- 实现简易版的知识库问答【数据管理】-【内容预设】。会通过检测提问关键词，将匹配到的内容附加到 `system` 参数中。
- 参考官网，实现问题编辑及答案重新生成。
- 修复一些显示问题。

## 开发版 v3.6.4

- 环境变量新增自定义后台路径设置。
- 修复删除对话后，对话显示错位的问题。
- 修复表格、代码块显示超宽的问题。

## 开发版 v3.6.3

- 用户端公告框架改为后台相同框架，后台编辑预览的效果即为用户看到的。
- 修复后台绘画记录图片无法点开的问题。
- 修复打开后台会跳转到对话页的问题（有旧版缓存的依然需要清空一次）。
- 修复手机号注册不受后台控制的问题。
- 修复模型排序无法通过输入编辑的问题。
- 修复默认的 AI 头像显示错位的问题。

## 开发版 v3.6.2

- 尝试修复回复 400 错误。（未测试）
- 调整移动端 HTML 编辑预览逻辑。（注意：有些浏览器不支持预览）
- 新增 AI 回复中，禁止切换、新建对话的限制。
- 修复一些对话显示问题。

## 开发版 v3.6.0

- 参考 ChatGPT 官网界面，将对话调整为左右分布。
- 新增 HTML 代码预览编辑弹窗。
- 适配 LumaAPI，支持文生视频、图生视频功能。需添加插件（luma-video）及创意模型（luma-video）。(生成时间会比较久，模拟了进度百分比，设置模型的时候，注意调高超时时间)
- Suno 音乐编辑新增风格选择。
- 优化思维导图编辑弹窗的呼出及移动端端显示。
- 优化回复中暂停功能。
- 统一管理端界面及说明，优化设置分类，精简一些不需要的配置。将不再维护的其他页面，移动到其他设置。
- 管理端新增：【隐藏朗读按钮】、【绘画记录（仅显示对话页绘画记录）】、【图标重新上传】。
- 调整存储桶文件存放目录，区分`userFiles`、`images`、`midjourney`、`system`、`others`等目录，一些目录下，按时间顺序生成子目录，方便统一管理。
- 将验证码调整为纯字母/数字方案。

## 开发版 v3.5.2

- 修复对话消息显示问题，升级需重新安装依赖。（浏览器的缓存可能导致不会及时生效，可以换设备或者无痕浏览测试确认）
- 微信多域名回调保持当前环境变量方式，因回调系统导致的差异需要去检索指定的环境变量值，自行调整域名。

## 开发版 v3.5.1

请仔细阅读升级说明，升级前前请做好数据库备份

- 新增更多的内置对话预设，剔除一些显示不正常的图标。
- 对话显示公式渲染适配 `/[ /]`、`/( /)` 格式。
- `插件应用`-`基础设置` 新增 `插件优先显示` 选项，可以自由调整对话页插件、对话显示的优先级。
- 精简创意模型、特殊模型中不需要的配置项目。
- 压缩分割用户端 `JS` 文件，提升用户端加载速度。
- 修复管理端模型类型显示错误问题。
- 修复专业绘画页积分扣除问题。
- 修复提问后输入框不回弹的问题。

## 开发版 v3.5.0

请仔细阅读升级说明，升级前前请做好数据库备份，跨版本降级可能会丢失数据

- 优化首页"九宫格"显示（不再依赖后端配置，目前前端预制了 50 个左右的提问，后续会加上更多）
- 适配`luma-video`模型，初步实现文生视频功能，需添加创意模型，然后在插件中添加对应插件，模型，参数均为`luma-video`。（等待时间会比较久，图生视频即将适配）
- 头像点击展开更多处，新增退出登录
- 网页端对话页新增思维导图编辑功能（当前功能比较基础，后续会继续优化）
- 恢复免验证注册功能（忘记密码和这个有一定的冲突，所以暂时隐藏了，下个版本兼容上）
- 升级管理端框架，调整 UI 显示（node 16 版本不确定是否兼容，使用 node 16 的小伙伴请谨慎升级）
- 管理端模型管理处，默认增加了`claude-3-5-sonnet-20240620`等模型等选择（次处可自由填选）
- 管理端百度统计行增自动更新 AccessToken 功能（未完全测试，次处需设置 ApiKey、SecretKey 等新增内容）
- 后端`.env`新增自定义微信 URL 的支持，默认无需调整，使用微信多域名回调请参考`.env.example`中格式自行修改对应 URL
- 修复下拉到底部按钮，在初始对话也会出现的问题
- 修复新建应用，对话页还是显示在当前对话的问题（未完全测试，如果依然出现请反馈）
- 修复后台账户明细 userId 显示错误的问题
- 重构优化后端代码

## 开发版 v3.4.2

- 新增模型自定义图标及介绍
- 优化上传逻辑，避免同名文件

## 开发版 v3.4.1

- 适配 SunoAPI
- 修复了一些东西，具体的忘记了

## 开发版 v3.4.0

- 后台-系统管理-基础配置 新增 【隐藏侧边菜单】 选项，开启隐藏后用户端将不显示侧边菜单
- 后台-套餐管理-积分显示 新增【积分显示设置】，可自定义是否显示各种积分以及自定义名称
- 后台-模型管理-模型全局配置 新增 【继承对话模型】 选项，开启后，新建对话将会继承上一次对话的模型
- 新增【插件系统】。可自定义插件名称、描述和头像用于前端显示，同时需要设置对应的插件参数。插件系统包含，内置插件和普通插件两种。
- 内置插件已支持 Suno 音乐（参数：suno-v3）、Midjourney 绘图（参数：midjourney）、Stable Diffusion 绘图（参数：stable-diffusion）、Dalle 绘画（参数：dall-e-3），均需通过创意模型配置对应模型。
- 普通插件需要外部插件系统支持，具体参数请查看插件系统（<https://github.com/vastxie/99AIPlugin>）文档。需在 插件应用-基础设置 中配置外部插件的地址和 key
- 修复了一些小特性

## 开发版 v3.3.5

- 后台-模型管理-模型全局配置新增 Temperature 设置（不知道干啥用的不用管）
- 提问输入框新增通过剪切板粘贴图片，文件功能
- 支持通过环境变量（.env）设置搜索功能的反代 URL

## 开发版 v3.3.4

- 重构联网插件，支持本地搜索，优先使用 bing 搜索，然后是 google/duckduckgo，支持打开网页，深度提取搜索内容。（google/duckduckgo 国内服务器无法直接使用）
- 修复文件分析无法使用的问题
- 后台可显示当前版本号

## 开发版 v3.3.3

v3.3.2

- 优化对话适配，提高适配范围
- 修复对话页 mj 绘画上传图片不显示的问题
- 修复反代后后无法正常获取用户登录 IP 的问题

## 开发版 v3.3.2

v3.3.3

- 合并多操作到左下角头像区域
- 修复头像被覆盖到问题
- 微调插件/对话/新建对话布局
- 修复一些显示问题

v3.3.2

- 小小更新一个，下个版本将支持开关侧边选择的选项
- 插件自定义也下个版本

## 开发版 v3.3.1

- 新增插件系统
- 新增 @ 调用应用功能

- 此版本为尝鲜版，上个版本的很多小问题也还没修复，请按需升级

## 开发版 v3.3.0

- 修复通过应用广场新建应用时出现的显示错误
- 优化一些显示上的小问题
- 修复 Token 关联计费计算导致的在特殊情况下不扣费的问题
- 修复版本号过低的重大 bug

- 所有模型都建议开启，基于 token 计费。扣除方式为：扣除的积分 = 单次扣除金额 \_（token 消耗 / token 计费比例）结果向上取整【例如开启 token 计费，单次扣除金额为 3 积分，token 计费比例为 1000，用户调用消耗 1500 token，那么扣除的积分为 3 \*（1500 / 1000）向上取整 6 积分】
- 原计划的验证码登录功能，将在后面的登录/注册后台重构后再上线
- 次版本相对稳定，推荐不追求极致更新的小伙伴选择升级

## 开发版 v3.2.6

## 更新内容

- 修复登录页【扫码】【邮箱】【手机】登录的显示问题
- 修复手机号验证码无法发送/验证问题（未测试）
- 修复未登录状态下可使用卡密进行兑换（未登录状态将无法访问个人中心）
- 修复后台基础配置 上传 网站 ico 实际上传到了 用户端 LOGO
- 删除绘画页 niji 风格参数
- 旧的应用广场页也重定向到新版应用广场页
- 新增对话页 midjourney 绘图【图生图】【人脸一致性】【风格一致性】选项

## 其他反馈

- 用户自定义应用功能丢失（此功能已移除）
- midjourney 的模型 key 设为不启用，绘画时仍然会被调用（关闭用户端显示将在用户端隐藏模型、但不会影响后台的调用、完全不用可删除）
- 手机端没有应用情况无法进入对话界面（反馈不明确）
- 公告那里设置 mermaid 图（饼图，流程图等）后台可以显示出来，然后前台就是黑色的，显示不出来（可以的话私我具体的代码）
- 选择应用后，默认是其他的聊天记录而不是该应用的（旧版本会有，理论上新的修复了）

## 开发版 v3.2.5

- 对话页 midjourney 新增尺寸选择及风格选择
- 调整登录/注册页 UI 显示，合并手机/邮箱验证码
- 精简优化后台邮箱配置，新增网页链接选项，用于发信显示
- 手机/邮箱验证统一用同一个验证码
- 修复兼容微信支付二维码显示问题

## 说明

- 登录/注册页选项（用户协议/忘记密码）只是放了个文字，实际功能及配置下个版本再加
- UI 调整先大致整一个，后面优化

## 开发版 v3.2.4

- 修复主页九宫格应用点击会新建两条对话的问题
- 修复上下文错乱问题
- 修复专业绘画页无法使用全局模型绘画问题

- 本次更新作为稳定版的前置版本，强烈推荐升级，上下文错乱比较影响用户体验

## 开发版 v3.2.3

- 解决了侧边应用偶尔无法点击的问题。
- 修复了主页九宫格应用点击不直接新建对话的问题。
- 解决了自动回复功能无效的问题。
- 修复了在对话界面执行删除操作后没有反应的问题。
- 修复了未开启微信登录设置时，移动端仍显示绑定微信文字入口的问题，现已实现根据设置自动显示/隐藏。
- 解决了对话页面未开启签到功能却显示签到入口的问题，签到入口现将根据功能开启状态自动显示/隐藏。
- 修复了模型设置中 mj 选择绘画积分，但在列表中错误显示为高级积分的问题。
- 将邮箱配置内容迁移到后台配置中。
- 对话回复新增错误记录和反馈。

- 本次更新主要针对问题修复，需要在后台配置-用户管理-邮件登录配置-SMTP 服务器配置中，重新填写邮箱发件配置。

## 开发版 v3.2.2

- 模型配置新增【文件上传-文件分析】选项默认不再全模型开启文件分析
- 创意模型现已针对以下模型特别优化【dalle-e-3】（OpenAI 格式）【midjourney】（Mj-Plus 格式）【stable-diffusion】【suno】（https//:api.lightai.io 中转格式）
- 新增应用收藏功能，收藏的应用将显示在侧边栏的，最新的对话将显示在最前面。（侧边栏对于应用的取消收藏及更多下次再加）
- 不再限制一个应用只能开启一个对话
- 侧边栏添加公告栏触发按钮
- 优化 AI 音乐（suno-v3）的匹配范围，匹配结果更精准
- 调整应用广场显示密度以及文字细节，显示更和谐
- 修复一些问题，优化逻辑

- 本地存储以及对话页思维导图插件暂未完全优化好，下个版本再更
- 升级使用中有任何问题欢迎在群内反馈

## 开发版 v3.2.1

- 适配文生音乐，测试版，仅供娱乐
- 适配 sd 绘图——dalle 的平替
- 修复推广页注册用户最后登录时间显示问题
- 修复升级可能出现的应用广场图片丢失问题（本次升级 100% 丢失，所以务必备份好再导入）
- 修改对话的等待动画，参考 ChatGPT 官网
- 重构流式前后端对话缓存及处理逻辑，可能大概或许会流畅一些
- 调整文档显示 UI，显示更美观

- 接下来会重构梳理下后台逻辑，已经完成一小部份，使用中遇到任何问题欢迎群内反馈

## 开发版 v3.1.4

- 修复手机绘图"绘制中"显示问题
- 修复图片上传中的一些问题（已测试 3 种方式均可使用，如果在用不了就是设置的问题，使用区域重绘需开启跨域）
- 新增联网搜索模式
- 新增全模型文件分析功能（只支持带文字的 pdf，word，ppt，txt，md 等文件）
- 全新设计的应用中心，未设置头像将自动使用应用名称作为头像
- 联网及模型分析均内置了链接，属于临时方案，后续会支持填写自己的链接
- 文件分析功能会消耗较多的 tokens，并且没有单独去考虑单独计费，属于尝鲜版本
- 本次更新内容较多，使用过程中遇到 bug，可在群内反馈

## 开发版 v3.1.3

- 模型配置新增模型频率限制选择，可控制单用户每小时该模型最大调用频率
- 修复微信静默登录失效的问题
- 初步重构应用逻辑，特殊模型中无需配置翻译及导图模型，移至[模型管理-系统应用]中

因为应用这块儿有不少不稳定的情况，代重构修复后版本号再升到 3.2.0

## 开发版 v3.1.2

- 重新排版后台管理菜单，精简一些不需要的配置
- 删除旧的 dalle 绘画页
- 对话页新增 dalle 绘画风格选择
- 应用固定模型可使用绘图模型
- 修复绘画模式，积分不自动刷新的问题
- GPTs 或固定模型的时候固定模型切换按钮，积分显示对应模型的消耗
- 修复使用提交按钮，输入框高度不自动回弹的问题
- 修复 Midjourney 绘画提交文字显示错误
- 修复专业绘画页，无法调用全局配置绘画的问题

应用这块的客户端模型及积分显示均为较临时方案，有 bug 及时反馈，后面会重构逻辑

## 开发版 v3.1.1

- 调整输入框样式及上传文件预览位置，支持自动换行，视觉观感更简约
- 新增 dall-e 模型绘画比例 （方形/宽屏/垂直）选择
- 新增 dall-e 连续绘画模式，可通过对话调整绘画内容（模型管理-模型全局配置-Dalle 绘图配置）
- 新增蓝兔支付，由@づ淡然萌え微笑 提供（未测试）
- 修复修改 key 后绘画页区域重绘无法使用的问题
- 修复邀请记录显示问题
- 修复后台下拉框宽度显示不正常的问题（需重新 pnpm install）

## 开发版 v3.1.0

- 新增 GPTs 适配，修复对话时，不显示应用名称的问题
- 新增对话页绘画进度反馈
- 重构对话页绘画逻辑，提升绘画成功率（TODO： 绘画失败退还积分）
- 修复过对话页 Midjourney 绘画扣费不会 ✖️4 的问题
- 后台新增自选是否开启 mj 翻译（todo，检测到有中文才翻译）
- 后台新建应用时可选是否固定模型，可搭配外部知识库 API 使用
- 新增模型配置时可选是否开启文件上传模块（区分 ALL/4V 格式）
- 适配 ALL 模型及 GPTs 返回的图片，解决图片显示过大的问题
- 修改全局字体，优先使用系统默认字体

## 开发版 v3.0.4

- 修复高级模型及访客模式可能不扣费的问题
- 修复 key 占用报未配置模型的问题
- 修复对话组中第一条对话无法重新生成的问题，调整对话重新生成问题
- 修复在对话页中，点击 Midjourney 不会自动跳转到 Midjourney 模型的问题
- 加回对话删除功能（有些模型在用户和 AI 对话非完整成组的时候会报错，遇见后请及时反馈，后续会针对性优化）

- DALL-E 绘画页依旧不可用，对话页可用（并且暂无修复计划）
- 后台务必配置全局代理地址、key 及模型，当前对话页的 DALL-E 回复反馈和标题生成依赖此配置

### 开发计划

- 除对话页，其他页面后续不太会维护了，绘画附加参数，思维导图功能也会逐渐迁移到对话页
- 待功能迁移的差不多后，会默认关闭侧边栏页面切换栏，可后台自行开启
- GPTs 及数据库相关功能会在前两项完成之后才会继续开发

## 开发版 v3.0.3

- 修复了一些 bug
- 新增根据用户提问内容，自动生成标题功能

- 由于数据库兼容性性问题，DALL-E 绘画页绘图暂不可以，后续会将其整合进专业绘画中

## 开发版 v3.0.2

- 模型配置均迁移到模型管理中，分为普通对话｜绘画模型｜特殊模型
- 模型配置支持选择模型扣费类型：普通积分｜高级积分｜绘画积分
- 通过模型配置，可以自定义修改 Dall-e 及 Midjourney 使用的 key 及扣费类型和扣费量
- 使用 TTS 实现对话语音播报（在特殊模型中配置后生效）
- 特殊模型中支持单独配置思维导图及翻译用的 key 及模型（模型样式为功能-模型，例如思维导图使用 gpt-3.5-turbo 即为 mindmap-gpt-3.5-turbo 格式）
- 新增 claude-3.0 模型的选择

- 本次更新对模型选择有较大变动，特别是旧版的 Midjourney 配置已不再生效，需及时在模型管理中新增对应绘画模型

## 开发版 v3.0.1

## 更新说明

### 功能优化

- 后台对话容错加强，区分失败及停止（初步完成，需进一步具体化）
- 新建对话检测登录状态，未登录弹出登录框

### 用户界面调整

- 调整网页字体大小及 markdown 代码块显示风格（统一暗色）
- 对话页手机端操作按钮布局异常修复
- 手机端对话右侧宽度调整

### Bug 修复

- 修复超时配置无效的 bug
- 修复 model 数据库缺少 timeout

### 文件管理优化

- MJ 存储图片的时候，不根据链接来获取文件名，改成日期+任务 ID 形式

## 开发版 v3.0.0

- 重构 Midjourney 绘画后端逻辑，新增区域重绘功能（当前仅专业绘画页面支持）
- 对话中支持调用 Dalle 及 Midjourney 模型进行绘画（需在后台配置对应模型）
- 精简 chat 页 UI，参考 ChatGPT 官网布局，美化页面
- 根据模型自动显示 AI 头像，可在整合包中自定义替换
- 重构对话上下文的获取方式，允许跨模型连续对话
- 新增全局请求地址及 Key，免除同一 key 重复填写（次配置为必选配置，翻译及导图依赖次 key）
- 可全局设置 midjourney，是否存储图片，详细配置见管理端后台
- 后台模型设置区分基础对话及绘画模型
- 后台支持手动修改用户激活状态
- midjourney 的 key 及地址可从 [中转](https://api.lightiai.io) 获取或自建 midjourney-proxy-plus

- 区分绘画类型，支持自定义绘画积分，加强绘画的容错机制，绘制失败不扣除积分（当前版本失败也会扣除对应积分）等功能持续开发中
- 除 chat 对话页外的其他页面后续会逐渐停止更新及维护
- 新版数据库有较大变动，升级前请务必做好备份，防止数据丢失
- 请自行斟酌后选择是否升级，使用中问题可及时在群内反馈

## 开发版 v2.5.2

### 功能更新

1. **修复界面问题**：

   - 解决了 DALL-E 页面绘图不显示的问题，优化用户体验。
   - 修复了 Chat 页面绘图功能需要刷新页面才能显示的问题，提高使用便捷性。

2. **优化中文释义**：

   - 对页面中的中文释义进行了调整，使其更准确、易懂。

3. **第三方中转推荐**：
   - 推荐使用 <https://api.lightai.io> 作为第三方中转平台，以便于用户更方便地使用服务。

- 本次升级 Midjourney 对历史数据不兼容，升级前请做好备份准备。这是为了确保在进行升级过程中，用户的数据安全和服务的连续性不受影响。

本次更新旨在提升用户体验，解决已知问题，并持续优化产品功能。我们非常重视用户反馈，欢迎大家使用新版本并分享使用感受。

## 开发版 v2.5.1

1. **mj-proxy-plus 支持更新**：

   - 新增容错和重试机制，提高稳定性。

2. **模型新增排序功能**：

   - 优化模型排序逻辑，提升用户体验。

3. **精简 mj 模型配置**：

   - 后台配置现仅需地址和 key，简化操作流程。

4. **dall-e 绘画整合进 chat**：

   - dall-e-3 模型可在后台单独配置。
   - 计划废弃现有的 dall-e-3 页面，绘图功能将纳入 chat 组件。
   - 连续绘图功能开发中。

5. **文件类型支持扩展**：

   - all 模型除 pdf 外，增加多种文件类型支持。

6. **国产模型兼容性修复**：

   - 修复了国产模型添加后无法使用的 bug。

7. **界面显示优化**：
   - 修复后台及绘画广场的显示问题。

- 由于本次 mj-proxy-plus 升级不向下兼容，建议删除数据库中旧的 mj 数据库。
- 新的 key 可以通过中转平台购买。
- 如果您之前订阅过，但不想自建 mj-proxy-plus，可以考虑共享账号给我们，以合组账号池。

- **SUNO 4.5 模型适配**：新增对 SUNO 4.5 版本的完整支持，显著增强 AI 音乐生成能力，提供更高质量的音乐创作体验和更丰富的音乐风格选择。
- **图片尺寸选择**：图片编辑预览区域新增尺寸选择功能，支持自定义输出尺寸，满足不同场景需求。
- **输入框全屏模式**：新增全屏编辑按钮，切换后将最大化可用编辑空间，显著提升长文本编辑和预览体验。
- **模型思考类型配置**：在管理端模型设置中新增"模型思考类型"选项，管理员可为每个模型单独配置推理方式，包括直接输出模式（不进行额外思考推理，快速响应）、全局推理模式（使用全局统一的思考推理逻辑）、高级推理模式（运用模型本身的高级推理能力）。
- **Claude-4 模型支持**：后台模型管理新增 Claude-4 系列模型选项，扩展可用 AI 模型范围，为用户提供更多高性能的对话模型选择。
- **嘟噜支付集成**：后台支付系统新增"嘟噜支付"作为新的支付方式选项，为用户提供更多便捷的支付渠道。

- **后台应用列表分页修复**：修复后台管理界面中应用列表分页功能无法正常切换选择的问题，确保管理员能够顺畅浏览和管理所有应用。
