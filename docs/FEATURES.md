# 99AI 详细功能介绍

本文档详细介绍 99AI 平台的核心功能和特色，配合截图展示实际使用效果。

## 💬 AI 对话

支持 OpenAI Chat 格式，后台可自定义模型名称、头像、介绍、代理、key、积分扣除方式、文件上传模式等参数。

<img width="1444" alt="AI对话界面" src="https://github.com/vastxie/99AI/assets/24899308/dd373bec-674e-4a6c-8304-f6abd1a65e1e">

## 🖼️ 多模态模型

使用`gpt-4o`、`claude-3`等视觉模型，或`gpt-4-all`等逆向模型，完成图像、文件的识别分析。

<img width="1443" alt="多模态模型分析" src="https://github.com/vastxie/99AI/assets/24899308/e1f1ed62-97e5-4412-9b72-5916d2337fdc">

## 📄 全模型文件分析

<img width="1444" alt="文件分析功能" src="https://github.com/vastxie/99AI/assets/24899308/eedfd9fd-f3f3-4a37-8a82-b73e135e8dfe">

## 💻 代码预览

`HTML` 代码的预览与编辑。

<img width="1444" alt="代码预览功能1" src="https://github.com/vastxie/99AI/assets/24899308/d7860dbf-0897-4f26-9d70-d304a270c05a">

<img width="1444" alt="代码预览功能2" src="https://github.com/vastxie/99AI/assets/24899308/13e558fa-62a8-4fff-9e2b-c4820acefbc4">

## 🔍 联网搜索

对接[插件系统](https://github.com/vastxie/99AIPlugin)，拓展 AI 功能边界。

<img width="1444" alt="联网搜索功能" src="https://github.com/vastxie/99AI/assets/24899308/6ad1bcbb-ac6b-4478-9d91-9ae8b71afa64">

## 🗺️ 思维导图

<img width="1444" alt="思维导图功能1" src="https://github.com/vastxie/99AI/assets/24899308/d6eb861d-2e26-415a-acf6-e2d44fc29620">

<img width="1445" alt="思维导图功能2" src="https://github.com/vastxie/99AI/assets/24899308/61f1b059-1eab-428a-91a7-3015ffdac970">

## 🎨 AI 绘画

对接 `midjourney` 、`dall-e`、`stable-diffusion` 等绘画模型。

<img width="1444" alt="AI绘画功能1" src="https://github.com/vastxie/99AI/assets/24899308/39728c39-ed98-4d77-bee8-f7548c5f4a28">

<img width="1444" alt="AI绘画功能2" src="https://github.com/vastxie/99AI/assets/24899308/4a70785d-bf66-49e2-a822-5c91a68bd667">

## 🎵 AI 音乐

对接 `suno-music` 完成音乐创作。

<img width="1445" alt="AI音乐功能1" src="https://github.com/vastxie/99AI/assets/24899308/a2e42201-fad7-4498-9fb0-c107fcc2f683">

<img width="1446" alt="AI音乐功能2" src="https://github.com/vastxie/99AI/assets/24899308/62e466d0-7866-4efb-b28e-03f6b088d043">

## 🎬 AI 视频

对接 `luma-video` 文生视频。

<img width="1446" alt="AI视频功能1" src="https://github.com/vastxie/99AI/assets/24899308/365cc93e-6fc0-4107-ac4c-6b25f31f0f12">

<img width="1443" alt="AI视频功能2" src="https://github.com/vastxie/99AI/assets/24899308/734013e1-273b-4655-b18e-a8f138165130">

## 📚 知识库预设

<img width="1446" alt="知识库预设功能1" src="https://github.com/vastxie/99AI/assets/24899308/abe7fe07-535f-43cc-8bc5-5e49eb6271cf">

<img width="1442" alt="知识库预设功能2" src="https://github.com/vastxie/99AI/assets/24899308/330be2fc-db83-446c-8518-d97f85ef0ec4">

<img width="1443" alt="知识库预设功能3" src="https://github.com/vastxie/99AI/assets/24899308/a341dc07-cd83-4594-bff7-c5b784f41eb1">
