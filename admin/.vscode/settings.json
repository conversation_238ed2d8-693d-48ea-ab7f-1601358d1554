{"eslint.useFlatConfig": false, "prettier.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "json", "jsonc", "json5", "yaml", "yml", "markdown"], "editor.defaultFormatter": "dbaeumer.vscode-eslint", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[css]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[scss]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "cSpell.words": ["ant<PERSON>", "axios", "Baichuan", "bumpp", "Chatbox", "chatglm", "chatgpt", "chenz<PERSON>yu", "chevereto", "cogvideox", "commitlint", "<PERSON><PERSON><PERSON>", "cref", "dall", "dalle", "<PERSON><PERSON><PERSON>", "deepsearch", "deepseek", "docker<PERSON>b", "do<PERSON>o", "duckduck<PERSON>", "<PERSON><PERSON>", "EMAILCODE", "Epay", "errmsg", "esno", "flowith", "GPTAPI", "gpts", "headlessui", "highlightjs", "hljs", "hun<PERSON>", "iconify", "<PERSON><PERSON><PERSON>", "ISDEV", "katex", "ka<PERSON><PERSON><PERSON><PERSON>", "kontext", "langchain", "<PERSON><PERSON>", "linkify", "logprobs", "longcontext", "luma", "mapi", "Markmap", "mdhljs", "micromessenger", "mila", "Mindmap", "MODELSMAPLIST", "MODELTYPELIST", "modelvalue", "newconfig", "niji", "Nmessage", "nodata", "OPENAI", "pinia", "Popconfirm", "PPTCREATE", "projectaddress", "qwen", "rushstack", "sdxl", "<PERSON><PERSON>", "seedream", "<PERSON><PERSON>", "sref", "suno", "tailwindcss", "<PERSON><PERSON>", "traptitech", "tsup", "Typecheck", "typeorm", "unocss", "unplugin", "usercenter", "vastxie", "VITE", "vueuse", "wechat"], "vue.codeActions.enabled": false}