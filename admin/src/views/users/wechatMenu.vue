<route lang="yaml">
meta:
  title: 微信公众号菜单
</route>

<script lang="ts" setup>
  import apiConfig from '@/api/modules/config';
  import apiOfficial from '@/api/modules/official';
  import type { FormInstance, FormRules } from 'element-plus';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { onMounted, reactive, ref } from 'vue';

  interface MenuItem {
    type?: string;
    name: string;
    key?: string;
    url?: string;
    appid?: string;
    pagepath?: string;
    media_id?: string;
    article_id?: string;
    sub_button: MenuItem[];
  }

  interface MenuForm {
    button: MenuItem[];
  }

  const menuForm = reactive<MenuForm>({
    button: [],
  });

  const formRef = ref<FormInstance>();
  const loading = ref(false);
  const wechatConfigured = ref(false);

  // 一级菜单最多三个，二级菜单最多五个
  const rules = reactive<FormRules>({
    button: [
      {
        type: 'array',
        max: 3,
        message: '一级菜单最多3个',
        trigger: 'change',
      },
    ],
  });

  // 菜单类型选项
  const menuTypes = [
    { label: '点击推事件', value: 'click' },
    { label: '跳转URL', value: 'view' },
    { label: '扫码推事件', value: 'scancode_push' },
    { label: '扫码带提示', value: 'scancode_waitmsg' },
    { label: '系统拍照发图', value: 'pic_sysphoto' },
    { label: '拍照或从相册发图', value: 'pic_photo_or_album' },
    { label: '微信相册发图', value: 'pic_weixin' },
    { label: '发送位置', value: 'location_select' },
    { label: '图片、音频、视频或图文消息', value: 'media_id' },
    { label: '图文消息URL', value: 'view_limited' },
    { label: '发布后的图文消息', value: 'article_id' },
    { label: '发布后的图文消息URL', value: 'article_view_limited' },
    { label: '小程序', value: 'miniprogram' },
  ];

  // 检查微信公众号配置是否完成
  async function checkWechatConfig() {
    try {
      const res = await apiConfig.queryConfig({
        keys: ['wechatOfficialAppId', 'wechatOfficialToken', 'wechatOfficialAppSecret'],
      });

      const { wechatOfficialAppId, wechatOfficialToken, wechatOfficialAppSecret } = res.data;
      wechatConfigured.value = !!(
        wechatOfficialAppId &&
        wechatOfficialToken &&
        wechatOfficialAppSecret
      );

      if (!wechatConfigured.value) {
        ElMessage.warning('请先完成微信公众号基本配置');
      }
    } catch (error) {
      console.error('获取微信配置失败', error);
    }
  }

  // 查询当前菜单配置
  async function queryMenu() {
    loading.value = true;
    try {
      const res = await apiOfficial.queryOfficialMenu();
      if (res.data && res.data.menu && res.data.menu.button) {
        menuForm.button = res.data.menu.button;
      }
    } catch (error) {
      console.error('获取菜单配置失败', error);
    } finally {
      loading.value = false;
    }
  }

  // 确保每个菜单项都有必要的属性
  function ensureMenuItemsHaveRequiredProps(menuItems: MenuItem[]) {
    for (const item of menuItems) {
      // 确保所有必要的属性都存在
      if (!item.key) {
        item.key = `KEY_${item.name}_${Date.now()}`;
      }

      // 确保media_id存在
      if (!item.media_id) {
        item.media_id = '';
      }

      // 确保url存在
      if (!item.url) {
        item.url = '';
      }

      // 确保appid和pagepath存在
      if (!item.appid) {
        item.appid = '';
      }

      if (!item.pagepath) {
        item.pagepath = '';
      }

      // 确保article_id存在
      if (!item.article_id) {
        item.article_id = '';
      }

      // 递归处理子菜单
      if (item.sub_button && item.sub_button.length > 0) {
        ensureMenuItemsHaveRequiredProps(item.sub_button);
      }
    }
  }

  // 保存菜单
  function saveMenu() {
    if (!wechatConfigured.value) {
      ElMessage.warning('请先完成微信公众号基本配置');
      return;
    }

    formRef.value?.validate(async (valid) => {
      if (valid) {
        loading.value = true;
        try {
          // 在保存前确保所有菜单项都有必要的属性
          ensureMenuItemsHaveRequiredProps(menuForm.button);

          await apiOfficial.createOfficialMenu(menuForm);
          ElMessage.success('自定义菜单保存成功');
          queryMenu();
        } catch (error: any) {
          console.error('保存菜单失败', error);
          if (error.response && error.response.data) {
            ElMessage.error(`保存失败: ${error.response.data.message || '未知错误'}`);
          } else {
            ElMessage.error('保存失败，请检查菜单配置');
          }
        } finally {
          loading.value = false;
        }
      } else {
        ElMessage.error('表单验证失败，请检查填写内容');
      }
    });
  }

  // 删除菜单
  function deleteMenu() {
    if (!wechatConfigured.value) {
      ElMessage.warning('请先完成微信公众号基本配置');
      return;
    }

    ElMessageBox.confirm('确定要删除所有自定义菜单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(async () => {
        loading.value = true;
        try {
          await apiOfficial.deleteOfficialMenu();
          ElMessage.success('自定义菜单删除成功');
          menuForm.button = [];
        } catch (error) {
          console.error('删除菜单失败', error);
        } finally {
          loading.value = false;
        }
      })
      .catch(() => {});
  }

  // 添加一级菜单
  function addFirstLevelMenu() {
    if (menuForm.button.length >= 3) {
      ElMessage.warning('一级菜单最多只能添加3个');
      return;
    }
    menuForm.button.push({
      name: '菜单名称',
      key: `KEY_${Date.now()}`,
      type: '',
      url: '',
      media_id: '',
      appid: '',
      pagepath: '',
      article_id: '',
      sub_button: [],
    });
  }

  // 添加二级菜单
  function addSecondLevelMenu(index: number) {
    if (menuForm.button[index].sub_button.length >= 5) {
      ElMessage.warning('二级菜单最多只能添加5个');
      return;
    }

    menuForm.button[index].sub_button.push({
      type: 'view',
      name: '子菜单名称',
      key: `KEY_${Date.now()}`,
      url: 'http://',
      media_id: '',
      appid: '',
      pagepath: '',
      article_id: '',
      sub_button: [],
    });
  }

  // 移除菜单项
  function removeMenuItem(firstIndex: number, secondIndex?: number) {
    if (secondIndex !== undefined) {
      menuForm.button[firstIndex].sub_button.splice(secondIndex, 1);
    } else {
      menuForm.button.splice(firstIndex, 1);
    }
  }

  onMounted(() => {
    checkWechatConfig();
    queryMenu();
  });
</script>

<template>
  <div>
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">微信公众号自定义菜单设置</div>
      </template>
      <template #content>
        <div class="text-sm/6">
          <div>设置微信公众号菜单，用户关注后即可查看。</div>
          <div>
            请先在<router-link to="/user/wechat">微信登录配置</router-link>完成公众号基本配置。
          </div>
          <div>一级菜单最多3个(4字)，二级菜单最多5个(8字)，超出部分显示为"..."。</div>
          <div>菜单创建后约5分钟生效，所有菜单项都需设置KEY值。</div>
        </div>
      </template>
      <div class="flex gap-2">
        <HButton outline text @click="saveMenu" :loading="loading">
          <SvgIcon name="i-ri:save-line" />
          保存菜单
        </HButton>
        <HButton outline text @click="deleteMenu" :loading="loading" type="danger">
          <SvgIcon name="i-ri:delete-bin-line" />
          删除菜单
        </HButton>
      </div>
    </PageHeader>

    <el-card style="margin: 20px">
      <el-alert
        v-if="!wechatConfigured"
        title="请先完成微信公众号基本配置"
        type="warning"
        :closable="false"
        style="margin-bottom: 20px"
      />

      <el-form ref="formRef" :model="menuForm" :rules="rules" label-width="100px">
        <div class="menu-container">
          <div class="menu-preview">
            <div class="preview-title">菜单预览</div>
            <div class="preview-content">
              <div class="button-row">
                <div v-for="(item, index) in menuForm.button" :key="index" class="menu-button">
                  {{ item.name }}
                </div>
              </div>
            </div>
          </div>

          <div class="menu-editor">
            <div class="editor-title">
              <span>菜单设置</span>
              <el-button type="primary" size="small" @click="addFirstLevelMenu" plain>
                添加一级菜单
              </el-button>
            </div>

            <div v-if="menuForm.button.length === 0" class="no-menu">
              暂无菜单，请点击"添加一级菜单"按钮创建
            </div>

            <el-collapse v-else accordion>
              <el-collapse-item
                v-for="(firstItem, firstIndex) in menuForm.button"
                :key="firstIndex"
                :title="`${firstItem.name || '未命名菜单'}`"
              >
                <div class="menu-item">
                  <el-form-item :label="'菜单名称'" :prop="`button.${firstIndex}.name`">
                    <el-input v-model="firstItem.name" placeholder="一级菜单名称" maxlength="16" />
                  </el-form-item>

                  <el-form-item :label="'菜单KEY'" :prop="`button.${firstIndex}.key`">
                    <el-input v-model="firstItem.key" placeholder="菜单KEY值" />
                  </el-form-item>

                  <div v-if="firstItem.sub_button && firstItem.sub_button.length === 0">
                    <el-form-item :label="'菜单类型'" :prop="`button.${firstIndex}.type`">
                      <el-select v-model="firstItem.type" placeholder="请选择菜单类型">
                        <el-option
                          v-for="type in menuTypes"
                          :key="type.value"
                          :label="type.label"
                          :value="type.value"
                        />
                      </el-select>
                    </el-form-item>

                    <el-form-item
                      v-if="firstItem.type && ['view', 'miniprogram'].includes(firstItem.type)"
                      :label="'链接地址'"
                      :prop="`button.${firstIndex}.url`"
                    >
                      <el-input v-model="firstItem.url" placeholder="跳转链接" />
                    </el-form-item>

                    <template v-if="firstItem.type === 'miniprogram'">
                      <el-form-item :label="'小程序appid'" :prop="`button.${firstIndex}.appid`">
                        <el-input v-model="firstItem.appid" placeholder="小程序appid" />
                      </el-form-item>

                      <el-form-item :label="'小程序页面'" :prop="`button.${firstIndex}.pagepath`">
                        <el-input v-model="firstItem.pagepath" placeholder="小程序页面路径" />
                      </el-form-item>
                    </template>

                    <el-form-item
                      v-if="firstItem.type && ['media_id', 'view_limited'].includes(firstItem.type)"
                      :label="'素材ID'"
                      :prop="`button.${firstIndex}.media_id`"
                    >
                      <el-input v-model="firstItem.media_id" placeholder="永久素材media_id" />
                    </el-form-item>

                    <el-form-item
                      v-if="
                        firstItem.type &&
                        ['article_id', 'article_view_limited'].includes(firstItem.type)
                      "
                      :label="'图文ID'"
                      :prop="`button.${firstIndex}.article_id`"
                    >
                      <el-input
                        v-model="firstItem.article_id"
                        placeholder="发布后获得的article_id"
                      />
                    </el-form-item>
                  </div>

                  <div class="sub-menu-container">
                    <div class="sub-menu-header">
                      <span>子菜单列表</span>
                      <el-button
                        type="primary"
                        size="small"
                        @click="addSecondLevelMenu(firstIndex)"
                        plain
                      >
                        添加子菜单
                      </el-button>
                    </div>

                    <div v-if="firstItem.sub_button && firstItem.sub_button.length > 0">
                      <el-collapse accordion>
                        <el-collapse-item
                          v-for="(secondItem, secondIndex) in firstItem.sub_button"
                          :key="secondIndex"
                          :title="`${secondItem.name || '未命名子菜单'}`"
                        >
                          <div class="menu-item">
                            <el-form-item
                              :label="'子菜单名称'"
                              :prop="`button.${firstIndex}.sub_button.${secondIndex}.name`"
                            >
                              <el-input
                                v-model="secondItem.name"
                                placeholder="子菜单名称"
                                maxlength="60"
                              />
                            </el-form-item>

                            <el-form-item
                              :label="'菜单KEY'"
                              :prop="`button.${firstIndex}.sub_button.${secondIndex}.key`"
                            >
                              <el-input v-model="secondItem.key" placeholder="菜单KEY值" />
                            </el-form-item>

                            <el-form-item
                              :label="'菜单类型'"
                              :prop="`button.${firstIndex}.sub_button.${secondIndex}.type`"
                            >
                              <el-select v-model="secondItem.type" placeholder="请选择菜单类型">
                                <el-option
                                  v-for="type in menuTypes"
                                  :key="type.value"
                                  :label="type.label"
                                  :value="type.value"
                                />
                              </el-select>
                            </el-form-item>

                            <el-form-item
                              v-if="
                                secondItem.type && ['view', 'miniprogram'].includes(secondItem.type)
                              "
                              :label="'链接地址'"
                              :prop="`button.${firstIndex}.sub_button.${secondIndex}.url`"
                            >
                              <el-input v-model="secondItem.url" placeholder="跳转链接" />
                            </el-form-item>

                            <template v-if="secondItem.type === 'miniprogram'">
                              <el-form-item
                                :label="'小程序appid'"
                                :prop="`button.${firstIndex}.sub_button.${secondIndex}.appid`"
                              >
                                <el-input v-model="secondItem.appid" placeholder="小程序appid" />
                              </el-form-item>

                              <el-form-item
                                :label="'小程序页面'"
                                :prop="`button.${firstIndex}.sub_button.${secondIndex}.pagepath`"
                              >
                                <el-input
                                  v-model="secondItem.pagepath"
                                  placeholder="小程序页面路径"
                                />
                              </el-form-item>
                            </template>

                            <el-form-item
                              v-if="
                                secondItem.type &&
                                ['media_id', 'view_limited'].includes(secondItem.type)
                              "
                              :label="'素材ID'"
                              :prop="`button.${firstIndex}.sub_button.${secondIndex}.media_id`"
                            >
                              <el-input
                                v-model="secondItem.media_id"
                                placeholder="永久素材media_id"
                              />
                            </el-form-item>

                            <el-form-item
                              v-if="
                                secondItem.type &&
                                ['article_id', 'article_view_limited'].includes(secondItem.type)
                              "
                              :label="'图文ID'"
                              :prop="`button.${firstIndex}.sub_button.${secondIndex}.article_id`"
                            >
                              <el-input
                                v-model="secondItem.article_id"
                                placeholder="发布后获得的article_id"
                              />
                            </el-form-item>

                            <div class="action-buttons">
                              <el-button
                                type="danger"
                                size="small"
                                @click.stop="removeMenuItem(firstIndex, secondIndex)"
                                plain
                              >
                                删除子菜单
                              </el-button>
                            </div>
                          </div>
                        </el-collapse-item>
                      </el-collapse>
                    </div>

                    <div v-else class="no-sub-menu">
                      暂无子菜单，可点击"添加子菜单"按钮创建，或设置当前一级菜单属性
                    </div>
                  </div>

                  <div class="action-buttons">
                    <el-button
                      type="danger"
                      size="small"
                      @click.stop="removeMenuItem(firstIndex)"
                      plain
                    >
                      删除此菜单
                    </el-button>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<style scoped>
  .menu-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
  }

  .menu-preview {
    padding: 15px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #f8f9fa;
    width: 30%;
    position: sticky;
    top: 20px;
    align-self: flex-start;
    height: fit-content;
  }

  .preview-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #606266;
  }

  .preview-content {
    background-color: #f2f6fc;
    padding: 20px 10px 10px;
    border-radius: 4px;
    min-height: 300px;
    position: relative;
  }

  .button-row {
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    border-top: 1px solid #dcdfe6;
  }

  .menu-button {
    flex: 1;
    text-align: center;
    padding: 8px 0;
    background-color: #fff;
    border-right: 1px solid #dcdfe6;
    font-size: 14px;
    cursor: pointer;
  }

  .menu-button:last-child {
    border-right: none;
  }

  .menu-editor {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 15px;
    flex: 1;
  }

  .editor-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-weight: bold;
    color: #606266;
  }

  .menu-item {
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .sub-menu-container {
    margin-top: 15px;
    padding: 10px;
    border: 1px dashed #dcdfe6;
    border-radius: 4px;
  }

  .sub-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  .action-buttons {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }

  .no-menu,
  .no-sub-menu {
    padding: 20px;
    text-align: center;
    color: #909399;
    background-color: #f8f9fa;
    border-radius: 4px;
  }
</style>
