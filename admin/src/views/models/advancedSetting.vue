<route lang="yaml">
meta:
  title: 其他设置
</route>

<script lang="ts" setup>
  import apiConfig from '@/api/modules/config';
  import { QuestionFilled } from '@element-plus/icons-vue';
  import type { FormInstance, FormRules } from 'element-plus';
  import { ElMessage } from 'element-plus';
  import { nextTick, onMounted, reactive, ref } from 'vue';

  const formInline = reactive({
    mjNotSaveImg: 0,
    mjProxyImgUrl: '',
    mjNotUseProxy: 1,
    isMjTranslate: 0,
    isConvertToBase64: 0,
    openaiVoice: '',
    drawingStyles: '',
    sideDrawingEditModel: '',
    sideDrawingEditSupportMask: 0,
  });

  type VoiceOption = { label: string; value: string };
  const voiceOptions = ref<VoiceOption[]>([
    { label: 'Alloy', value: 'alloy' },
    { label: 'Echo', value: 'echo' },
    { label: 'Fable', value: 'fable' },
    { label: 'Onyx', value: 'onyx' },
    { label: 'Nova', value: 'nova' },
    { label: 'Shimmer', value: 'shimmer' },
  ]);

  const rules = ref<FormRules>({
    isMjTranslate: [{ required: false, trigger: 'blur', message: '是否开启翻译/联想' }],
    isGeneratePromptReference: [
      { required: false, trigger: 'blur', message: '是否生成提示词参考' },
    ],
    openaiTemperature: [
      {
        required: false,
        trigger: 'blur',
        message: '请填写温度',
      },
    ],
    openaiVoice: [
      {
        required: false,
        trigger: 'blur',
        message: '请填写openai的语音音色',
      },
    ],
    isConvertToBase64: [
      {
        required: false,
        trigger: 'blur',
        message: '是否转换为base64',
      },
    ],
    sideDrawingEditModel: [
      {
        required: false,
        trigger: 'blur',
        message: '侧边绘画编辑模型方式',
      },
    ],
    sideDrawingEditSupportMask: [
      {
        required: false,
        trigger: 'blur',
        message: '编辑模型是否支持蒙版',
      },
    ],
  });
  const drawingStyleList = ref(); // 绘画风格关键词列表
  const inputVisible = ref(false); // 控制输入框的显示
  const inputValue = ref(''); // 输入框的值
  const inputRef = ref(); // 输入框的引用
  const formRef = ref<FormInstance>();

  async function queryAllconfig() {
    const res = await apiConfig.queryConfig({
      keys: [
        'openaiBaseUrl',
        'openaiBaseKey',
        'openaiTimeout',
        'openaiBaseModel',
        'openaiTemperature',
        'mjNotSaveImg',
        'mjProxyImgUrl',
        'systemPreMessage',
        'mjNotUseProxy',
        'isMjTranslate',
        'isGeneratePromptReference',
        'openaiVoice',
        'isConvertToBase64',
        'drawingStyles',
        'sideDrawingEditModel',
        'sideDrawingEditSupportMask',
      ],
    });
    if (res.data.drawingStyles) {
      drawingStyleList.value = res.data.drawingStyles.split(',');
    } else {
      drawingStyleList.value = []; // 确保drawingStyleList是一个空数组，如果没有drawingStyles数据
    }
    const {
      openaiBaseUrl = '',
      openaiBaseKey = '',
      openaiTimeout = 300,

      openaiTemperature = 1,
      isMjTranslate = 0,
      isGeneratePromptReference = 0,
      mjNotSaveImg = 0,
      mjProxyImgUrl = '',

      mjNotUseProxy = 1,

      openaiVoice = '',
      isConvertToBase64 = 0,
      sideDrawingEditModel = '',
      sideDrawingEditSupportMask = 0,
    } = res.data;
    Object.assign(formInline, {
      openaiBaseUrl,
      openaiBaseKey,
      openaiTimeout,
      isMjTranslate,
      isGeneratePromptReference,
      openaiTemperature,
      mjNotSaveImg,
      mjProxyImgUrl,
      mjNotUseProxy,
      openaiVoice,
      isConvertToBase64,
      sideDrawingEditModel,
      sideDrawingEditSupportMask: Number(sideDrawingEditSupportMask),
    });
  }

  function handlerUpdateConfig() {
    formRef.value?.validate(async (valid) => {
      if (valid) {
        try {
          await apiConfig.setConfig({ settings: fotmatSetting(formInline) });
          ElMessage.success('变更配置信息成功');
        } catch (error) {}
        queryAllconfig();
      } else {
        ElMessage.error('请填写完整信息');
      }
    });
  }

  function fotmatSetting(settings: any) {
    formInline.drawingStyles = drawingStyleList.value.join(',');
    return Object.keys(settings).map((key) => {
      return {
        configKey: key,
        configVal: settings[key],
      };
    });
  }

  // 确认输入
  function handleInputConfirm() {
    const value = inputValue.value.trim();
    if (value) {
      drawingStyleList.value.push(value);
    }
    inputVisible.value = false;
    inputValue.value = '';
  }

  // 移除关键词
  function handleStyleRemove(index: any) {
    drawingStyleList.value.splice(index, 1);
  }

  // 显示输入框
  function showInput() {
    inputVisible.value = true;
    // 等待下次 DOM 更新后聚焦输入框
    nextTick(() => {
      inputRef.value.focus();
    });
  }

  // 初始化时从字符串解析关键词列表
  function initDrawingStyles() {
    if (formInline.drawingStyles) {
      drawingStyleList.value = formInline.drawingStyles.split(',');
    }
  }

  onMounted(() => {
    queryAllconfig();
  });
</script>

<template>
  <div>
    <PageHeader>
      <template #title>
        <div class="flex items-center gap-4">其他设置</div>
      </template>
      <template #content>
        <div class="text-sm/6">
          <div>
            其他设置是对系统的一些功能进行配置，包括绘画配置、提示词优化、绘图风格显示、Base64
            识图、TTS 音色、侧边绘画编辑等，可参考说明自行配置调整。
          </div>
        </div>
      </template>
      <HButton text outline @click="handlerUpdateConfig">
        <SvgIcon name="i-ri:file-text-line" />
        保存设置
      </HButton>
    </PageHeader>
    <el-card style="margin: 20px">
      <el-form ref="formRef" :rules="rules" :model="formInline" label-width="220px">
        <h5>绘画配置</h5>
        <el-row>
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="不存储图片" prop="mjNotSaveImg" label-width="120">
              <el-switch v-model="formInline.mjNotSaveImg" active-value="1" inactive-value="0" />
              <el-tooltip class="box-item" effect="dark" placement="right">
                <template #content>
                  <div style="width: 250px">
                    默认会存储图片到配置的存储中、如果开启此选择则表示不保存原图到我们配置的存储上、直接反代访问原始图片、这样可以进一步节省空间、但访问速度及稳定性可能有所降低！
                  </div>
                </template>
                <el-icon class="ml-3 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="不使用代理" prop="mjNotUseProxy" label-width="120">
              <el-switch v-model="formInline.mjNotUseProxy" active-value="1" inactive-value="0" />
              <el-tooltip class="box-item" effect="dark" placement="right">
                <template #content>
                  <div style="width: 250px">
                    开启不使用代理将直接使用重中转获取到的链接、原生discord地址国内无法访问!
                  </div>
                </template>
                <el-icon class="ml-3 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="[0].includes(Number(formInline.mjNotUseProxy))">
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="反代地址" prop="mjProxyImgUrl" label-width="120px">
              <el-input
                v-model="formInline.mjProxyImgUrl"
                placeholder="Midjourney 反代地址，为空将直接使用原链接"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="提示词优化" prop="isMjTranslate" label-width="120">
              <el-switch v-model="formInline.isMjTranslate" active-value="1" inactive-value="0" />
              <el-tooltip class="box-item" effect="dark" placement="right">
                <template #content>
                  <div style="width: 250px">
                    开启优化后, MJ 提示词默认会使用全局模型进行翻译/联想, 不再单独扣费,
                    一般中转会自带翻译, 请根据实际情况选择。
                  </div>
                </template>
                <el-icon class="ml-3 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="编辑预览模型" prop="sideDrawingEditModel" label-width="120">
              <div style="display: flex; align-items: center; gap: 8px">
                <el-select
                  v-model="formInline.sideDrawingEditModel"
                  placeholder="请选择或输入编辑预览模型"
                  filterable
                  clearable
                  allow-create
                  style="flex: 1; width: 240px"
                >
                  <el-option label="不开启" value="" />
                  <el-option label="gpt-image-1" value="gpt-image-1" />
                  <el-option label="doubao-image" value="doubao-image" />
                  <el-option
                    label="black-forest-labs/flux-kontext-pro"
                    value="black-forest-labs/flux-kontext-pro"
                  />
                  <el-option
                    label="black-forest-labs/flux-kontext-max"
                    value="black-forest-labs/flux-kontext-max"
                  />
                </el-select>
                <el-tooltip class="box-item" effect="dark" placement="right">
                  <template #content>
                    <div style="width: 250px">
                      <p>选择侧边绘画编辑使用的模型：</p>
                      <p>• 不开启：关闭侧边全局编辑及预览功能</p>
                      <p>• 可选择预设模型或自定义输入模型名称</p>
                      <p style="color: #f56c6c; margin-top: 8px">
                        注意：需在创意模型中配置对应的模型
                      </p>
                    </div>
                  </template>
                  <el-icon class="cursor-pointer">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="编辑支持蒙版" prop="sideDrawingEditSupportMask" label-width="120">
              <div style="display: flex; align-items: center; gap: 8px">
                <el-switch
                  v-model="formInline.sideDrawingEditSupportMask"
                  :active-value="1"
                  :inactive-value="0"
                />
                <el-tooltip class="box-item" effect="dark" placement="right">
                  <template #content>
                    <div style="width: 250px">
                      <p>开启后，编辑模型将支持蒙版功能：</p>
                      <p>• 可以使用蒙版进行局部编辑</p>
                      <p>• 适用于支持蒙版功能的图像编辑模型</p>
                    </div>
                  </template>
                  <el-icon class="cursor-pointer">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row class="mt-2">
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="绘图风格显示" label-width="120" prop="drawingStyles">
              <div style="display: flex; flex-wrap: wrap; gap: 10px">
                <el-tag
                  v-for="(item, index) in drawingStyleList"
                  :key="index"
                  closable
                  style="margin-bottom: 10px"
                  @close="handleStyleRemove(index)"
                >
                  {{ item }}
                </el-tag>
                <el-input
                  v-if="inputVisible"
                  ref="inputRef"
                  v-model="inputValue"
                  size="small"
                  style="margin-left: 10px; width: auto; min-width: 80px"
                  @keyup.enter="handleInputConfirm"
                  @blur="handleInputConfirm"
                />
                <el-button v-else size="small" style="margin-left: 10px" @click="showInput">
                  + 添加风格
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider />
        <h5>其他配置</h5>
        <el-row>
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="Base64 识图" prop="isConvertToBase64" label-width="120">
              <el-switch
                v-model="formInline.isConvertToBase64"
                active-value="1"
                inactive-value="0"
              />
              <el-tooltip class="box-item" effect="dark" placement="right">
                <template #content>
                  <div style="width: 250px">
                    <p>
                      开启后，识图时将使用 base64 格式，对于本地/存储桶 链接 API
                      端无法访问时建议开启
                    </p>
                  </div>
                </template>
                <el-icon class="ml-3 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :xs="24" :md="20" :lg="15" :xl="12">
            <el-form-item label="TTS 音色" prop="openaiVoice" label-width="120px">
              <el-select
                v-model="formInline.openaiVoice"
                placeholder="选择或输入 openai 语音合成的默认发音人"
                clearable
                filterable
                allow-create
              >
                <!-- 预定义选项 -->
                <el-option
                  v-for="voice in voiceOptions"
                  :key="voice.value"
                  :label="voice.label"
                  :value="voice.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
