<script setup lang="ts">
import { useGlobalStoreWithOut } from '@/store'
import { computed, defineAsyncComponent, inject, ref } from 'vue'

// 异步加载预览组件
const TextEditor = defineAsyncComponent(() => import('./Previewer/TextEditor.vue'))
const ImagePreviewer = defineAsyncComponent(() => import('./Previewer/ImagePreviewer.vue'))
const HtmlPreviewer = defineAsyncComponent(() => import('./Previewer/HtmlPreviewer.vue'))
const WorkflowPreviewer = defineAsyncComponent(() => import('./Previewer/WorkflowPreviewer.vue'))
const MarkdownPreviewer = defineAsyncComponent(() => import('./Previewer/MarkdownPreviewer.vue'))

// 获取全局状态
const useGlobalStore = useGlobalStoreWithOut()

// 从父组件注入onConversation函数
const onConversation = inject<(params: any) => void>('onConversation')

// 定义事件
const emit = defineEmits<{
  (e: 'ask-question', prompt: string): void
}>()

// 计算当前应该显示哪个预览器
const activePanel = computed(() => {
  if (useGlobalStore.showHtmlPreviewer) return 'html'
  if (useGlobalStore.showTextEditor) return 'text'
  if (useGlobalStore.showImagePreviewer) return 'image'
  if (useGlobalStore.showWorkflowPreviewer) return 'workflow'
  if (useGlobalStore.isMarkdownPreviewerVisible) return 'markdown'
  return null
})

// 提供的关闭方法
const closeHtmlPreviewer = () => {
  useGlobalStore.updateHtmlPreviewer(false)
}

const closeTextEditor = () => {
  useGlobalStore.updateTextEditor(false)
}

const closeImagePreviewer = () => {
  useGlobalStore.updateImagePreviewer(false)
}

const closeWorkflowPreviewer = () => {
  useGlobalStore.updateWorkflowPreviewer(false)
}

const closeMarkdownPreviewer = () => {
  useGlobalStore.updateMarkdownPreviewer(false)
}

// 处理问题提问
const handleQuestion = (prompt: string) => {
  emit('ask-question', prompt)
}

// 暴露TextEditor的引用
const textEditorRef = ref(null)

// 统一关闭侧边栏的方法
const closeSidePanel = () => {
  if (useGlobalStore.showHtmlPreviewer) closeHtmlPreviewer()
  if (useGlobalStore.showTextEditor) closeTextEditor()
  if (useGlobalStore.showImagePreviewer) closeImagePreviewer()
  if (useGlobalStore.showWorkflowPreviewer) closeWorkflowPreviewer()
  if (useGlobalStore.isMarkdownPreviewerVisible) closeMarkdownPreviewer()
}

// 暴露给父组件的方法
defineExpose({
  textEditorRef,
  closeSidePanel,
})
</script>

<template>
  <div class="h-full w-full">
    <transition name="fade" mode="out-in">
      <HtmlPreviewer
        v-if="activePanel === 'html'"
        :close="closeHtmlPreviewer"
        class="h-full w-full"
      />
      <TextEditor
        v-else-if="activePanel === 'text'"
        :close="closeTextEditor"
        class="h-full w-full"
        ref="textEditorRef"
      />
      <ImagePreviewer
        v-else-if="activePanel === 'image'"
        :close="closeImagePreviewer"
        class="h-full w-full"
      />
      <WorkflowPreviewer
        v-else-if="activePanel === 'workflow'"
        :close="closeWorkflowPreviewer"
        class="h-full w-full"
      />
      <MarkdownPreviewer
        v-else-if="activePanel === 'markdown'"
        :close="closeMarkdownPreviewer"
        class="h-full w-full"
      />
    </transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
