<script setup lang="ts">
import { fetchQueryAppsAPI } from '@/api/appStore'
import type { ResData } from '@/api/types'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'
import { useAuthStore, useChatStore, useGlobalStoreWithOut } from '@/store'
import {
  AddPicture,
  FullScreen,
  LoadingFour,
  OffScreen,
  Plus,
  SendOne,
  Sphere,
  Square,
  TreasureChest,
  TwoEllipses,
} from '@icon-park/vue-next'
import PinyinMatch from 'pinyin-match'

// import { getDocument } from 'pdfjs-dist';
import { uploadFile } from '@/api/upload'
import { message } from '@/utils/message'
import { computed, inject, nextTick, onMounted, onUnmounted, Ref, ref, watch } from 'vue'
import FilePreview from './components/FilePreview.vue'
import ToolbarOptions from './components/ToolbarOptions.vue'

interface Emit {
  (ev: 'pause-request'): void
}

interface Props {
  dataSourcesLength: number
}

const props = defineProps<Props>()

// 引入依赖
const onConversation = inject<any>('onConversation')
// 引用的 store
const useGlobalStore = useGlobalStoreWithOut()
const authStore = useAuthStore()
const chatStore = useChatStore()
const emit = defineEmits<Emit>()
const siteName = authStore.globalConfig?.siteName || 'AIWeb'
const ms = message()

// 初始化变量 - 统一的图片尺寸配置
const imageSize = ref([
  {
    id: 'square',
    title: '自动',
    values: 'auto',
    aspectRatio: '1 / 1',
  },
  {
    id: 'illustration',
    title: t('chat.illustration'),
    values: '4:3',
    aspectRatio: '4 / 3',
  },
  {
    id: 'wallpaper',
    title: t('chat.wallpaper'),
    values: '16:9',
    aspectRatio: '16 / 9',
  },
  {
    id: 'media',
    title: t('chat.media'),
    values: '3:4',
    aspectRatio: '3 / 4',
  },
  {
    id: 'poster',
    title: t('chat.poster'),
    values: '9:16',
    aspectRatio: '9 / 16',
  },
])

const mjVersions = ref([
  {
    title: '默认',
    values: '--v 7',
  },
  {
    title: '草稿',
    values: '--draft',
  },
  {
    title: '普通',
    values: '--v 6',
  },
  {
    title: '卡通',
    values: '--niji 6',
  },
])

// gpt-image-1 的质量选项
const gptImage1Qualities = ref([
  {
    title: '自动',
    values: 'auto',
  },
  {
    title: '低',
    values: 'low',
  },
  {
    title: '中',
    values: 'medium',
  },
  {
    title: '高',
    values: 'high',
  },
])

// gpt-image-1 的压缩选项
const gptImage1Compressions = ref([
  {
    title: '无压缩',
    values: '0',
  },
  {
    title: '25%',
    values: '25',
  },
  {
    title: '50%',
    values: '50',
  },
  {
    title: '75%',
    values: '75',
  },
  {
    title: '100%',
    values: '100',
  },
])

// gpt-image-1 的背景选项
const gptImage1Backgrounds = ref([
  {
    title: '自动',
    values: 'auto',
  },
  {
    title: '透明',
    values: 'transparent',
  },
  {
    title: '不透明',
    values: 'opaque',
  },
])

const mjUrl = ref([
  {
    title: t('chat.imageToImage'), // 图生图
    values: 'imageToImage',
  },
  {
    title: t('chat.faceConsistency'), // 人脸一致
    values: 'faceConsistency',
  },
  {
    title: t('chat.styleConsistency'), // 风格一致
    values: 'styleConsistency',
  },
  {
    title: '以图生文', // 文生图
    values: 'imageToText',
  },
])

// const isStreamIn = ref(false);
const showDeleteIcon = ref(false)
const isFile = ref(true)
const fileInput = ref()
const imageInput = ref()
const isUploading = ref(false)
const searchResults = ref<any[]>([])
const inputRef = ref<Ref | null>(null)
const footerRef = ref<HTMLElement | null>(null) // 添加容器引用
const isDragging = ref(false) // 添加拖拽状态标志
const isFileDraggingOverPage = ref(false) // 添加文件拖到页面内(但未到输入框)的状态标志
const extraParam = ref<{
  size: string
  style: string
  quality?: string
  compression?: string
  background?: string
}>({ size: '', style: '' })
const mjUrlParam = ref()
const mjVersionsParam = ref('--v 7')
const randomStyles = ref<string[]>([])
const selectedSize = ref(imageSize.value[0])
const selectedMjUrl = ref(mjUrl.value[0])
const selectedMjVersion = ref(mjVersions.value[0])
// const selectedGptImage1Quality = ref(gptImage1Qualities.value[0])
// const selectedGptImage1Compression = ref(gptImage1Compressions.value[0])
const selectedGptImage1Background = ref(gptImage1Backgrounds.value[0])
const customWidth = ref('')
const customHeight = ref('')
const customAspectRatio = ref('')
const showSuggestions = ref(false)
const selectedApp = ref()
const isSelectedApp = ref(false)
const appList = ref<App[]>([])
let searchTimeout: string | number | NodeJS.Timeout | null | undefined = null
const fileUploadConfig = ref({
  accept: '.pdf, .txt, .doc, .docx,.ppt,.pptx, .xlsx,.xls,.csv .md, .markdown',
  multiple: true,
})

const imageUploadConfig = ref({
  accept: 'image/*',
  multiple: true,
})

interface App {
  id: number
  name: string
  des: string
  coverImg: string
  catId: number
  appCount: number
  demoData: string
  backgroundImg?: string
  prompt?: string
  loading?: boolean
  createdAt: string
  updatedAt: string
}

// 双向绑定 chatStore.prompt
const prompt = computed({
  get: () => chatStore.prompt,
  set: value => {
    chatStore.setPrompt(value || '')
  },
})

const usingNetwork = computed({
  get: () => chatStore.usingNetwork,
  set: value => {
    chatStore.setUsingNetwork(value)
  },
})

const usingMcpTool = computed({
  get: () => chatStore.usingMcpTool,
  set: value => {
    chatStore.setUsingMcpTool(value)
  },
})

const usingDeepThinking = computed({
  get: () => chatStore.usingDeepThinking,
  set: value => {
    chatStore.setUsingDeepThinking(value)
  },
})

const globalConfig = computed(() => authStore.globalConfig)
const { isMobile } = useBasicLayout()
const usingPlugin = computed(() => chatStore.currentPlugin)
const isStreamIn = computed(() => {
  return chatStore.isStreamIn !== undefined ? chatStore.isStreamIn : false
})
const dataSources = computed(() => chatStore.chatList)
const activeModelName = computed(() => String(configObj?.value.modelInfo.modelName))
const activeModelKeyType = computed(() => {
  return usingPlugin.value?.modelType || Number(configObj?.value.modelInfo.keyType)
})

const activeGroupId = computed(() => chatStore.active)
const activeGroupInfo = computed(() => chatStore.getChatByGroupInfo())
const configObj = computed(() => {
  const configString = activeGroupInfo.value?.config
  if (!configString) {
    return {} // 提早返回一个空对象
  }

  try {
    return JSON.parse(configString)
  } catch (e) {
    return {} // 解析失败时返回一个空对象
  }
})
const activeModel = computed(() => String(configObj?.value?.modelInfo?.model ?? ''))
const activeModelFileUpload = computed(() => Number(configObj?.value?.modelInfo?.isFileUpload))
const activeModelImageUpload = computed(() => Number(configObj?.value?.modelInfo?.isImageUpload))

// 添加两个表，控制哪些插件支持文件上传和图片上传
const pluginSupportFileUpload = ref(['mind-map', 'ai-ppt', 'mermaid'])

const pluginSupportImageUpload = ref([
  'mind-map',
  'mermaid',
  'midjourney',
  'luma-video',
  'flux-draw',
  'cog-video',
  'gpt-4o-image',
  'gpt-4o-image-vip',
  'sora_image',
  'seededit',
  'seedream-3.0',
  'doubao-image',
  'gpt-image-1',
  'flux-kontext-pro',
  'flux-kontext-max',
])

const isFilesModel = computed(() => {
  // 优先判断是否在使用插件
  if (usingPlugin.value) {
    // 如果使用插件，则根据插件支持列表判断
    return pluginSupportFileUpload.value.includes(usingPlugin.value.parameters)
  }
  // 如果不使用插件，则按照模型配置判断
  return activeModelFileUpload.value !== 0
})

const isImageModel = computed(() => {
  // 优先判断是否在使用插件
  if (usingPlugin.value) {
    // 如果使用插件，则根据插件支持列表判断
    if (usingPlugin.value.drawingType && usingPlugin.value.drawingType !== 0) {
      return true
    }
    return pluginSupportImageUpload.value.includes(usingPlugin.value.parameters)
  }
  // 如果不使用插件，则按照模型配置判断
  return activeModelImageUpload.value !== 0
})

const isDalleModel = computed(
  () =>
    activeModel.value === 'dall-e-3' ||
    activeModel.value === 'doubao-image' ||
    usingPlugin.value?.drawingType === 1
)

const isGptImage1Model = computed(
  () =>
    activeModel.value === 'gpt-image-1' ||
    usingPlugin.value?.parameters === 'gpt-image-1' ||
    usingPlugin.value?.parameters === 'dall-e-3' ||
    usingPlugin.value?.parameters === 'doubao-image' ||
    usingPlugin.value?.drawingType === 2 ||
    usingPlugin.value?.drawingType === 5
)

const isSeedEditModel = computed(
  () => activeModel.value === 'seededit' || usingPlugin.value?.parameters === 'seededit'
)

const isNetworkSearch = computed(() => configObj?.value?.modelInfo?.isNetworkSearch || false)

const isMcpTool = computed(() => configObj?.value?.modelInfo?.isMcpTool || false)

const isDeepThinking = computed(() => configObj?.value?.modelInfo?.deepThinkingType === 1 || false)

const isFluxModel = computed(
  () => activeModel.value.includes('flux') || usingPlugin.value?.parameters.includes('flux')
)
const isMidjourneyModel = computed(
  () =>
    activeModel.value === 'midjourney' ||
    usingPlugin.value?.parameters === 'midjourney' ||
    usingPlugin.value?.drawingType === 3
)

const isMermaidModel = computed(
  () => activeModel.value === 'mermaid' || usingPlugin.value?.parameters === 'mermaid'
)

const isPptModel = computed(
  () => activeModel.value === 'ai-ppt' || usingPlugin.value?.parameters === 'ai-ppt'
)

const clipboardText = computed(() => useGlobalStore.clipboardText)

const buttonDisabled = computed(
  () =>
    isStreamIn.value ||
    ((!prompt.value || prompt.value.trim() === '') && !(dataBase64List.value.length > 0))
)

const switchSize = (option: any) => {
  if (option.id === 'custom') {
    const customRatio = `${customWidth.value}:${customHeight.value}`
    selectedSize.value = {
      ...option,
      values: customRatio,
      aspectRatio: `${customWidth.value} / ${customHeight.value}`,
    }
    extraParam.value.size = customRatio
  } else {
    selectedSize.value = option
    extraParam.value.size = option.values
  }
}

const switchMjVersion = (option: any) => {
  selectedMjVersion.value = option
  mjVersionsParam.value = option.values
}

const switchMjUrl = (option: any) => {
  if (!(dataBase64List.value.length > 0)) {
    triggerImageUpload()
  }
  selectedMjUrl.value = option
  mjUrlParam.value = option.values
}

// const switchGptImage1Quality = (option: any) => {
//   selectedGptImage1Quality.value = option
// }

// const switchGptImage1Compression = (option: any) => {
//   selectedGptImage1Compression.value = option
// }

const switchGptImage1Background = (option: any) => {
  selectedGptImage1Background.value = option
}

const isExpanded = ref(false) // 控制输入框是否扩展
const shouldShowExpandButton = ref(false) // 控制是否显示扩展按钮

const autoResize = () => {
  if (inputRef.value) {
    const textarea = inputRef.value

    if (isExpanded.value) {
      // 展开模式下，使用固定高度
      textarea.style.height = '60vh'
      return
    }

    // 保存当前高度用于平滑过渡
    const currentHeight = textarea.style.height

    // 普通模式下，先重置高度，然后根据内容自适应
    textarea.style.height = 'auto' // 使用auto而不是固定的小值，让浏览器自动计算

    // 获取自动计算后的scrollHeight
    const contentHeight = textarea.scrollHeight

    // 获取行高
    const singleLineHeight = parseFloat(window.getComputedStyle(textarea).lineHeight) || 20 // 默认行高 20px

    // 普通模式最大行数为8行
    const maxLines = 8
    const maxHeight = singleLineHeight * maxLines // 最大高度

    // 计算新高度，确保不超过最大高度
    const newHeight = Math.min(contentHeight, maxHeight)

    // 设置新高度
    textarea.style.height = `${newHeight}px`

    // 判断是否应该显示扩展按钮 - 当内容高度超过4行时显示
    shouldShowExpandButton.value = contentHeight > singleLineHeight * 4
  }
}

// 切换扩展模式
const toggleExpanded = () => {
  // 从展开到收起模式时，先获取当前高度
  const currentHeight = isExpanded.value ? inputRef.value.style.height : null

  // 切换状态
  isExpanded.value = !isExpanded.value

  nextTick(() => {
    if (isExpanded.value) {
      // 展开模式下，使用固定高度
      inputRef.value.style.height = '60vh'
    } else {
      // 收起模式时，先保持当前高度，然后在下一帧进行调整
      // 这样可以让过渡效果更平滑
      if (currentHeight) {
        // 先设置当前高度，避免立即收缩导致的视觉跳跃
        inputRef.value.style.height = currentHeight

        // 在下一帧进行实际的大小调整
        requestAnimationFrame(() => {
          autoResize()
        })
      } else {
        autoResize()
      }
    }
  })
}

// 监听 prompt 的变化（外部修改时调整高度）
watch(
  prompt,
  () => {
    nextTick(() => {
      autoResize()
    })
  },
  { immediate: true } // 初始化时立即调整
)

const handleInput = async (event: KeyboardEvent) => {
  const inputElement = event.target as HTMLTextAreaElement
  const inputValue = inputElement.value
  showSuggestions.value = inputValue.startsWith('@')

  // 清除之前的定时器，如果有的话
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  if (showSuggestions.value && !isSelectedApp.value) {
    const searchTerm = inputValue.slice(1) // 去掉'@'

    // 使用定时器来节流搜索请求
    searchTimeout = setTimeout(async () => {
      if (searchTerm.length > 0) {
        try {
          const keywordLower = searchTerm.toLowerCase()

          // 根据拼音匹配过滤符合的应用
          const filteredResults = appList.value.filter(item =>
            PinyinMatch.match(item.name, keywordLower)
          )

          searchResults.value = filteredResults.slice(0, 5)
        } catch (error) {
          console.error('Error fetching search results:', error)
          searchResults.value = []
        }
      } else {
        // 如果关键字为空，随机选取5个结果
        const randomResults = appList.value
          .sort(() => Math.random() - 0.5) // 随机打乱顺序
          .slice(0, 5) // 取前5个
        searchResults.value = randomResults
      }
    }, 100) // 设置1秒的延迟
  } else {
    searchResults.value = []
  }
}

async function queryApps() {
  const res: ResData = await fetchQueryAppsAPI()
  appList.value = res?.data?.rows.map((item: App) => {
    item.loading = false
    return item
  })
  // activeList.value = appList.value;
}

const activeModelAvatar = computed(() => {
  return String(usingPlugin?.value?.pluginImg || configObj?.value.modelInfo?.modelAvatar || '')
})

// 初始化randomStyles或者在需要时更新它
const updateRandomStyles = () => {
  const stylesArray = globalConfig.value?.drawingStyles.split(',')
  const shuffled = stylesArray.sort(() => 0.5 - Math.random())
  // 如果是移动端，只显示3个；否则显示5个
  const displayCount = 3
  randomStyles.value = shuffled.slice(0, displayCount)
}

const handleToolbarOptionsUpdate = (val: string[]) => {
  randomStyles.value = val
}

async function appendStyleToInput(style: any) {
  // 检查prompt.value是否非空并且以逗号结尾
  if (prompt.value && /,\s*$/.test(prompt.value)) {
    // 如果已经以逗号结尾，直接添加风格
    await chatStore.setPrompt(`${prompt.value} ${style}`)
    // prompt.value += ` ${style}`;
  } else if (prompt.value) {
    // 如果非空但不以逗号结尾，先添加逗号再添加风格
    await chatStore.setPrompt(`${prompt.value}, ${style}`)
    // prompt.value += `, ${style}`;
  } else {
    // 如果prompt.value为空，只添加风格不添加逗号
    await chatStore.setPrompt(`${style}`)
    // prompt.value = `${style} ,`;
  }

  // 确保inputRef是已经和textarea元素绑定的ref
  inputRef.value.focus()
  inputRef.value.scrollTop = inputRef.value.scrollHeight
}

const createNewChatGroup = inject('createNewChatGroup', () =>
  Promise.resolve()
) as () => Promise<void>

// 修改计算属性，直接从对话组获取fileUrl
const fileUrl = computed(() => activeGroupInfo.value?.fileUrl || '')

// 修改计算属性，解析fileUrl为对象数组
const savedFiles = computed(() => {
  if (!fileUrl.value) return []

  try {
    return JSON.parse(fileUrl.value) as { name: string; url: string; type?: string }[]
  } catch (e) {
    console.error('解析fileUrl失败:', e)
    return []
  }
})

const handleSubmit = async (index?: number) => {
  if (isStreamIn.value) {
    return
  }

  // 新增检查：如果是SeedEdit模型且没有上传图片，提醒用户
  if (isSeedEditModel.value && dataBase64List.value.length === 0) {
    ms.warning('请先上传图片')
    return
  }

  if (chatStore.groupList.length === 0) {
    await createNewChatGroup()
  }
  chatStore.setStreamIn(true)
  let action = ''
  if (usingPlugin.value?.parameters === 'suno-music') {
    action = 'LYRICS'
  } else if (usingPlugin.value?.parameters === 'midjourney') {
    if (mjUrlParam.value === 'imageToText') {
      action = 'DESCRIBE'
    } else {
      action = 'IMAGINE'
    }
  }

  let useModel =
    usingPlugin.value?.parameters === 'mind-map' || usingPlugin.value?.parameters === 'mermaid'
      ? selectedApp?.value?.model || chatStore?.activeModel
      : usingPlugin.value?.parameters || selectedApp?.value?.model || chatStore?.activeModel
  let useModelName =
    usingPlugin?.value?.pluginName || selectedApp?.value?.name || activeModelName.value

  const useModelType =
    usingPlugin.value?.parameters &&
    usingPlugin.value?.parameters !== 'mind-map' &&
    usingPlugin.value?.parameters !== 'mermaid'
      ? 2
      : activeModelKeyType.value

  console.log('selectedApp', selectedApp.value)
  let modelAvatar = selectedApp?.value?.coverImg || activeModelAvatar.value
  let appId

  if (selectedApp?.value) {
    appId = selectedApp?.value?.id
  } else {
    appId = activeGroupInfo?.value?.appId
  }

  let imageUrl = ''
  let videoUrl = ''
  let submittedFileUrl = fileUrl.value || ''
  let msg = prompt.value || ''

  // 统一设置size参数给所有图片生成模型
  if (selectedSize.value && selectedSize.value.values) {
    extraParam.value.size = selectedSize.value.values
  }

  // 处理图片文件上传（只有在提交时才上传图片）
  if (dataBase64List.value.length > 0) {
    // 获取所有图片文件
    const imageFiles = fileList.value.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length > 0) {
      console.log('开始上传图片...')
      isUploading.value = true
      try {
        // 上传所有图片文件
        const uploadPromises = imageFiles.map(async file => {
          try {
            const response = await uploadFile(file)
            return response.data
          } catch (error) {
            console.error(`上传图片 ${file.name} 失败:`, error)
            return ''
          }
        })

        // 等待所有图片上传完成
        const results = await Promise.all(uploadPromises)
        imageUrl = results.filter(url => url).join(',')
        console.log('图片上传成功:', imageUrl)
      } catch (error) {
        console.error('上传图片过程中发生错误:', error)
        ms.error('图片上传失败')
      } finally {
        isUploading.value = false
      }
    }
  }

  // 处理视频文件上传（只有在提交时才上传视频）
  const videoFiles = fileList.value.filter(file => file.type.startsWith('video/'))
  if (videoFiles.length > 0) {
    console.log('开始上传视频...')
    isUploading.value = true
    try {
      // 上传所有视频文件
      const uploadPromises = videoFiles.map(async file => {
        try {
          const response = await uploadFile(file)
          return response.data
        } catch (error) {
          console.error(`上传视频 ${file.name} 失败:`, error)
          return ''
        }
      })

      // 等待所有视频上传完成
      const results = await Promise.all(uploadPromises)
      videoUrl = results.filter(url => url).join(',')
      console.log('视频上传成功:', videoUrl)
    } catch (error) {
      console.error('上传视频过程中发生错误:', error)
      ms.error('视频上传失败')
    } finally {
      isUploading.value = false
    }
  }

  // midjourney处理逻辑
  if (useModel === 'midjourney' && action === 'IMAGINE') {
    if (imageUrl) {
      switch (mjUrlParam.value) {
        case 'faceConsistency':
          msg = `${msg} --cref ${imageUrl} --cw 100`
          break
        case 'styleConsistency':
          msg = `${msg} --sref ${imageUrl}`
          break
        default:
          msg = `${imageUrl} ${msg}`
      }
    }

    // 使用统一的比例格式，直接添加--ar参数
    if (extraParam.value.size && !msg.includes('--ar')) {
      const size = extraParam.value.size
      msg = `${msg} --ar ${size}`
    }

    if (
      mjVersionsParam.value &&
      !msg.includes('--v') &&
      !msg.includes('--niji') &&
      !msg.includes('--draft')
    ) {
      msg = `${msg} ${mjVersionsParam.value}`
    }
  }

  // 处理drawingType为1的情况，将图片链接加到msg前面
  if (usingPlugin.value?.drawingType === 1 && imageUrl) {
    msg = `${imageUrl} ${msg}`
  }

  // 处理gpt-image-1模型的参数
  if (useModel === 'gpt-image-1' || useModel === 'doubao-image') {
    // 设置背景参数
    if (selectedGptImage1Background.value) {
      extraParam.value.background = selectedGptImage1Background.value.values
    }

    // 如果有图片，转换为imageUrls格式
    if (imageUrl) {
      // 为gpt-image-1模型创建特殊的JSON格式
      const imageUrlsJSON = {
        imageUrls: imageUrl.split(',').map((url, index) => {
          return {
            url: url,
            type: 'image',
            index: index,
          }
        }),
      }

      // 将JSON对象转为字符串，直接设置为imageUrl参数
      imageUrl = JSON.stringify(imageUrlsJSON)
    }
  }

  if (action === 'DESCRIBE') {
    if (imageUrl) {
      msg = `以图生文`
    }
  }

  // if (appId) {
  //   try {
  //     const res: any = await fetchQueryOneCatAPI({ id: appId })
  //     modelAvatar = res.data.modelAvatar
  //   } catch (error) {}
  // }

  await chatStore.setPrompt('')
  inputRef.value.style.height = '1rem' // 使用初始高度

  console.log('开始对话', {
    prompt,
    action,
    useModel,
    useModelName,
    useModelType,
    modelAvatar,
    appId,
    extraParam: extraParam.value,
    fileUrl: submittedFileUrl,
    imageUrl,
    videoUrl,
    pluginParam: usingPlugin.value?.parameters,
  })

  onConversation({
    msg: msg,
    action: action,
    model: useModel,
    modelName: useModelName,
    modelType: useModelType,
    modelAvatar: modelAvatar,
    appId: appId,
    extraParam: extraParam.value,
    fileUrl: submittedFileUrl,
    imageUrl: imageUrl,
    videoUrl: videoUrl,
    pluginParam: usingPlugin.value?.parameters,
  })

  // 清空所有上传的图片和视频，确保imageUrl、videoUrl和fileUrl使用后立即销毁
  chatStore.setStreamIn(false)
  isUploading.value = false

  // 只清空图片和视频文件，保留文档文件
  const nonMediaFiles = fileList.value.filter(
    file => !file.type.startsWith('image/') && !file.type.startsWith('video/')
  )
  const nonMediaData = dataBase64List.value.filter(
    (_, index) =>
      !fileList.value[index]?.type.startsWith('image/') &&
      !fileList.value[index]?.type.startsWith('video/') &&
      index < fileList.value.length
  )

  fileList.value = nonMediaFiles
  dataBase64List.value = nonMediaData

  // 重要: 清空临时变量，确保不会被再次使用
  imageUrl = ''
  videoUrl = ''
}

const triggerUpload = () => {
  // 根据当前模型支持情况决定触发哪种上传
  const canUploadFiles = isFilesModel.value
  const canUploadImages = isImageModel.value
  const canUploadVideos = true // 视频上传默认支持

  if (canUploadFiles && canUploadImages && canUploadVideos) {
    // 三种类型都支持，使用综合配置
    fileUploadConfig.value = {
      accept:
        '.pdf, .txt, .doc, .docx,.ppt,.pptx, .xlsx,.xls,.csv .md, .markdown, image/*, video/*',
      multiple: true,
    }
  } else if (canUploadImages && canUploadVideos) {
    // 支持图片和视频
    fileUploadConfig.value = {
      accept: 'image/*, video/*',
      multiple: true,
    }
  } else if (canUploadFiles && canUploadVideos) {
    // 支持文件和视频
    fileUploadConfig.value = {
      accept: '.pdf, .txt, .doc, .docx, .ppt,.pptx, .xlsx,.xls,.csv .md, .markdown, video/*',
      multiple: true,
    }
  } else if (canUploadImages) {
    // 只支持图片
    fileUploadConfig.value = {
      accept: 'image/*',
      multiple: true,
    }
  } else if (canUploadVideos) {
    // 只支持视频
    fileUploadConfig.value = {
      accept: 'video/*',
      multiple: true,
    }
  } else if (canUploadFiles) {
    // 只支持文件
    fileUploadConfig.value = {
      accept: '.pdf, .txt, .doc, .docx, .ppt,.pptx, .xlsx,.xls,.csv .md, .markdown',
      multiple: true,
    }
  }

  // 重新设置 input 的属性
  if (fileInput.value) {
    fileInput.value.accept = fileUploadConfig.value.accept
    fileInput.value.multiple = fileUploadConfig.value.multiple
  }

  // 触发文件选择
  fileInput?.value?.click()
}

const triggerImageUpload = (config?: { accept: string; multiple: boolean }) => {
  if (config) {
    imageUploadConfig.value = config
    console.log('imageUploadConfig', imageUploadConfig.value)
  } else {
    imageUploadConfig.value = {
      accept: 'image/*',
      multiple: true,
    }
  }

  // 重新设置 input 的属性
  if (imageInput.value) {
    imageInput.value.accept = imageUploadConfig.value.accept
    imageInput.value.multiple = imageUploadConfig.value.multiple
  }

  imageInput?.value?.click()
}

const fileList = ref<File[]>([]) // 使用 ref 来创建响应式的文件列表
const dataBase64List = ref<string[]>([]) // 使用 ref 来创建响应式的 Base64 数据列表

// 视频处理复用图片逻辑 - 直接存储在 fileList 和 dataBase64List 中

const handleSetFile = async (file: File) => {
  // 添加数量限制检查
  const isImageFile = file.type.startsWith('image/')

  // 获取当前已有文件数量
  const currentImageCount = fileList.value.filter(f => f.type.startsWith('image/')).length

  // 检查对话组中已有的文件数量
  let existingFiles = []
  try {
    if (fileUrl.value) {
      existingFiles = JSON.parse(fileUrl.value)
    }
  } catch (error) {
    existingFiles = []
  }

  const savedImageCount = Array.isArray(existingFiles)
    ? existingFiles.filter(f => f.type === 'image').length
    : 0
  const savedFileCount = Array.isArray(existingFiles)
    ? existingFiles.filter(f => f.type === 'document').length
    : 0

  // 图片数量限制为4张（缓存图片+已保存图片）
  if (isImageFile && currentImageCount + savedImageCount >= 4) {
    ms.warning('图片数量已达上限（最多4张）')
    return
  }

  // 文件数量限制为5个（已保存文件）
  if (!isImageFile && savedFileCount >= 5) {
    ms.warning('文件数量已达上限（最多5个）')
    return
  }

  fileList.value.push(file) // 使用 .value 访问 ref 对象并追加新文件

  const reader = new FileReader()

  reader.onload = (event: any) => {
    const base64Data = event.target?.result as string
    dataBase64List.value.push(base64Data) // 使用 .value 访问 ref 对象并追加 Base64 数据
    console.log(`文件 ${file.name} 的 Base64 数据已添加`)

    // 如果不是图片文件且不是视频文件，立即上传文件并更新对话组
    if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
      handleUploadFile(file)
    }
  }

  reader.readAsDataURL(file) // 读取文件并转换为 Base64

  fileInput.value = null
}

// 新增：处理单个文件上传
const handleUploadFile = async (file: File) => {
  if (file.type.startsWith('image/') || file.type.startsWith('video/')) {
    // 图片和视频文件不立即上传，只预览
    return
  }

  isUploading.value = true
  try {
    const response = await uploadFile(file)
    console.log(`文件 ${file.name} 上传成功:`, response.data)

    // 将文件信息添加到对话组
    const fileInfo = {
      name: file.name,
      url: response.data,
      type: 'document',
    }

    // 获取对话组当前已有文件
    let existingFiles = []
    try {
      if (fileUrl.value) {
        existingFiles = JSON.parse(fileUrl.value)
        if (!Array.isArray(existingFiles)) {
          existingFiles = []
        }
        // 数据验证和清理：过滤掉异常数据
        existingFiles = existingFiles.filter(
          file =>
            file &&
            typeof file === 'object' &&
            file.name &&
            file.url &&
            (file.type === 'image' || file.type === 'document')
        )
      }
    } catch (error) {
      existingFiles = []
    }

    // 将新文件添加到列表
    existingFiles.push(fileInfo)

    // 更新对话组的fileUrl字段
    await chatStore.updateGroupInfo({
      groupId: activeGroupId.value,
      fileUrl: JSON.stringify(existingFiles),
    })

    // 刷新数据以更新UI
    await chatStore.queryMyGroup()

    // ms.success(`文件"${file.name}"已上传`)
  } catch (error) {
    console.error(`上传文件 ${file.name} 失败:`, error)
    ms.error(`文件"${file.name}"上传失败`)
  } finally {
    isUploading.value = false
  }
}

const handlePaste = async (event: ClipboardEvent) => {
  const clipboardData = event.clipboardData || (window as any).clipboardData
  const items = clipboardData.items

  for (const item of items) {
    if (item.kind === 'file') {
      const file = item.getAsFile()
      if (file) {
        if (file.type.startsWith('image/') && isImageModel.value) {
          await processImageFile(file)
        } else if (file.type.startsWith('video/')) {
          await processVideoFile(file)
        } else if (
          !file.type.startsWith('image/') &&
          !file.type.startsWith('video/') &&
          isFilesModel.value
        ) {
          await processDocumentFile(file)
        } else {
          console.warn('文件类型不匹配当前模型支持的类型:', file.name)
        }
      }
    }
  }
}

// 处理文档类型文件
const processDocumentFile = async (file: File) => {
  if (file.type.startsWith('image/')) {
    console.warn('图片文件应使用图片上传功能:', file.name)
    return
  }

  if (file.type.startsWith('video/')) {
    console.warn('视频文件应使用视频上传功能:', file.name)
    return
  }

  // 检查文件类型
  const acceptedTypes = [
    '.pdf',
    '.txt',
    '.doc',
    '.docx',
    '.pptx',
    '.xlsx',
    '.md',
    '.markdown',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/markdown',
  ]

  const isAcceptedType = acceptedTypes.some(type => {
    if (type.startsWith('.')) {
      return file.name.toLowerCase().endsWith(type.toLowerCase())
    } else {
      return file.type === type
    }
  })

  if (!isAcceptedType) {
    ms.warning(`不支持的文件类型: ${file.name}`)
    return
  }

  // 检查文件大小限制（20MB）
  const maxSize = 20 * 1024 * 1024 // 20MB
  if (file.size > maxSize) {
    ms.warning(`文件过大: ${file.name}，最大支持20MB`)
    return
  }

  // 获取当前已有文件数量
  let existingFiles = []
  try {
    if (fileUrl.value) {
      existingFiles = JSON.parse(fileUrl.value)
      if (!Array.isArray(existingFiles)) {
        existingFiles = []
      }
    }
  } catch (error) {
    existingFiles = []
  }

  const savedFileCount = existingFiles.filter(f => f.type === 'document').length
  const currentFileCount = fileList.value.filter(
    f => !f.type.startsWith('image/') && !f.type.startsWith('video/')
  ).length

  // 检查文件总数是否超过限制
  if (savedFileCount + currentFileCount >= 5) {
    ms.warning('文件数量已达上限')
    return
  }

  let trimmedFileName = file.name
  const maxLength = 25 // 最大长度限制
  const extension = trimmedFileName.split('.').pop() || '' // 获取文件扩展名

  if (trimmedFileName.length > maxLength) {
    // 截取文件名并添加省略号，同时保留扩展名
    trimmedFileName =
      trimmedFileName.substring(0, maxLength - extension.length - 1) + '….' + extension
  }

  // 处理非图片文件，支持多文件
  isFile.value = true
  handleSetFile(file)

  if (isPptModel.value) {
    await chatStore.setPrompt(`使用 ${trimmedFileName} 生成 PPT`)
  }
}

// 处理图片类型文件
const processImageFile = async (file: File) => {
  if (!file.type.startsWith('image/')) {
    console.warn('非图片文件应使用文件上传功能:', file.name)
    return
  }

  // 检查图片类型
  const acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/bmp']

  if (!acceptedTypes.includes(file.type)) {
    ms.warning(`不支持的图片类型: ${file.name}，请使用jpg、png、gif、webp或bmp格式`)
    return
  }

  // 检查图片大小限制（10MB）
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    ms.warning(`图片过大: ${file.name}，最大支持10MB`)
    return
  }

  // 检查当前已有图片数量
  const currentImageCount = fileList.value.filter(f => f.type.startsWith('image/')).length

  // 计算已保存的图片数量
  let existingFiles = []
  try {
    if (fileUrl.value) {
      existingFiles = JSON.parse(fileUrl.value)
      if (!Array.isArray(existingFiles)) {
        existingFiles = []
      }
    }
  } catch (error) {
    existingFiles = []
  }

  const savedImageCount = existingFiles.filter(f => f.type === 'image').length

  // 检查图片总数是否超过限制
  if (currentImageCount + savedImageCount >= 4) {
    ms.warning('图片数量已达上限')
    return
  }

  let trimmedFileName = file.name
  const maxLength = 25 // 最大长度限制
  const extension = trimmedFileName.split('.').pop() || '' // 获取文件扩展名

  if (trimmedFileName.length > maxLength) {
    // 截取文件名并添加省略号，同时保留扩展名
    trimmedFileName =
      trimmedFileName.substring(0, maxLength - extension.length - 1) + '….' + extension
  }

  // 处理图片文件，支持多图片
  isFile.value = false
  handleSetFile(file)
}

// 处理视频类型文件 - 复用图片处理逻辑
const processVideoFile = async (file: File) => {
  if (!file.type.startsWith('video/')) {
    console.warn('非视频文件应使用其他上传功能:', file.name)
    return
  }

  // 检查视频类型
  const acceptedTypes = [
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/quicktime',
    'video/x-msvideo',
  ]

  if (!acceptedTypes.includes(file.type)) {
    ms.warning(`不支持的视频类型: ${file.name}，请使用mp4、webm、ogg、mov或avi格式`)
    return
  }

  // 检查视频大小限制（50MB）
  const maxSize = 50 * 1024 * 1024 // 50MB
  if (file.size > maxSize) {
    ms.warning(`视频过大: ${file.name}，最大支持50MB`)
    return
  }

  // 检查当前已有视频数量（只检查缓存中的视频）
  const currentVideoCount = fileList.value.filter(f => f.type.startsWith('video/')).length

  // 检查视频总数是否超过限制（最多2个视频）
  if (currentVideoCount >= 2) {
    ms.warning('视频数量已达上限（最多2个）')
    return
  }

  let trimmedFileName = file.name
  const maxLength = 25 // 最大长度限制
  const extension = trimmedFileName.split('.').pop() || '' // 获取文件扩展名

  if (trimmedFileName.length > maxLength) {
    // 截取文件名并添加省略号，同时保留扩展名
    trimmedFileName =
      trimmedFileName.substring(0, maxLength - extension.length - 1) + '….' + extension
  }

  // 处理视频文件 - 复用图片处理逻辑
  handleSetFile(file)
}

const handleFileSelect = async (event: Event) => {
  console.log('文件选择事件触发')
  const input = event.target as HTMLInputElement
  const files = input?.files
  if (!files || files.length === 0) return

  // 分类文件
  const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'))
  const videoFiles = Array.from(files).filter(file => file.type.startsWith('video/'))
  const documentFiles = Array.from(files).filter(
    file => !file.type.startsWith('image/') && !file.type.startsWith('video/')
  )

  // 处理图片文件
  if (imageFiles.length > 0 && isImageModel.value) {
    // 获取当前已有图片数量
    const currentImageCount = fileList.value.filter(f => f.type.startsWith('image/')).length
    // 计算已保存的图片数量
    let existingFiles = []
    try {
      if (fileUrl.value) {
        existingFiles = JSON.parse(fileUrl.value)
      }
    } catch (error) {
      existingFiles = []
    }
    if (!Array.isArray(existingFiles)) {
      existingFiles = []
    }
    const savedImageCount = existingFiles.filter(f => f.type === 'image').length
    // 限制只处理允许范围内的图片数量
    const remainingImageSlots = 4 - currentImageCount - savedImageCount

    // 如果图片数量超过限制
    if (imageFiles.length > remainingImageSlots && remainingImageSlots > 0) {
      ms.warning(
        `已选择${imageFiles.length}张图片，但只能再添加${remainingImageSlots}张图片。将只处理前${remainingImageSlots}张图片。`
      )
    } else if (remainingImageSlots <= 0) {
      ms.warning('图片数量已达上限（最多4张）')
    }

    // 处理允许范围内的图片
    const imagesToProcess = imageFiles.slice(0, Math.max(0, remainingImageSlots))
    for (const file of imagesToProcess) {
      await processImageFile(file)
    }
  } else if (imageFiles.length > 0 && !isImageModel.value) {
    ms.warning('当前模型不支持图片上传')
  }

  // 处理视频文件
  if (videoFiles.length > 0) {
    // 获取当前已有视频数量（只检查缓存）
    const currentVideoCount = fileList.value.filter(f => f.type.startsWith('video/')).length
    // 限制只处理允许范围内的视频数量
    const remainingVideoSlots = 2 - currentVideoCount

    // 如果视频数量超过限制
    if (videoFiles.length > remainingVideoSlots && remainingVideoSlots > 0) {
      ms.warning(
        `已选择${videoFiles.length}个视频，但只能再添加${remainingVideoSlots}个视频。将只处理前${remainingVideoSlots}个视频。`
      )
    } else if (remainingVideoSlots <= 0) {
      ms.warning('视频数量已达上限（最多2个）')
    }

    // 处理允许范围内的视频
    const videosToProcess = videoFiles.slice(0, Math.max(0, remainingVideoSlots))
    for (const file of videosToProcess) {
      await processVideoFile(file)
    }
  }

  // 处理文档文件
  if (documentFiles.length > 0 && isFilesModel.value) {
    // 获取当前已有文件数量
    let existingFiles = []
    try {
      if (fileUrl.value) {
        existingFiles = JSON.parse(fileUrl.value)
      }
    } catch (error) {
      existingFiles = []
    }
    if (!Array.isArray(existingFiles)) {
      existingFiles = []
    }
    const savedFileCount = existingFiles.filter(f => f.type === 'document').length
    // 限制只处理允许范围内的文件数量
    const remainingFileSlots = 5 - savedFileCount

    // 如果文件数量超过限制
    if (documentFiles.length > remainingFileSlots && remainingFileSlots > 0) {
      ms.warning(
        `已选择${documentFiles.length}个文件，但只能再添加${remainingFileSlots}个文件。将只处理前${remainingFileSlots}个文件。`
      )
    } else if (remainingFileSlots <= 0) {
      ms.warning('文件数量已达上限（最多5个）')
    }

    // 处理允许范围内的文件
    const filesToProcess = documentFiles.slice(0, Math.max(0, remainingFileSlots))
    // 显示上传进行中提示
    if (filesToProcess.length > 0) {
      isUploading.value = true
    }

    // 并行上传所有文件
    try {
      await Promise.all(
        filesToProcess.map(async file => {
          // 获取Base64预览
          await new Promise<void>(resolve => {
            const reader = new FileReader()
            reader.onload = e => {
              const base64Data = e.target?.result as string
              fileList.value.push(file)
              dataBase64List.value.push(base64Data)
              resolve()
            }
            reader.readAsDataURL(file)
          })

          // 直接上传文件并添加到对话组
          await handleUploadFile(file)
        })
      )
    } catch (error) {
      console.error('批量上传文件失败:', error)
    } finally {
      isUploading.value = false
    }
  } else if (documentFiles.length > 0 && !isFilesModel.value) {
    ms.warning('当前模型不支持文件上传')
  }

  // 清空input的值，允许再次选择相同文件
  input.value = ''
}

const handleImageSelect = async (event: Event) => {
  console.log('图片选择事件触发')
  const input = event.target as HTMLInputElement
  const files = input?.files
  if (!files || files.length === 0) return

  // 计算当前已有图片数量
  const currentImageCount = fileList.value.filter(f => f.type.startsWith('image/')).length

  // 计算已保存的图片数量
  let existingFiles = []
  try {
    if (fileUrl.value) {
      existingFiles = JSON.parse(fileUrl.value)
    }
  } catch (error) {
    existingFiles = []
  }

  if (!Array.isArray(existingFiles)) {
    existingFiles = []
  }

  const savedImageCount = existingFiles.filter(f => f.type === 'image').length

  // 检查图片总数是否超过限制
  if (currentImageCount + savedImageCount >= 4) {
    ms.warning('图片数量已达上限（最多4张）')
    input.value = ''
    return
  }

  // 限制只处理允许范围内的图片数量
  const remainingSlots = 4 - currentImageCount - savedImageCount

  // 先收集所有图片文件
  const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'))

  // 如果图片文件数量超过剩余槽位，进行提示
  if (imageFiles.length > remainingSlots) {
    ms.warning(
      `已选择${imageFiles.length}张图片，但只能再添加${remainingSlots}张图片。将只预览前${remainingSlots}张图片。`
    )
  }

  // 只处理剩余槽位数量的图片
  const imagesToProcess = imageFiles.slice(0, remainingSlots)

  // 并行处理所有图片（只预览，不上传）
  try {
    await Promise.all(
      imagesToProcess.map(async file => {
        // 获取Base64预览
        await new Promise<void>(resolve => {
          const reader = new FileReader()
          reader.onload = e => {
            const base64Data = e.target?.result as string
            fileList.value.push(file)
            dataBase64List.value.push(base64Data)
            resolve()
          }
          reader.readAsDataURL(file)
        })
      })
    )
  } catch (error) {
    console.error('批量处理图片失败:', error)
  } finally {
    input.value = ''
  }
}

const clearSelectApp = async () => {
  searchResults.value = []
  showSuggestions.value = false
  isSelectedApp.value = false
  selectedApp.value = null
  chatStore.setUsingPlugin(null)
}

const handleEnter = (event: KeyboardEvent) => {
  if (!isMobile.value) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSubmit()
    }
  } else {
    if (event.key === 'Enter' && event.ctrlKey) {
      event.preventDefault()
      handleSubmit()
    }
  }
}

const selectApp = async (app: any) => {
  // 这里可以设置选中的应用程序的逻辑
  selectedApp.value = app
  isSelectedApp.value = true
  await chatStore.setPrompt('')
  // prompt.value = '';
  inputRef.value?.focus()
}

const handleStop = () => {
  emit('pause-request')
  chatStore.setStreamIn(false)
}

watch([customWidth, customHeight], ([newWidth, newHeight], [oldWidth, oldHeight]) => {
  // 当两个输入都被填写且与之前的值不同时，执行switchSize
  if (newWidth && newHeight && (newWidth !== oldWidth || newHeight !== oldHeight)) {
    switchSize({
      id: 'custom',
      title: `${newWidth}:${newHeight}`,
      values: `${newWidth}:${newHeight}`,
      aspectRatio: `${newWidth} / ${newHeight}`,
    })
    customAspectRatio.value = `${customWidth.value} / ${customHeight.value}`
  }
})

watch(clipboardText, async val => {
  await chatStore.setPrompt(val)
  // prompt.value = val;
  inputRef.value?.focus()
  inputRef.value.scrollTop = inputRef.value.scrollHeight
})

watch(
  dataSources,
  val => {
    if (val.length === 0) return
  },
  { immediate: true }
)

// 监听 activeModelFileUpload 和 activeModelImageUpload 的变化
watch(
  [activeModelFileUpload, activeModelImageUpload],
  async ([newFileUpload, newImageUpload], [oldFileUpload, oldImageUpload]) => {
    if (oldFileUpload !== 0 && newFileUpload === 0) {
      // 文件上传支持从有变为无，清空所有文件
      // 先清空本地文件列表
      fileList.value = fileList.value.filter(file => file.type.startsWith('image/'))
      dataBase64List.value = dataBase64List.value.filter((_, index) =>
        fileList.value[index]?.type.startsWith('image/')
      )

      // 清空对话组中的文件（保留图片）
      if (fileUrl.value) {
        try {
          const files = JSON.parse(fileUrl.value)
          if (Array.isArray(files)) {
            const imageFiles = files.filter(file => file.type === 'image')
            // 更新对话组文件信息
            await chatStore.updateGroupInfo({
              groupId: activeGroupId.value,
              fileUrl: imageFiles.length > 0 ? JSON.stringify(imageFiles) : '',
            })
            // 刷新数据以更新UI
            await chatStore.queryMyGroup()
          }
        } catch (error) {
          console.error('清空文件失败：', error)
        }
      }
    }

    if (oldImageUpload !== 0 && newImageUpload === 0) {
      // 图片上传支持从有变为无，清空所有图片
      // 先清空本地图片列表
      fileList.value = fileList.value.filter(file => !file.type.startsWith('image/'))
      dataBase64List.value = dataBase64List.value.filter(
        (_, index) => !fileList.value[index]?.type.startsWith('image/')
      )

      // 清空对话组中的图片（保留文件）
      if (fileUrl.value) {
        try {
          const files = JSON.parse(fileUrl.value)
          if (Array.isArray(files)) {
            const nonImageFiles = files.filter(file => file.type === 'document')
            // 更新对话组文件信息
            await chatStore.updateGroupInfo({
              groupId: activeGroupId.value,
              fileUrl: nonImageFiles.length > 0 ? JSON.stringify(nonImageFiles) : '',
            })
            // 刷新数据以更新UI
            await chatStore.queryMyGroup()
          }
        } catch (error) {
          console.error('清空图片失败：', error)
        }
      }
    }
  }
)

// 修改clearData方法，统一使用JSON处理，支持视频删除
const clearData = async (index: number, isSavedFile = false, fileType?: string) => {
  if (isSavedFile) {
    // 处理已保存的文件，从对话组中删除文件
    try {
      if (fileUrl.value) {
        const files = JSON.parse(fileUrl.value)
        if (Array.isArray(files) && index >= 0 && index < files.length) {
          files.splice(index, 1)
          // 更新对话组文件信息
          await chatStore.updateGroupInfo({
            groupId: activeGroupId.value,
            fileUrl: files.length > 0 ? JSON.stringify(files) : '',
          })
          // 刷新数据以更新UI
          await chatStore.queryMyGroup()
        }
      }
    } catch (error) {
      console.error('删除文件失败：', error)
    }
  } else {
    // 处理新上传的文件，从本地列表中移除
    // 视频文件现在也存储在 fileList 和 dataBase64List 中
    if (index >= 0 && index < dataBase64List.value.length) {
      dataBase64List.value.splice(index, 1)
      fileList.value.splice(index, 1)
    }
  }
}

const buttonContainerRef = ref<HTMLElement | null>(null)
const availableWidth = ref(0)
const containerResizeObserver = ref<ResizeObserver | null>(null)

// 计算输入框占位符文本，根据不同工具状态显示不同提示
const placeholderText = computed(() => {
  const activeFeatures = []

  // 根据工具状态添加提示
  if (usingDeepThinking.value) {
    activeFeatures.push('使用AI推理寻找深层次答案')
  }

  if (usingNetwork.value) {
    activeFeatures.push('使用网络搜索获取最新信息')
  }

  if (usingMcpTool.value) {
    activeFeatures.push('使用MCP工具解决复杂问题')
  }

  // 拖拽状态提示
  if (isDragging.value) {
    return '松开鼠标上传文件'
  }

  // 如果有特殊功能激活，则显示功能描述
  if (activeFeatures.length > 0) {
    return activeFeatures.join('，')
  }

  // 默认提示
  return `向 ${siteName} 发消息，使用 @ 搜索应用`
})

// 按钮显示优先级
const shouldShowMcpTool = computed(() => {
  // 只检查是否支持MCP工具且没有使用插件
  return isMcpTool.value && !usingPlugin.value
})

const shouldShowNetworkSearch = computed(() => {
  // 只检查是否支持网络搜索且没有使用插件
  return isNetworkSearch.value && !usingPlugin.value
})

const shouldShowDeepThinking = computed(() => {
  // 只检查是否支持深度思考且没有使用插件
  return isDeepThinking.value && !usingPlugin.value
})

// 添加拖拽相关的处理函数
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragging.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragging.value = false
}

const handleDrop = (event: DragEvent) => {
  handleUnifiedFileDrop(event, 'area')
}

// 添加全局拖拽相关的处理函数
const handleDocumentDragOver = (event: DragEvent) => {
  event.preventDefault()
  // 检查是否有文件被拖动
  if (event.dataTransfer?.types.includes('Files')) {
    isFileDraggingOverPage.value = true
  }
}

const handleDocumentDragLeave = (event: DragEvent) => {
  // 只有当拖动到viewport外部时才重置状态
  if (
    event.clientX <= 0 ||
    event.clientY <= 0 ||
    event.clientX >= window.innerWidth ||
    event.clientY >= window.innerHeight
  ) {
    isFileDraggingOverPage.value = false
  }
}

const handleDocumentDrop = (event: DragEvent) => {
  // 重置拖拽状态
  isFileDraggingOverPage.value = false
  // 如果不是拖到了指定的上传区域，阻止默认行为
  if (!isDragging.value) {
    event.preventDefault()
  }
}

// 添加文件处理的新函数，对特定按钮区域的拖放文件进行处理
const handleFileDropOnButton = async (event: DragEvent, fileType: 'image' | 'document') => {
  const files = event.dataTransfer?.files
  if (!files || files.length === 0) return

  const fileArray = Array.from(files)
  const filteredFiles =
    fileType === 'image'
      ? fileArray.filter(file => file.type.startsWith('image/'))
      : fileArray.filter(file => !file.type.startsWith('image/'))

  if (filteredFiles.length === 0) {
    // 提示用户拖放的文件类型与目标不匹配
    if (fileType === 'image') {
      ms.warning('请拖放图片文件到图片上传按钮')
    } else {
      ms.warning('请拖放文档文件到文件上传按钮')
    }
    return
  }

  // 处理符合条件的文件
  for (const file of filteredFiles) {
    if (fileType === 'image') {
      await processImageFile(file)
    } else {
      await processDocumentFile(file)
    }
  }
}

onMounted(async () => {
  // 初始化 randomStyles
  updateRandomStyles()
  chatStore.setPrompt('')

  // 设置焦点
  nextTick(() => {
    if (inputRef.value && !isMobile.value) {
      inputRef.value.focus()
    }
  })
  await queryApps()

  // 添加全局拖拽事件监听
  document.addEventListener('dragover', handleDocumentDragOver)
  document.addEventListener('dragleave', handleDocumentDragLeave)
  document.addEventListener('drop', handleDocumentDrop)

  // 初始化调整宽度
  nextTick(() => {
    if (buttonContainerRef.value) {
      availableWidth.value = buttonContainerRef.value.offsetWidth
    }

    // 创建ResizeObserver来监听容器宽度变化
    containerResizeObserver.value = new ResizeObserver(entries => {
      for (const entry of entries) {
        if (entry.target === buttonContainerRef.value) {
          availableWidth.value = entry.contentRect.width
        }
      }
    })

    if (buttonContainerRef.value) {
      containerResizeObserver.value.observe(buttonContainerRef.value)
    }
  })
})

onUnmounted(() => {
  // 移除全局拖拽事件监听
  document.removeEventListener('dragover', handleDocumentDragOver)
  document.removeEventListener('dragleave', handleDocumentDragLeave)
  document.removeEventListener('drop', handleDocumentDrop)

  // 组件卸载时取消观察
  if (containerResizeObserver.value && buttonContainerRef.value) {
    containerResizeObserver.value.unobserve(buttonContainerRef.value)
    containerResizeObserver.value.disconnect()
  }
})

// 整合文件拖放处理逻辑
const handleUnifiedFileDrop = async (event: DragEvent, source: 'button' | 'area') => {
  event.preventDefault()
  event.stopPropagation()
  isDragging.value = false
  isFileDraggingOverPage.value = false

  const files = event.dataTransfer?.files
  if (!files || files.length === 0) return

  // 将FileList转换为数组以便处理
  const fileArray = Array.from(files)

  // 分类文件
  const imageFiles = fileArray.filter(file => file.type.startsWith('image/'))
  const videoFiles = fileArray.filter(file => file.type.startsWith('video/'))
  const documentFiles = fileArray.filter(
    file => !file.type.startsWith('image/') && !file.type.startsWith('video/')
  )

  // 检查文件类型支持情况
  const canUploadImages = isImageModel.value
  const canUploadVideos = true // 视频上传默认支持
  const canUploadDocuments = isFilesModel.value

  // 准备处理的文件数组
  const filesToProcess = []
  const unsupportedFiles = []

  // 检查图片文件
  if (imageFiles.length > 0) {
    if (canUploadImages) {
      filesToProcess.push(...imageFiles)
    } else {
      unsupportedFiles.push(
        ...imageFiles.map(f => ({
          name: f.name,
          reason: '当前模型不支持图片上传',
        }))
      )
    }
  }

  // 检查视频文件
  if (videoFiles.length > 0) {
    if (canUploadVideos) {
      filesToProcess.push(...videoFiles)
    } else {
      unsupportedFiles.push(
        ...videoFiles.map(f => ({
          name: f.name,
          reason: '当前不支持视频上传',
        }))
      )
    }
  }

  // 检查文档文件
  if (documentFiles.length > 0) {
    if (canUploadDocuments) {
      filesToProcess.push(...documentFiles)
    } else {
      unsupportedFiles.push(
        ...documentFiles.map(f => ({
          name: f.name,
          reason: '当前模型不支持文档上传',
        }))
      )
    }
  }

  // 处理不支持的文件提示
  if (unsupportedFiles.length > 0) {
    // 按类型分组
    const imageCount = unsupportedFiles.filter(f =>
      f.name.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/i)
    ).length
    const videoCount = unsupportedFiles.filter(f =>
      f.name.match(/\.(mp4|webm|ogg|mov|avi)$/i)
    ).length
    const docCount = unsupportedFiles.length - imageCount - videoCount

    // 生成提示消息
    const messages = []
    if (imageCount > 0) messages.push(`${imageCount} 张图片`)
    if (videoCount > 0) messages.push(`${videoCount} 个视频`)
    if (docCount > 0) messages.push(`${docCount} 个文档`)

    if (messages.length > 0) {
      ms.warning(`无法上传 ${messages.join('、')}，当前不支持`)
    }
  }

  // 处理可以上传的文件
  for (const file of filesToProcess) {
    if (file.type.startsWith('image/')) {
      await processImageFile(file)
    } else if (file.type.startsWith('video/')) {
      await processVideoFile(file)
    } else {
      await processDocumentFile(file)
    }
  }
}

// 添加一个计算属性来判断是否显示上传按钮
const showUploadButton = computed(() => {
  return isFilesModel.value || isImageModel.value
})

// 计算上传按钮的提示文本
const uploadButtonTooltip = computed(() => {
  const canUploadFiles = isFilesModel.value
  const canUploadImages = isImageModel.value
  const canUploadVideos = true // 视频上传默认支持

  const supportedTypes = []
  if (canUploadFiles) supportedTypes.push('文件')
  if (canUploadImages) supportedTypes.push('图片')
  if (canUploadVideos) supportedTypes.push('视频')

  if (supportedTypes.length > 1) {
    return `上传${supportedTypes.join('、')}`
  } else if (supportedTypes.length === 1) {
    return `上传${supportedTypes[0]}`
  }
  return '上传'
})

// 添加计算属性来控制按钮文字的显示
const shouldShowButtonText = computed(() => {
  return availableWidth.value > 300 // 当宽度大于300px时显示按钮文字
})

// 统一的图片模型判断 - 只要drawingType不为0就显示尺寸选择器
const isImageGenerationModel = computed(
  () =>
    isDalleModel.value ||
    isMidjourneyModel.value ||
    isFluxModel.value ||
    isGptImage1Model.value ||
    (usingPlugin.value?.drawingType ?? 0) !== 0
)
</script>

<template>
  <div>
    <!-- 移除全屏蒙版 -->

    <!-- before-footer slot -->
    <slot name="before-footer"></slot>

    <!-- Main footer content -->
    <div
      class="flex flex-col items-center justify-center w-full"
      :class="[isMobile ? 'px-3 pb-3' : 'px-2']"
    >
      <footer
        ref="footerRef"
        class="flex flex-col items-center justify-center w-full bg-transparent"
        :class="[dataSources.length > 0 ? 'max-w-4xl' : 'max-w-3xl']"
        @dragover="handleDragOver"
        @dragleave="handleDragLeave"
        @drop="handleDrop"
      >
        <div class="flex justify-center w-full flex-col resize-none">
          <!-- <div
            class="absolute right-0 bottom-full left-0 h-10 bg-gradient-to-b from-transparent to-white/80 dark:to-gray-750/80"
            style="transition: opacity 0.3s ease"
          ></div> -->
          <ToolbarOptions
            :is-dalle-model="isDalleModel"
            :is-midjourney-model="isMidjourneyModel"
            :is-flux-model="isFluxModel"
            :is-gpt-image1-model="isGptImage1Model"
            :is-mermaid-model="isMermaidModel"
            :is-image-generation-model="isImageGenerationModel"
            :image-sizes="imageSize"
            :mj-versions="mjVersions"
            :mj-url="mjUrl"
            :gpt-image1-backgrounds="gptImage1Backgrounds"
            :global-config="globalConfig"
            :selected-size="selectedSize"
            :selected-mj-url="selectedMjUrl"
            :selected-mj-version="selectedMjVersion"
            :selected-gpt-image1-background="selectedGptImage1Background"
            @update:selected-size="switchSize"
            @update:selected-mj-url="switchMjUrl"
            @update:selected-mj-version="switchMjVersion"
            @update:selected-gpt-image1-background="switchGptImage1Background"
            @append-style="appendStyleToInput"
            @update-random-styles="handleToolbarOptionsUpdate"
          />
        </div>

        <div
          class="flex w-full border border-gray-400 dark:border-gray-700 hover:ring-1 hover:ring-primary-500 dark:hover:ring-primary-500 focus-within:ring-1 focus-within:ring-primary-500 dark:focus-within:ring-primary-500 justify-center items-center flex-col rounded-3xl resize-none px-2 transition-all duration-200"
          :class="{
            'ring-1 ring-primary-500 dark:ring-primary-500': isDragging,
            'bg-gray-50 dark:bg-gray-700/80': isFileDraggingOverPage,
          }"
          :style="{ minHeight: '1.5rem', position: 'relative' }"
        >
          <!-- 移除多余的内部提示层 -->

          <div
            v-if="showSuggestions && !isSelectedApp && !usingPlugin && searchResults.length !== 0"
            class="w-full z-50 bg-white my-2 px-1 py-1 justify-center items-center flex-col rounded-2xl resize-none dark:bg-gray-800 border border-gray-400 dark:border-gray-700"
            :style="{
              minHeight: '1.5rem',
              position: 'absolute',
              top: props.dataSourcesLength || isMobile ? 'auto' : '100%',
              bottom: props.dataSourcesLength || isMobile ? '100%' : 'auto',
              left: '50%',
              transform: 'translateX(-50%)',
            }"
          >
            <div
              v-if="searchResults.length !== 0"
              v-for="app in searchResults"
              :key="app.id"
              @click="selectApp(app)"
              class="flex items-center bg-white dark:bg-gray-800 hover:bg-opacity py-2 px-2 dark:hover:bg-gray-700 rounded-2xl w-full cursor-pointer duration-150 ease-in-out"
            >
              <div
                class="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center overflow-hidden shadow-sm border border-gray-300 mr-3"
              >
                <img
                  v-if="app.coverImg"
                  :src="app.coverImg"
                  alt="Cover Image"
                  class="w-8 h-8 rounded-full flex justify-start"
                />
                <span
                  v-else
                  class="w-8 h-8 text-base font-medium text-gray-700 dark:text-gray-400 rounded-full flex items-center justify-center dark:bg-gray-700"
                >
                  {{ app.name.charAt(0) }}
                </span>
              </div>

              <h3 class="text-md font-bold text-gray-600 dark:text-primary-500 mr-3 flex-shrink-0">
                {{ app.name }}
              </h3>
              <p class="text-base text-gray-400 dark:text-gray-400 flex-grow truncate">
                {{ app.des }}
              </p>
            </div>
          </div>

          <FilePreview
            :data-base64-list="dataBase64List"
            :file-list="fileList"
            :saved-files="savedFiles"
            :is-selected-app="isSelectedApp"
            :using-plugin="usingPlugin"
            :selected-app="selectedApp"
            @clear-data="clearData"
            @clear-select-app="clearSelectApp"
          />
          <!-- 渐变阴影效果 -->

          <div class="relative w-full">
            <!-- 扩展按钮 - 只在内容高度超过阈值时显示 -->
            <div v-if="shouldShowExpandButton" class="absolute right-1 top-2 z-10 group">
              <button
                @click="toggleExpanded"
                class="btn-pill btn-sm"
                :aria-label="isExpanded ? '收起输入框' : '展开输入框'"
              >
                <OffScreen v-if="isExpanded" size="15" />
                <FullScreen v-else size="15" />
              </button>
              <div v-if="!isMobile" class="tooltip tooltip-bottom">
                {{ isExpanded ? '收起' : '展开' }}
              </div>
            </div>

            <!-- 拖拽提示覆盖层 - 替代文本区域 -->
            <div
              v-if="isFileDraggingOverPage"
              class="h-20 flex items-center justify-center my-2 w-full"
            >
              <div class="flex flex-col items-center justify-center">
                <AddPicture
                  size="28"
                  class="mb-2"
                  :class="
                    isDragging
                      ? 'text-primary-600 dark:text-primary-400'
                      : 'text-gray-500 dark:text-gray-400'
                  "
                />
                <p
                  class="text-center text-sm"
                  :class="
                    isDragging
                      ? 'text-primary-600 dark:text-primary-400 font-medium'
                      : 'text-gray-500 dark:text-gray-400'
                  "
                >
                  {{ isDragging ? '松开鼠标开始上传' : '拖放文件到这里上传' }}
                </p>
              </div>
            </div>

            <!-- 文本区域 - 非拖拽状态显示 -->
            <textarea
              v-show="!isFileDraggingOverPage"
              ref="inputRef"
              v-model="prompt"
              :placeholder="placeholderText"
              class="flex flex-grow items-center justify-center mt-3 mb-2 w-full placeholder:text-gray-400 dark:placeholder:text-gray-500 text-base resize-none dark:text-gray-400 px-2 bg-transparent custom-scrollbar transition-all duration-300 ease-in-out"
              @input="autoResize"
              @keypress="handleEnter"
              @keyup="handleInput"
              @paste="handlePaste"
              :style="{
                maxHeight: isExpanded ? 'none' : '30vh',
                minHeight: '4rem',
              }"
              aria-label="聊天消息输入框"
              role="textbox"
            ></textarea>
          </div>

          <!-- 按钮容器 -->
          <div ref="buttonContainerRef" class="flex justify-between flex-grow w-full pb-2">
            <!-- 文件上传按钮区域 -->
            <div class="flex justify-start items-center">
              <!-- 统一的文件/图片上传按钮 -->
              <div
                v-if="showUploadButton && !isUploading"
                class="group relative"
                @dragover.prevent="
                  e => {
                    e.stopPropagation()
                    isDragging = true
                  }
                "
                @dragleave.prevent="
                  e => {
                    e.stopPropagation()
                    isDragging = false
                  }
                "
                @drop.prevent="
                  e => {
                    e.stopPropagation()
                    isDragging = false
                    isFileDraggingOverPage = false
                    handleUnifiedFileDrop(e, 'button')
                  }
                "
              >
                <button
                  type="button"
                  class="btn-pill mx-1"
                  @click="triggerUpload"
                  :aria-label="uploadButtonTooltip"
                >
                  <Plus size="15" />
                </button>
                <div v-if="!isMobile" class="tooltip tooltip-top">{{ uploadButtonTooltip }}</div>
              </div>

              <LoadingFour
                v-if="isUploading"
                size="15"
                class="p-1 mx-2 animate-rotate text-gray-500 dark:text-gray-500"
              />

              <!-- 隐藏的文件输入框 - 保持原有的两个input，但共用同一个按钮触发 -->
              <input ref="fileInput" type="file" class="hidden" @change="handleFileSelect" />
              <input
                ref="imageInput"
                type="file"
                accept="image/*"
                class="hidden"
                @change="handleImageSelect"
              />

              <div v-if="shouldShowDeepThinking" class="group relative">
                <div
                  class="btn-pill btn-md mx-1"
                  :class="[usingDeepThinking ? 'btn-pill-active' : '']"
                  @click="usingDeepThinking = !usingDeepThinking"
                  role="button"
                  :aria-pressed="usingDeepThinking"
                  aria-label="启用或禁用推理功能"
                  tabindex="0"
                >
                  <TwoEllipses size="15" />
                  <span v-if="shouldShowButtonText" class="ml-1">推理</span>
                </div>
                <div v-if="!isMobile" class="tooltip tooltip-top">
                  AI 推理能力，帮助寻找更深层次的答案
                </div>
              </div>

              <div v-if="shouldShowNetworkSearch" class="group relative">
                <div
                  class="btn-pill btn-md mx-1"
                  :class="[usingNetwork ? 'btn-pill-active' : '']"
                  @click="usingNetwork = !usingNetwork"
                  role="button"
                  :aria-pressed="usingNetwork"
                  aria-label="启用或禁用网络搜索"
                  tabindex="0"
                >
                  <Sphere size="15" />
                  <span v-if="shouldShowButtonText" class="ml-1">搜索</span>
                </div>
                <div v-if="!isMobile" class="tooltip tooltip-top">启用网络搜索，获取最新信息</div>
              </div>

              <div v-if="shouldShowMcpTool" class="group relative">
                <div
                  class="btn-pill btn-md mx-1"
                  :class="[usingMcpTool ? 'btn-pill-active' : '']"
                  @click="usingMcpTool = !usingMcpTool"
                  role="button"
                  :aria-pressed="usingMcpTool"
                  aria-label="启用或禁用工具功能"
                  tabindex="0"
                >
                  <TreasureChest size="15" />
                  <span v-if="shouldShowButtonText" class="ml-1">工具</span>
                </div>
                <div v-if="!isMobile" class="tooltip tooltip-top">
                  调用 MCP 工具，解决更多复杂问题
                </div>
              </div>
            </div>

            <div class="flex justify-end items-center mr-1">
              <!-- 当不在加载状态时显示这个按钮，用于提交 -->
              <div v-if="!isStreamIn" class="group relative">
                <button
                  type="button"
                  class="btn-send"
                  :class="{ 'opacity-60 cursor-not-allowed': buttonDisabled, 'h-8 w-8': isMobile }"
                  :disabled="buttonDisabled"
                  @click="handleSubmit()"
                  aria-label="发送消息"
                >
                  <SendOne size="15" />
                </button>
                <div v-if="!isMobile" class="tooltip tooltip-top">发送</div>
              </div>

              <!-- 当在加载状态时显示这个按钮，用于停止 -->
              <div v-if="isStreamIn" class="group relative">
                <button
                  type="button"
                  class="btn-stop"
                  :class="{ 'h-8 w-8': isMobile }"
                  @click="handleStop()"
                  aria-label="停止生成"
                >
                  <Square size="16" />
                </button>
                <div v-if="!isMobile" class="tooltip tooltip-top">停止生成</div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>

    <!-- after-footer slot -->
    <slot name="after-footer"></slot>
  </div>
</template>
