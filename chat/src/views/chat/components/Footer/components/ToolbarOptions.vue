<script setup lang="ts">
import { DropdownMenu } from '@/components/common/DropdownMenu'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'
import { useGlobalStoreWithOut } from '@/store'
import { ShuffleOne } from '@icon-park/vue-next'
import { computed, ref } from 'vue'

interface SizeOption {
  id: string
  title: string
  values: string
  aspectRatio: string
}

interface VersionOption {
  title: string
  values: string
}

interface UrlOption {
  title: string
  values: string
}

interface QualityOption {
  title: string
  values: string
}

interface CompressionOption {
  title: string
  values: string
}

interface BackgroundOption {
  title: string
  values: string
}

interface MermaidChartOption {
  title: string
  values: string
}

const props = defineProps({
  isDalleModel: {
    type: Boolean,
    default: false,
  },
  isMidjourneyModel: {
    type: Boolean,
    default: false,
  },
  isFluxModel: {
    type: Boolean,
    default: false,
  },
  isGptImage1Model: {
    type: Boolean,
    default: false,
  },
  isMermaidModel: {
    type: Boolean,
    default: false,
  },
  isImageGenerationModel: {
    type: Boolean,
    default: false,
  },
  imageSizes: {
    type: Array as () => SizeOption[],
    default: () => [],
  },
  mjVersions: {
    type: Array as () => VersionOption[],
    default: () => [],
  },
  mjUrl: {
    type: Array as () => UrlOption[],
    default: () => [],
  },
  gptImage1Backgrounds: {
    type: Array as () => BackgroundOption[],
    default: () => [],
  },
  globalConfig: {
    type: Object,
    default: () => ({}),
  },
  selectedSize: {
    type: Object as () => SizeOption,
    default: () => ({}) as SizeOption,
  },
  selectedMjUrl: {
    type: Object as () => UrlOption,
    default: () => ({}) as UrlOption,
  },
  selectedMjVersion: {
    type: Object as () => VersionOption,
    default: () => ({}) as VersionOption,
  },
  selectedGptImage1Background: {
    type: Object as () => BackgroundOption,
    default: () =>
      ({
        title: '自动',
        values: 'auto',
      }) as BackgroundOption,
  },
})

const emit = defineEmits<{
  'update:selectedSize': [option: SizeOption]
  'update:selectedMjUrl': [option: UrlOption]
  'update:selectedMjVersion': [option: VersionOption]
  // 'update:selectedGptImage1Quality': [option: QualityOption]
  // 'update:selectedGptImage1Compression': [option: CompressionOption]
  'update:selectedGptImage1Background': [option: BackgroundOption]
  appendStyle: [style: string]
  updateRandomStyles: [styles: string[]]
}>()

const { isMobile } = useBasicLayout()
const randomStyles = ref<string[]>([])
const randomMermaidCharts = ref<string[]>([])
const useGlobalStore = useGlobalStoreWithOut()

const isImagePreviewerVisible = computed(() => useGlobalStore.showImagePreviewer)

// 定义mermaid图表类型数据
const mermaidChartTypes: MermaidChartOption[] = [
  { title: '流程图', values: '生成流程图' },
  { title: '时序图', values: '生成时序图' },
  { title: '类图', values: '生成类图' },
  { title: '状态图', values: '生成状态图' },
  { title: '实体关系图', values: '生成实体关系图' },
  { title: '用户旅程图', values: '生成用户旅程图' },
  { title: '甘特图', values: '生成甘特图' },
  { title: '饼图', values: '生成饼图' },
  { title: '象限图', values: '生成象限图' },
  { title: '需求图', values: '生成需求图' },
  { title: 'Git图', values: '生成Git图' },
  { title: 'C4图', values: '生成C4图' },
  { title: '思维导图', values: '生成思维导图' },
  { title: '时间线图', values: '生成时间线图' },
  { title: '桑基图', values: '生成桑基图' },
  { title: 'XY图', values: '生成XY图' },
  { title: '框图', values: '生成框图' },
]
// 初始化randomStyles或者在需要时更新它
const updateRandomStyles = () => {
  const stylesArray = props.globalConfig?.drawingStyles?.split(',') || []
  const shuffled = stylesArray.sort(() => 0.5 - Math.random())
  // 如果是移动端，只显示3个；否则显示5个
  const displayCount = 3
  randomStyles.value = shuffled.slice(0, displayCount)
  emit('updateRandomStyles', randomStyles.value)
}

// 初始化randomMermaidCharts或者在需要时更新它
const updateRandomMermaidCharts = () => {
  const shuffled = [...mermaidChartTypes].sort(() => 0.5 - Math.random())
  // 每次显示5个图表类型
  const displayCount = 5
  randomMermaidCharts.value = shuffled.slice(0, displayCount).map(chart => chart.title)
}

// 通过标题查找对应的mermaid图表模板
const appendMermaidChartToInput = (chartTitle: string) => {
  const chart = mermaidChartTypes.find(c => c.title === chartTitle)
  if (chart) {
    emit('appendStyle', chart.values)
  }
}

const switchSize = (option: SizeOption) => {
  emit('update:selectedSize', option)
}

const switchMjVersion = (option: VersionOption) => {
  emit('update:selectedMjVersion', option)
}

const switchMjUrl = (option: UrlOption) => {
  emit('update:selectedMjUrl', option)
}

// const switchGptImage1Quality = (option: QualityOption) => {
//   emit('update:selectedGptImage1Quality', option)
// }

// const switchGptImage1Compression = (option: CompressionOption) => {
//   emit('update:selectedGptImage1Compression', option)
// }

const switchGptImage1Background = (option: BackgroundOption) => {
  console.log('Switching background to:', option)
  emit('update:selectedGptImage1Background', option)
}

const appendStyleToInput = (style: string) => {
  emit('appendStyle', style)
}

const getAspectRatioStyle = (aspectRatioString: string, fixedSize = 16) => {
  const [height, width] = aspectRatioString.split(' / ').map(Number)
  const aspectRatio = height / width

  // 当宽度大于高度时，固定高度，动态调整宽度
  if (width > height) {
    return {
      width: `${fixedSize * aspectRatio}px`, // 宽度根据比例动态调整
      height: `${fixedSize}px`, // 固定高度
    }
  }
  // 当高度大于或等于宽度时，固定宽度，动态调整高度
  else {
    return {
      width: `${fixedSize}px`, // 固定宽度
      height: `${fixedSize / aspectRatio}px`, // 高度根据比例动态调整
    }
  }
}

// 确保在组件挂载时检查并初始化背景选项
const ensureBackgroundOption = () => {
  if (!props.selectedGptImage1Background || !props.selectedGptImage1Background.title) {
    // 如果没有提供有效的背景选项，设置默认值
    if (props.gptImage1Backgrounds && props.gptImage1Backgrounds.length > 0) {
      emit('update:selectedGptImage1Background', props.gptImage1Backgrounds[0])
    }
  }
}

// 初始化随机风格
updateRandomStyles()
// 初始化随机mermaid图表
updateRandomMermaidCharts()
// 初始化背景选项
ensureBackgroundOption()

// 添加下拉菜单状态管理
const dropdownStates = ref({
  mjUrl: false,
  mjVersion: false,
  imageSize: false,
  gptImage1Background: false,
})
</script>

<template>
  <div class="flex justify-between items-end w-full bg-transparent">
    <!-- 绘画风格按钮 -->
    <div
      v-if="
        (isDalleModel || isMidjourneyModel || isFluxModel || isGptImage1Model) &&
        !isMobile &&
        !isImagePreviewerVisible
      "
      class="flex flex-nowrap scrollbar-hide ml-2 overflow-x-auto mt-1 mb-2"
      style="
        scrollbar-width: none;
        -ms-overflow-style: none;
        ::-webkit-scrollbar {
          display: none;
        }
      "
    >
      <button
        v-for="(style, index) in randomStyles"
        :key="index"
        @click="appendStyleToInput(style)"
        class="btn btn-secondary btn-md mx-1 whitespace-nowrap"
      >
        {{ style }}
      </button>

      <button @click="updateRandomStyles" class="btn btn-secondary btn-md mx-1 whitespace-nowrap">
        <ShuffleOne theme="outline" size="16" class="" />
      </button>
    </div>

    <!-- Mermaid图表类型按钮 -->
    <div
      v-if="isMermaidModel && !isMobile && !isImagePreviewerVisible"
      class="flex flex-nowrap scrollbar-hide ml-2 overflow-x-auto mt-1 mb-2"
      style="
        scrollbar-width: none;
        -ms-overflow-style: none;
        ::-webkit-scrollbar {
          display: none;
        }
      "
    >
      <button
        v-for="(chartType, index) in randomMermaidCharts"
        :key="index"
        @click="appendMermaidChartToInput(chartType)"
        class="btn btn-secondary btn-md mx-1 whitespace-nowrap"
      >
        {{ chartType }}
      </button>

      <button
        @click="updateRandomMermaidCharts"
        class="btn btn-secondary btn-md mx-1 whitespace-nowrap"
      >
        <ShuffleOne theme="outline" size="16" class="" />
      </button>
    </div>
    <div class="flex-1 bg-transparent"></div>
    <div class="menu-container bg-transparent" style="display: flex; justify-content: flex-end">
      <DropdownMenu
        v-if="isMidjourneyModel"
        v-model="dropdownStates.mjUrl"
        position="top-right"
        min-width="112px"
        class="relative inline-block text-left group mr-3 mt-1 mb-2"
      >
        <template #trigger>
          <div
            class="btn btn-secondary btn-md inline-flex w-full justify-center whitespace-nowrap cursor-pointer"
          >
            {{ selectedMjUrl.title }}
          </div>
        </template>

        <template #menu="{ close }">
          <div>
            <div
              v-for="(option, index) in mjUrl"
              :key="index"
              @click="
                () => {
                  switchMjUrl(option)
                  close()
                }
              "
              class="menu-item menu-item-md"
            >
              {{ option.title }}
            </div>
          </div>
        </template>
      </DropdownMenu>
      <DropdownMenu
        v-if="isMidjourneyModel"
        v-model="dropdownStates.mjVersion"
        position="top-right"
        min-width="112px"
        class="relative inline-block text-left group mr-3 mt-1 mb-2"
      >
        <template #trigger>
          <div
            class="btn btn-secondary btn-md inline-flex w-full justify-center whitespace-nowrap cursor-pointer"
          >
            版本：{{ selectedMjVersion.title }}
          </div>
        </template>

        <template #menu="{ close }">
          <div>
            <div
              v-for="(option, index) in mjVersions"
              :key="index"
              @click="
                () => {
                  switchMjVersion(option)
                  close()
                }
              "
              class="menu-item menu-item-md"
            >
              {{ option.title }}
            </div>
          </div>
        </template>
      </DropdownMenu>

      <!-- GPT Image 1 质量选项 -->
      <!-- <Menu
        v-if="isGptImage1Model"
        as="div"
        class="relative hover:bg-opacity dark:hover:bg-gray-750 inline-block text-left group shadow-sm rounded-md ring-1 ring-gray-300 dark:ring-gray-600 dark:bg-gray-800 mr-3  py-1 px-2"
      >
        <MenuButton
          class="inline-flex w-full justify-center rounded-md text-base text-gray-500 group-hover:text-gray-700 dark:text-gray-500 dark:group-hover:text-gray-300 whitespace-nowrap"
        >
          质量：{{ selectedGptImage1Quality.title }}
        </MenuButton>

        <transition
          enter-active-class="transition ease-out duration-100"
          enter-from-class="transform opacity-0 scale-95"
          enter-to-class="transform opacity-100 scale-100"
          leave-active-class="transition ease-in duration-75"
          leave-from-class="transform opacity-100 scale-100"
          leave-to-class="transform opacity-0 scale-95"
        >
          <MenuItems
            class="absolute right-0 bottom-full mb-2 w-28 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-gray-750 dark:text-gray-400"
          >
            <div class="py-1">
              <MenuItem v-for="(option, index) in gptImage1Qualities" :key="index">
                <div class="group flex items-center" @click="switchGptImage1Quality(option)">
                  <a
                    href="#"
                    class="flex w-full items-center px-3 py-1 text-base hover:bg-opacity dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  >
                    {{ option.title }}
                  </a>
                </div>
              </MenuItem>
            </div>
          </MenuItems>
        </transition>
      </Menu> -->

      <!-- GPT Image 1 压缩选项 -->
      <!-- <Menu
        v-if="isGptImage1Model"
        as="div"
        class="relative hover:bg-opacity dark:hover:bg-gray-750 inline-block text-left group shadow-sm rounded-md ring-1 ring-gray-300 dark:ring-gray-600 dark:bg-gray-800 mr-3  py-1 px-2"
      >
        <MenuButton
          class="inline-flex w-full justify-center rounded-md text-base text-gray-500 group-hover:text-gray-700 dark:text-gray-500 dark:group-hover:text-gray-300 whitespace-nowrap"
        >
          压缩：{{ selectedGptImage1Compression.title }}
        </MenuButton>

        <transition
          enter-active-class="transition ease-out duration-100"
          enter-from-class="transform opacity-0 scale-95"
          enter-to-class="transform opacity-100 scale-100"
          leave-active-class="transition ease-in duration-75"
          leave-from-class="transform opacity-100 scale-100"
          leave-to-class="transform opacity-0 scale-95"
        >
          <MenuItems
            class="absolute right-0 bottom-full mb-2 w-28 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-gray-750 dark:text-gray-400"
          >
            <div class="py-1">
              <MenuItem v-for="(option, index) in gptImage1Compressions" :key="index">
                <div class="group flex items-center" @click="switchGptImage1Compression(option)">
                  <a
                    href="#"
                    class="flex w-full items-center px-3 py-1 text-base hover:bg-opacity dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
                  >
                    {{ option.title }}
                  </a>
                </div>
              </MenuItem>
            </div>
          </MenuItems>
        </transition>
      </Menu> -->

      <!-- GPT Image 1 背景选项 -->
      <DropdownMenu
        v-if="isGptImage1Model"
        v-model="dropdownStates.gptImage1Background"
        position="top-right"
        min-width="112px"
        class="relative inline-block text-left group mr-3 mt-1 mb-2"
      >
        <template #trigger>
          <div
            class="btn btn-secondary btn-md inline-flex w-full justify-center whitespace-nowrap cursor-pointer"
          >
            背景：{{ selectedGptImage1Background.title }}
          </div>
        </template>

        <template #menu="{ close }">
          <div>
            <div
              v-for="(option, index) in gptImage1Backgrounds"
              :key="index"
              @click="
                () => {
                  switchGptImage1Background(option)
                  close()
                }
              "
              class="menu-item menu-item-md"
            >
              {{ option.title }}
            </div>
          </div>
        </template>
      </DropdownMenu>

      <!-- 统一的图片尺寸选择器 - 适用于所有图片生成模型 -->
      <DropdownMenu
        v-if="isImageGenerationModel"
        v-model="dropdownStates.imageSize"
        position="top-right"
        min-width="160px"
        class="relative inline-block text-left group mr-3 mt-1 mb-2"
      >
        <template #trigger>
          <div
            class="btn btn-secondary btn-md inline-flex w-full justify-center whitespace-nowrap cursor-pointer"
          >
            {{ t('chat.size') + selectedSize.title }}
          </div>
        </template>

        <template #menu="{ close }">
          <div>
            <div
              v-for="(option, index) in imageSizes"
              :key="index"
              @click="
                () => {
                  switchSize(option)
                  close()
                }
              "
              class="menu-item menu-item-md flex items-center"
            >
              <div class="flex flex-1 justify-center mr-3">
                <div
                  :style="getAspectRatioStyle(option.aspectRatio)"
                  class="flex border border-gray-500 dark:border-gray-300 rounded-sm"
                ></div>
              </div>
              <div class="w-28">
                {{ option.title }}
              </div>
            </div>
          </div>
        </template>
      </DropdownMenu>
    </div>
  </div>
</template>
