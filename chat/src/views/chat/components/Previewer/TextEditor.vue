<script setup lang="ts">
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useGlobalStoreWithOut } from '@/store'
import { message } from '@/utils/message'
import { cpp } from '@codemirror/lang-cpp' // C++语法高亮
import { css } from '@codemirror/lang-css' // CSS语法高亮
import { html } from '@codemirror/lang-html' // HTML语法高亮
import { java } from '@codemirror/lang-java' // Java语法高亮
import { javascript } from '@codemirror/lang-javascript' // JavaScript语法高亮
import { json } from '@codemirror/lang-json' // JSON语法高亮
import { markdown } from '@codemirror/lang-markdown' // Markdown语法高亮
import { php } from '@codemirror/lang-php' // PHP语法高亮
import { python } from '@codemirror/lang-python' // Python语法高亮
import { rust } from '@codemirror/lang-rust' // Rust语法高亮
import { sql } from '@codemirror/lang-sql' // SQL语法高亮
import { EditorState } from '@codemirror/state'
import { oneDark } from '@codemirror/theme-one-dark'
import { Close, Copy, Download } from '@icon-park/vue-next'
import { EditorView, basicSetup } from 'codemirror'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

interface Props {
  close: () => void
}
const props = defineProps<Props>()

const globalStore = useGlobalStoreWithOut()
const ms = message()

// 编辑器相关
const editorContainerRef = ref<HTMLDivElement | null>(null)
const isDarkMode = computed(() => document.documentElement.classList.contains('dark'))
let editor: EditorView | null = null

// 流式输入状态
const isStreamIn = computed(() => false) // 可根据需要修改
const { isMobile } = useBasicLayout()
// 内容管理
const textContent = computed(() => {
  return globalStore.textContent || ''
})

// 语言类型
const languageType = ref('markdown')

// 行号和内容信息
const linesInfo = ref<{ [key: number]: string }>({})

// 本地编辑内容
const localEditableText = ref(textContent.value)

// 自动识别内容的语言类型
const detectLanguage = (content: string): string => {
  // 预处理：去除前后空白，获取前几行用于检测
  const trimmedContent = content.trim()
  const firstLines = trimmedContent.split('\n').slice(0, 10).join('\n')

  // 检测Python
  if (
    /^\s*(import|from|def|class|if __name__ == ['"]__main__['"]:|#\s*coding[:=]|#!\/usr)/i.test(
      firstLines
    ) ||
    /\bdef\s+\w+\s*\(.*\):|for\s+\w+\s+in\s+|while\s+.*:/i.test(firstLines)
  ) {
    return 'python'
  }

  // 检测JavaScript/TypeScript
  if (
    /^\s*(import|export|const|let|var|function|class|interface|type)\s+/m.test(firstLines) ||
    /^\s*\/\/.*|\/\*[\s\S]*?\*\/|console\.log|document\.|window\.|=>|async|await/m.test(
      firstLines
    ) ||
    /\.then\s*\(|\.catch\s*\(/m.test(firstLines)
  ) {
    // 检查是否可能是TypeScript
    if (/:\s*(string|number|boolean|any|void|interface|type|Record<|Partial<)/m.test(firstLines)) {
      return 'typescript'
    }
    return 'javascript'
  }

  // 检测HTML
  if (
    /^\s*<!DOCTYPE html>|<html|<head|<body|<div|<span|<p>|<script|<style/i.test(firstLines) ||
    /<\/\w+>/.test(firstLines)
  ) {
    return 'html'
  }

  // 检测CSS
  if (
    /^\s*(\.|#|\*)\s*\{|@media|@import|@keyframes|body\s*\{|margin:|padding:|color:/i.test(
      firstLines
    ) ||
    /display:\s*(flex|block|inline|grid)|position:\s*(absolute|relative|fixed)/i.test(firstLines)
  ) {
    return 'css'
  }

  // 检测JSON
  if (/^\s*(\{|\[)/.test(trimmedContent) && /(\}|\])\s*$/.test(trimmedContent)) {
    try {
      JSON.parse(trimmedContent)
      return 'json'
    } catch (e) {
      // 不是有效的JSON
    }
  }

  // 检测SQL
  if (
    /^\s*(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP|TRUNCATE|GRANT|REVOKE)\s+/i.test(
      firstLines
    ) ||
    /\bFROM\b.*\bWHERE\b|\bGROUP BY\b|\bORDER BY\b|\bLIMIT\b/i.test(firstLines)
  ) {
    return 'sql'
  }

  // 检测Java
  if (
    /^\s*(public|private|protected)\s+(class|interface|enum)|@Override|import\s+java\./i.test(
      firstLines
    ) ||
    /\bextends\b|\bimplements\b|System\.out\.print/i.test(firstLines)
  ) {
    return 'java'
  }

  // 检测C/C++
  if (
    /^\s*(#include|#define|#ifndef|#endif)|int\s+main\s*\(|struct\s+\w+\s*\{|namespace\s+/i.test(
      firstLines
    ) ||
    /\bstd::|new\s+\w+\s*\(|\bdelete\b|\btemplate\b<|::\b/i.test(firstLines)
  ) {
    return 'cpp'
  }

  // 检测PHP
  if (/^\s*(<\?php|\?>)|echo\s+['"]|\$\w+\s*=|function\s+\w+\s*\(/i.test(firstLines)) {
    return 'php'
  }

  // 检测Rust
  if (
    /^\s*(fn\s+\w+|struct\s+\w+|enum\s+\w+|impl\s+|use\s+|pub\s+|let\s+mut\s+|match\s+)/i.test(
      firstLines
    ) ||
    /\b(Option<|Result<|Vec<|String::|\bRust\b)/i.test(firstLines)
  ) {
    return 'rust'
  }

  // 检测Markdown（默认选项）
  if (
    /^\s*(#{1,6}\s+|\*\s+|-\s+|\d+\.\s+|>|```|---|===|!\[.*?\]\(.*?\)|\[.*?\]\(.*?\))/m.test(
      firstLines
    ) ||
    /\*\*.*?\*\*|__.*?__|_.*?_|\*.*?\*/m.test(firstLines)
  ) {
    return 'markdown'
  }

  // 默认返回markdown
  return 'markdown'
}

// 获取对应语言的语法高亮扩展
const getLanguageExtension = (lang: string) => {
  switch (lang) {
    case 'javascript':
    case 'typescript':
      return javascript()
    case 'python':
      return python()
    case 'html':
      return html()
    case 'css':
      return css()
    case 'json':
      return json()
    case 'sql':
      return sql()
    case 'java':
      return java()
    case 'cpp':
      return cpp()
    case 'php':
      return php()
    case 'rust':
      return rust()
    case 'markdown':
    default:
      return markdown()
  }
}

// 初始化 CodeMirror 编辑器
const initializeEditor = () => {
  if (!editorContainerRef.value || editor) return // 防止重新初始化

  // 检测初始内容的语言类型
  languageType.value = detectLanguage(localEditableText.value)

  const extensions = [
    basicSetup,
    getLanguageExtension(languageType.value), // 使用检测到的语言
    EditorView.lineWrapping,
    EditorView.updateListener.of(update => {
      if (update.docChanged) {
        const newContent = update.state.doc.toString()
        localEditableText.value = newContent

        // 当内容发生较大变化时，重新检测语言类型
        if (Math.abs(newContent.length - localEditableText.value.length) > 50) {
          const newLang = detectLanguage(newContent)
          if (newLang !== languageType.value) {
            languageType.value = newLang
            // 重新配置编辑器以应用新的语言高亮
            reconfigureEditor(newLang)
          }
        }

        // 更新全局 store
        globalStore.updateTextContent(newContent)
        // 更新行号和内容信息
        updateLinesInfo(newContent)
      }
    }),
  ]

  if (isDarkMode.value) {
    extensions.push(oneDark)
  }

  const state = EditorState.create({
    doc: localEditableText.value,
    extensions,
  })

  editor = new EditorView({ state, parent: editorContainerRef.value })
}

// 重新配置编辑器以应用新的语言高亮
const reconfigureEditor = (lang: string) => {
  if (!editor) return

  const content = editor.state.doc.toString()

  // 创建包含新语言高亮的状态
  const extensions = [
    basicSetup,
    getLanguageExtension(lang),
    EditorView.lineWrapping,
    EditorView.updateListener.of(update => {
      if (update.docChanged) {
        const newContent = update.state.doc.toString()
        localEditableText.value = newContent
        // 更新全局 store
        globalStore.updateTextContent(newContent)
        // 更新行号和内容信息
        updateLinesInfo(newContent)
      }
    }),
  ]

  if (isDarkMode.value) {
    extensions.push(oneDark)
  }

  const state = EditorState.create({
    doc: content,
    extensions,
  })

  // 用新状态重新创建编辑器
  editor.setState(state)
}

// 更新行号和内容信息
const updateLinesInfo = (content: string) => {
  const lines = content.split('\n')
  const info: { [key: number]: string } = {}

  lines.forEach((line, index) => {
    info[index + 1] = line // 行号从1开始
  })

  linesInfo.value = info
}

// 兼容性更好的剪贴板复制方法
const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    return false
  }
}

// 复制编辑器内容
const copyContent = async () => {
  const content = localEditableText.value
  if (!content) return

  const success = await copyToClipboard(content)
  if (success) {
    ms.success('内容已复制到剪贴板')
  } else {
    ms.error('复制失败')
  }
}

// 导出文本为TXT文件
const downloadContent = () => {
  const content = localEditableText.value
  if (!content) {
    ms.warning('没有内容可导出')
    return
  }

  const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'exported-text.txt'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ms.success('内容已导出为TXT文件')
}

// 导出行号格式化内容
const exportFormattedLines = () => {
  const formattedContent = getFormattedLinesInfo()
  if (!formattedContent) {
    ms.warning('没有内容可导出')
    return
  }

  const blob = new Blob([formattedContent], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = 'line-numbered-text.txt'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
  ms.success('带行号内容已导出为TXT文件')
}

// 获取格式化的行号和内容
const getFormattedLinesInfo = () => {
  let result = ''
  Object.entries(linesInfo.value).forEach(([lineNum, content]) => {
    result += `[${lineNum}]{${content}}\n`
  })
  return result
}

// 监听文本内容变化
watch(textContent, newVal => {
  if (newVal !== localEditableText.value) {
    localEditableText.value = newVal

    // 当内容发生较大变化时，重新检测语言
    const newLang = detectLanguage(newVal)
    if (newLang !== languageType.value) {
      languageType.value = newLang
    }

    if (editor) {
      // 如果语言变更，则重新配置编辑器
      if (newLang !== languageType.value) {
        reconfigureEditor(newLang)
      } else {
        // 否则只更新内容
        editor.dispatch({
          changes: { from: 0, to: editor.state.doc.length, insert: newVal || '' },
        })
      }
    }
    updateLinesInfo(newVal)
  }
})

// 监听全局store中的full_json变化
const fullJson = computed(() => globalStore.full_json)
watch(fullJson, newVal => {
  if (!newVal) return

  try {
    // 解析JSON
    const operationsData = JSON.parse(newVal)

    if (!operationsData.operations || !Array.isArray(operationsData.operations)) {
      console.log('无效的操作格式:', operationsData)
      return
    }

    // 操作已在chatBase.vue中处理，这里不需要重复处理
    // 此处只是为了监听变化以便将来可能需要的处理
  } catch (error) {
    console.error('解析full_json失败:', error)
  }
})

// 生命周期钩子
onMounted(() => {
  // 初始化编辑器
  nextTick(() => {
    initializeEditor()
    if (textContent.value) {
      updateLinesInfo(textContent.value)
    }
  })
})

onUnmounted(() => {
  if (editor) {
    editor.destroy()
    editor = null
  }
})

// 暴露方法给父组件
defineExpose({
  getFormattedLinesInfo,
  getContent: () => localEditableText.value,
  linesInfo,
})
</script>

<template>
  <div
    class="code-editor-container h-full flex flex-col transition-all duration-300 transform animate-fade-in"
  >
    <!-- 顶部操作栏 -->
    <div
      class="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700"
    >
      <div class="flex items-center">
        <button
          v-if="isMobile"
          @click="props.close"
          class="btn btn-ghost btn-sm mr-2"
          aria-label="返回"
        >
          <Left :size="20" />
        </button>
        <h3 class="text-base font-medium">代码编辑器</h3>
        <div class="ml-4">
          <select
            v-model="languageType"
            class="px-2 py-1 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded shadow-sm focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="javascript">JavaScript</option>
            <option value="typescript">TypeScript</option>
            <option value="python">Python</option>
            <option value="html">HTML</option>
            <option value="css">CSS</option>
            <option value="json">JSON</option>
            <option value="sql">SQL</option>
            <option value="java">Java</option>
            <option value="cpp">C++</option>
            <option value="php">PHP</option>
            <option value="rust">Rust</option>
            <option value="markdown">Markdown</option>
          </select>
        </div>
      </div>

      <div class="flex items-center">
        <button
          class="ml-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 transition-colors duration-200"
          title="复制代码"
          @click="copyContent"
        >
          <Copy size="18" />
        </button>
        <button
          class="ml-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 transition-colors duration-200"
          title="下载代码"
          @click="downloadContent"
        >
          <Download size="18" />
        </button>
        <button
          class="ml-2 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 transition-colors duration-200"
          title="关闭编辑器"
          @click="props.close"
        >
          <Close size="18" />
        </button>
      </div>
    </div>

    <!-- 编辑器区域 -->
    <div class="flex-grow overflow-hidden relative">
      <div ref="editorContainerRef" class="h-full editor-container animate-scale-in"></div>
    </div>
  </div>
</template>

<style scoped>
.code-editor-container {
  border-left: 1px solid #e5e7eb;
  background-color: #ffffff;
}

:deep(.cm-editor) {
  height: 100%;
}

:deep(.cm-scroller) {
  font-family: 'Fira Code', Monaco, Menlo, 'Ubuntu Mono', Consolas, 'Courier New', monospace;
  overflow: auto;
}

/* 暗色模式适配 */
.dark .code-editor-container {
  border-left-color: #374151;
  background-color: #1f2937;
}

/* 添加fade-in动画 */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

/* 添加scale-in动画 */
@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scale-in 0.3s ease-in-out;
}
</style>
