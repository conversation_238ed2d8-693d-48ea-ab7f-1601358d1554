<script setup lang="ts">
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useAppStore, useChatStore, useGlobalStoreWithOut } from '@/store'
import { message } from '@/utils/message'
import { Close, Copy, FullScreen, LoadingOne, OffScreen, Refresh } from '@icon-park/vue-next'
import mdKatex from '@traptitech/markdown-it-katex'
import hljs from 'highlight.js'
import MarkdownIt from 'markdown-it'
import mila from 'markdown-it-link-attributes'
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue'

// 引入useScroll钩子
import { useScroll } from '@/views/chat/hooks/useScroll'

interface Props {
  close: () => void
}

const props = defineProps<Props>()

const globalStore = useGlobalStoreWithOut()
const appStore = useAppStore()
const chatStore = useChatStore()
const ms = message()
const { isMobile } = useBasicLayout()

// 引入滚动相关函数
const { scrollRef, scrollToBottom, isAtBottom, handleScroll } = useScroll()

// 记录打开预览之前的侧边栏状态，用于关闭预览时恢复
const sidebarStateBeforePreview = ref(true)

// 是否处于全屏模式
const isFullscreen = ref(false)

// 预览容器的引用
const previewerContainerRef = ref<HTMLDivElement | null>(null)

// 用于MD渲染
const mdi = new MarkdownIt({
  linkify: true,
  html: true,
  highlight(code, language) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  },
})

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } })
mdi.use(mdKatex, {
  blockClass: 'katexmath-block rounded-md p-[10px]',
  inlineClass: 'katexmath-inline',
  errorColor: ' #cc0000',
})

// 计算获取工作流内容数组
const workflowContentArray = computed(() => globalStore.workflowContent)

// 链接检测正则表达式
const urlRegex = /https?:\/\/[^\s]+/g

// 格式化内容（支持URL识别和Markdown渲染）
const formatContent = (content: string) => {
  // 移除第一行标题（以'>'或'#'开头的行）
  const contentWithoutTitle = content.replace(/^(?:>|#{1,6})\s*(.+?)(?:\n|$)/, '')

  // 特殊链接格式替换
  const replacedContent = contentWithoutTitle
    .replace(/\\\(\s*/g, '$')
    .replace(/\s*\\\)/g, '$')
    .replace(/\\\[\s*/g, '$$')
    .replace(/\s*\\\]/g, '$$')
    .replace(
      /\[\[(\d+)\]\((https?:\/\/[^\)]+)\)\]/g,
      '<button class="bg-gray-500 text-white rounded-full w-4 h-4 mx-1 flex justify-center items-center text-sm hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-500 inline-flex" onclick="window.open(\'$2\', \'_blank\')">$1</button>'
    )

  // 检测是否包含markdown标记
  const hasMarkdown = /[*#>~`_\[\]\(\)\{\}]/.test(replacedContent)

  if (hasMarkdown) {
    // 如果内容包含markdown标记，使用markdown渲染
    return mdi.render(replacedContent)
  } else {
    // 否则将URL转换为链接，保留普通文本格式
    return replacedContent.replace(urlRegex, url => {
      return `<a href="${url}" target="_blank" class="text-primary-600 hover:underline">${url}</a>`
    })
  }
}

// 代码高亮显示的辅助函数
function highlightBlock(str: string, lang?: string) {
  const blockId = `code-block-${Date.now()}-${Math.floor(Math.random() * 1000)}`

  return `<pre 
    style="max-width:100%;"
    class="code-block-wrapper rounded-md overflow-auto"
    id="${blockId}"
  ><div class="code-block-header flex justify-between items-center p-2 bg-gray-100 dark:bg-gray-800">
    <span class="code-block-header__lang text-xs font-medium text-gray-500 dark:text-gray-400">${lang || 'text'}</span>
    <div class="code-block-header__buttons">
      <span class="code-block-header__copy text-xs cursor-pointer px-2 hover:text-primary-600" data-block-id="${blockId}">复制</span>
    </div>
  </div><code
    class="hljs block p-4 overflow-auto"
    style="max-height: 400px;"
  >${str}</code></pre>`
}

// 设置预览状态
const isLoading = ref(false)

// 处理刷新动作
const handleRefresh = () => {
  ms.success('刷新成功')
}

// 清空所有工作流内容
const clearAllContent = () => {
  globalStore.clearWorkflowContent()
  ms.success('内容已清空')
}

// 处理复制全部内容
const handleCopyAll = async () => {
  try {
    const fullText = workflowContentArray.value.join('\n\n')
    await navigator.clipboard.writeText(fullText)
    ms.success('全部内容已复制到剪贴板')
  } catch (err) {
    ms.error('复制失败')
    console.error('复制失败:', err)
  }
}

// 全屏切换逻辑
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    previewerContainerRef.value?.requestFullscreen().catch(err => {
      console.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`)
    })
  } else {
    document.exitFullscreen()
  }
}

// 同步全屏状态
const syncFullscreenState = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 打开预览时折叠侧边栏
const collapseSidebar = () => {
  // 记录当前侧边栏状态
  sidebarStateBeforePreview.value = appStore.siderCollapsed
  // 折叠侧边栏
  if (!appStore.siderCollapsed) {
    appStore.setSiderCollapsed(true)
  }
}

// 关闭预览时恢复侧边栏状态
const restoreSidebar = () => {
  // 只在非移动设备上执行恢复
  if (!isMobile.value && !sidebarStateBeforePreview.value && appStore.siderCollapsed) {
    appStore.setSiderCollapsed(false)
  }
}

// 关闭工作流预览器
const handleClose = () => {
  // 恢复侧边栏状态
  restoreSidebar()

  // 关闭预览器
  props.close()

  // 如果是移动端，确保全屏状态正确恢复
  if (isMobile.value) {
    document.documentElement.classList.remove('overflow-hidden')
    document.body.classList.remove('overflow-hidden')
  }
}

// 折叠状态管理 - 记录每个工作流项的展开/折叠状态
const collapsedState = ref<{ [key: number]: boolean }>({})

// 切换折叠状态
const toggleCollapse = (index: number) => {
  collapsedState.value[index] = !collapsedState.value[index]
}

// 判断某项是否处于折叠状态
const isCollapsed = (index: number) => {
  return !!collapsedState.value[index]
}

// 提取工作流步骤标题
const getStepTitle = (content: string, index: number) => {
  // 尝试从内容中提取标题（优先匹配引用格式 "> 标题"，然后是标题格式 "# 标题"）
  const quoteMatch = content.match(/^>\s*(.+?)(?:\n|$)/)
  if (quoteMatch && quoteMatch[1]) {
    return quoteMatch[1].trim()
  }

  const headerMatch = content.match(/^#{1,6}\s*(.+?)(?:\n|$)/)
  if (headerMatch && headerMatch[1]) {
    // 返回提取到的标题文本，去除前缀（如"步骤1："）
    const title = headerMatch[1].trim()
    const cleanTitle = title.replace(/^步骤\d+[\s:：]*/, '').trim()
    return cleanTitle || `步骤 ${index + 1}`
  }

  // 如果没有找到标题，返回默认标题
  return `步骤 ${index + 1}`
}

// 获取当前活动对话的最后一条消息
const latestMessage = computed(() => {
  const chatList = chatStore.chatList
  if (chatList.length > 0) {
    return chatList[chatList.length - 1]
  }
  return null
})

// 从最后一条消息中获取工作流节点信息
const currentNodeType = computed(() => latestMessage.value?.nodeType || '')
const currentStepName = computed(() => latestMessage.value?.stepName || '正在处理中...')
const currentProgress = computed(() => latestMessage.value?.workflowProgress || 0)

// 生命周期钩子
onMounted(() => {
  // 添加移动端全屏样式
  if (isMobile.value) {
    document.documentElement.classList.add('overflow-hidden')
    document.body.classList.add('overflow-hidden')
  }

  document.addEventListener('fullscreenchange', syncFullscreenState)

  // 折叠侧边栏
  collapseSidebar()

  // 初始化时设置所有内容为展开状态
  workflowContentArray.value.forEach((_, index) => {
    collapsedState.value[index] = false
  })

  // 添加复制功能
  const setupCodeCopy = () => {
    const copyButtons = document.querySelectorAll('.code-block-header__copy')
    copyButtons.forEach(button => {
      const blockId = button.getAttribute('data-block-id')
      if (!blockId) return

      button.addEventListener('click', event => {
        event.stopPropagation()
        event.preventDefault()

        const codeBlock = document.getElementById(blockId)
        if (codeBlock) {
          const codeElement = codeBlock.querySelector('code')
          if (codeElement && codeElement.textContent) {
            navigator.clipboard
              .writeText(codeElement.textContent)
              .then(() => {
                const originalText = button.textContent
                button.textContent = '已复制'

                setTimeout(() => {
                  button.textContent = originalText
                }, 2000)

                ms.success('代码已复制')
              })
              .catch(() => ms.error('复制失败'))
          }
        }
      })
    })
  }

  // 初始设置和DOM更新后重新设置
  nextTick(() => {
    setupCodeCopy()
    // 初始滚动到底部
    scrollToBottom()
  })
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', syncFullscreenState)

  // 确保在组件卸载时也能恢复侧边栏状态
  restoreSidebar()

  // 针对移动端，移除全屏样式
  if (isMobile.value) {
    document.documentElement.classList.remove('overflow-hidden')
    document.body.classList.remove('overflow-hidden')
  }
})

// 当工作流内容变化时，更新折叠状态
watch(workflowContentArray, newVal => {
  newVal.forEach((_, index) => {
    // 如果这个索引的折叠状态还未设置，则默认设为展开状态
    if (collapsedState.value[index] === undefined) {
      collapsedState.value[index] = false
    }
  })
})

// 监听工作流内容变化，滚动到底部
watch(
  workflowContentArray,
  () => {
    // 内容变化时滚动到底部
    nextTick(() => {
      scrollToBottom()
    })
  },
  { deep: true }
)
</script>

<template>
  <div
    ref="previewerContainerRef"
    class="h-full w-full flex flex-col workflow-previewer-container transition-all duration-300 transform animate-fade-in shadow-previewer"
    :class="{ fullscreen: isFullscreen }"
  >
    <!-- 顶部标题栏 -->
    <div
      class="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700"
    >
      <div class="flex items-center">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
          {{ currentStepName || '工作流详情' }}
        </h2>
      </div>
      <div class="flex items-center space-x-2">
        <button class="btn btn-ghost btn-sm" @click="handleRefresh" title="刷新">
          <Refresh :size="20" />
        </button>
        <button class="btn btn-ghost btn-sm" @click="handleCopyAll" title="复制全部">
          <Copy :size="20" />
        </button>
        <button class="btn btn-ghost btn-sm" @click="toggleFullscreen" title="全屏">
          <FullScreen v-if="!isFullscreen" :size="20" />
          <OffScreen v-else :size="20" />
        </button>
        <button class="btn btn-ghost btn-sm" @click="handleClose" title="关闭">
          <Close :size="20" />
        </button>
      </div>
    </div>

    <!-- 内容区域 - 含上下渐变过渡 -->
    <div class="relative flex-1 overflow-hidden">
      <!-- 上方渐变过渡区域 -->
      <div
        class="absolute left-0 right-0 top-0 h-12 z-10 bg-gradient-to-b from-white to-transparent dark:from-gray-800 dark:to-transparent pointer-events-none"
      ></div>

      <!-- 可滚动内容区域 -->
      <div ref="scrollRef" @scroll="handleScroll" class="h-full overflow-auto custom-scrollbar">
        <div class="workflow-content pt-8 pb-60 px-8">
          <div v-if="isLoading" class="flex justify-center items-center h-full">
            <LoadingOne class="animate-spin text-primary-600" :size="32" />
          </div>
          <div v-else-if="workflowContentArray.length === 0" class="skeleton-container py-8">
            <!-- 骨架屏效果 -->
            <div class="skeleton-line w-3/4 h-6 mb-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="skeleton-line w-full h-4 mb-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="skeleton-line w-5/6 h-4 mb-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="skeleton-line w-2/3 h-4 mb-6 bg-gray-200 dark:bg-gray-700 rounded"></div>

            <div class="skeleton-line w-4/5 h-4 mb-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="skeleton-line w-full h-4 mb-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="skeleton-line w-3/4 h-4 mb-6 bg-gray-200 dark:bg-gray-700 rounded"></div>

            <div
              class="skeleton-code w-full h-24 mb-6 bg-gray-200 dark:bg-gray-700 rounded-md"
            ></div>

            <div class="skeleton-line w-5/6 h-4 mb-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div class="skeleton-line w-full h-4 mb-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
          <div v-else>
            <div
              v-for="(content, index) in workflowContentArray"
              :key="index"
              class="workflow-item mb-6"
            >
              <div class="markdown-body" v-html="formatContent(content)"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 下方渐变过渡区域 -->
      <div
        class="absolute left-0 right-0 bottom-0 h-12 z-10 bg-gradient-to-t from-white to-transparent dark:from-gray-800 dark:to-transparent pointer-events-none"
      ></div>
    </div>
  </div>
</template>

<style scoped>
.workflow-previewer-container {
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  border-top-left-radius: 16px;
  border-bottom-left-radius: 16px;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.05);
}

.dark .workflow-previewer-container {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
}

/* 全屏模式下移除边框样式 */
.fullscreen.workflow-previewer-container {
  border-radius: 0;
  border-left: none;
  box-shadow: none;
}

.workflow-content {
  display: flex;
  flex-direction: column;
}

/* 骨架屏样式 */
.skeleton-line,
.skeleton-code {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
}

.skeleton-container {
  max-width: 800px;
  margin: 0 auto;
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

/* 针对折叠内容的过渡动画 */
.collapse-enter-active,
.collapse-leave-active {
  transition: max-height 0.3s ease-in-out;
  overflow: hidden;
}

.collapse-enter-from,
.collapse-leave-to {
  max-height: 0;
}

.collapse-enter-to,
.collapse-leave-from {
  max-height: 1000px;
}

/* 光带效果动画 */
@keyframes glowBandAnim {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.glow-band {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.5),
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: glowBandAnim 1.5s infinite linear;
  pointer-events: none;
  z-index: 1;
}

.dark .glow-band {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05),
    transparent
  );
}

/* 移动端响应式样式 */
@media (max-width: 768px) {
  .workflow-previewer-container {
    margin: 0;
    padding: 0;
  }
}
</style>

<style>
/* 这里放置非scoped样式，比如markdown渲染后的样式 */
.workflow-previewer-container .prose {
  font-size: 0.95rem;
}

.workflow-previewer-container .prose code {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
    monospace;
}

.dark .workflow-previewer-container .prose code {
  background-color: rgba(255, 255, 255, 0.1);
}

.workflow-previewer-container .prose a {
  color: #4f46e5;
  text-decoration: none;
}

.workflow-previewer-container .prose a:hover {
  text-decoration: underline;
}

.dark .workflow-previewer-container .prose a {
  color: #818cf8;
}
</style>

<style lang="less">
.workflow-previewer {
  .markdown-body {
    background-color: transparent !important;
    padding: 0 !important;
  }

  pre {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  @keyframes rotateAnimation {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .rotate-icon {
    animation: rotateAnimation 2s linear infinite;
  }
}
</style>
