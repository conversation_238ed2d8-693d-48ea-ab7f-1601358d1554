<script setup lang="ts">
import DropdownMenu from '@/components/common/DropdownMenu/index.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useAppStore, useAuthStore } from '@/store'
import { message } from '@/utils/message'
import {
  Close,
  Download,
  Earth,
  FileText,
  FullScreen,
  Help,
  Left,
  ListTwo,
  LoadingOne,
  Notes,
  OffScreen,
  Refresh,
  ZoomIn,
  ZoomOut,
} from '@icon-park/vue-next'
import type { PDFDocumentProxy } from 'pdfjs-dist/types/src/display/api'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
import { VuePdf, createLoadingTask } from 'vue3-pdfjs/esm'

// 添加防抖函数
const debounce = (fn: Function, delay: number) => {
  let timer: ReturnType<typeof setTimeout> | null = null
  return function (...args: any[]) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn(...args)
      timer = null
    }, delay)
  }
}

interface Props {
  pdfUrl: string
  close: () => void
}

const props = defineProps<Props>()

// 定义事件
const emit = defineEmits<{
  (e: 'ask-question', prompt: string): void
}>()

const appStore = useAppStore()
const authStore = useAuthStore()
const ms = message()
const { isMobile } = useBasicLayout()

// 状态管理
const isLoading = ref(true)
const isFullscreen = ref(false)
const previewerContainerRef = ref<HTMLDivElement | null>(null)
const pdfContainerRef = ref<HTMLDivElement | null>(null)
const zoomLevel = ref(125)
const isZooming = ref(false)

// PDF相关状态
const numOfPages = ref(0)
const currentPage = ref(1)
const loadError = ref(false)
const showOutline = ref(false)
const outlineItems = ref<{ title: string; pageNumber: number }[]>([])

// 懒加载相关状态
const renderedPages = ref<Set<number>>(new Set())
const maxRenderPages = ref(10) // 最大同时渲染页数
const renderRange = ref({ start: 1, end: 5 }) // 当前渲染范围

// 文字选择相关状态
const selectedText = ref('')
const showQuickActions = ref(false)
const selectionPosition = ref({ x: 0, y: 0 })
const activeOption = ref<string | null>(null)
const showSubMenusBelow = ref(false)

// 菜单选项配置
const menuOptions = [
  {
    label: '解释',
    value: 'explain',
    icon: Help,
  },
  {
    label: '改写',
    value: 'rewrite',
    icon: Notes,
    subOptions: [
      { label: '简化表达', value: 'simplify' },
      { label: '正式表达', value: 'formal' },
      { label: '口语表达', value: 'casual' },
      { label: '详细解释', value: 'detail' },
    ],
  },
  {
    label: '翻译',
    value: 'translate',
    icon: Earth,
    subOptions: [
      { label: '翻译成中文', value: 'translateToChinese' },
      { label: '翻译成英文', value: 'translateToEnglish' },
      { label: '翻译成日语', value: 'translateToJapanese' },
      { label: '翻译成韩语', value: 'translateToKorean' },
      { label: '翻译成法语', value: 'translateToFrench' },
      { label: '翻译成德语', value: 'translateToGerman' },
    ],
  },
  {
    label: '总结',
    value: 'summarize',
    icon: FileText,
  },
]

// 记录打开预览之前的侧边栏状态，用于关闭预览时恢复
const sidebarStateBeforePreview = ref(true)

// 计算页面标题
const pageTitle = computed(() => {
  const url = props.pdfUrl
  if (!url) return 'PDF预览'

  try {
    const urlObj = new URL(url)
    const pathSegments = urlObj.pathname.split('/')
    const filename = pathSegments[pathSegments.length - 1]
    return filename || 'PDF文件'
  } catch {
    return 'PDF预览'
  }
})

// 计算站点名称
const siteName = computed(() => authStore.globalConfig?.siteName || 'AIWEB')

// 计算缩放比例
const scale = computed(() => {
  const scaleValue = zoomLevel.value / 100
  console.log('🔍 计算缩放比例:', scaleValue, '基于缩放级别:', zoomLevel.value)
  return scaleValue
})

// 全屏切换逻辑
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    previewerContainerRef.value?.requestFullscreen().catch(err => {
      console.error(`Error attempting to enable full-screen mode: ${err.message} (${err.name})`)
    })
  } else {
    document.exitFullscreen()
  }
}

// 同步全屏状态
const syncFullscreenState = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 下载PDF文件
const downloadPdf = async () => {
  try {
    const response = await fetch(props.pdfUrl)
    const blob = await response.blob()

    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(blob)
    downloadLink.download = pageTitle.value
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)

    ms.success('PDF 文件已开始下载')
  } catch (error) {
    console.error('下载PDF失败:', error)
    ms.error('下载PDF失败，请检查文件链接')
  }
}

// 缩放控制
const zoomIn = debounce(() => {
  if (zoomLevel.value < 200) {
    isZooming.value = true
    zoomLevel.value = Math.min(200, zoomLevel.value + 25)
    console.log('🔍 缩放级别增加至:', zoomLevel.value, '%')
    setTimeout(() => {
      isZooming.value = false
    }, 300)
  }
}, 100)

const zoomOut = debounce(() => {
  if (zoomLevel.value > 50) {
    isZooming.value = true
    zoomLevel.value = Math.max(50, zoomLevel.value - 25)
    console.log('🔍 缩放级别减少至:', zoomLevel.value, '%')
    setTimeout(() => {
      isZooming.value = false
    }, 300)
  }
}, 100)

const resetZoom = debounce(() => {
  if (zoomLevel.value !== 100) {
    isZooming.value = true
    zoomLevel.value = 100
    console.log('🔍 缩放级别重置为:', zoomLevel.value, '%')
    setTimeout(() => {
      isZooming.value = false
    }, 300)
  }
}, 100)

// 刷新PDF
const refreshPdf = () => {
  isLoading.value = true
  loadError.value = false

  // 重新加载PDF
  loadPdf()
}

// 加载PDF
const loadPdf = async () => {
  try {
    console.log('🚀 开始加载PDF:', props.pdfUrl)
    const loadingTask = createLoadingTask(props.pdfUrl)
    const pdf: PDFDocumentProxy = await loadingTask.promise

    numOfPages.value = pdf.numPages
    isLoading.value = false
    loadError.value = false
    console.log('✅ PDF加载成功，总页数:', numOfPages.value)

    // 重置懒加载状态
    renderedPages.value.clear()
    renderRange.value = { start: 1, end: Math.min(5, numOfPages.value) }
    currentPage.value = 1
    console.log('🔄 初始渲染范围:', renderRange.value)

    // 尝试加载大纲
    try {
      console.log('📚 开始加载PDF大纲...')
      const outline = await pdf.getOutline()
      console.log('📚 原始大纲数据:', outline)

      if (outline && outline.length > 0) {
        const processedOutline = []

        for (const item of outline) {
          let pageNumber = 0

          if (item.dest) {
            console.log('📚 处理大纲项目:', item.title, '目标:', item.dest)

            if (Array.isArray(item.dest)) {
              // 处理数组形式的dest
              const destRef = item.dest[0]
              if (typeof destRef === 'object' && destRef.num) {
                // 通过页面引用获取页码
                try {
                  const pageRef = await pdf.getPageIndex(destRef)
                  pageNumber = pageRef + 1 // 页码从1开始
                  console.log('📚 通过引用获取页码:', pageNumber)
                } catch (refError) {
                  console.warn('⚠️ 无法解析页面引用:', refError)
                  // 尝试直接使用num作为页码
                  pageNumber = destRef.num || 0
                }
              } else if (typeof destRef === 'number') {
                pageNumber = destRef + 1
              }
            } else if (typeof item.dest === 'string') {
              // 有些PDF使用字符串形式的dest
              console.log('📚 字符串形式的dest:', item.dest)
            }
          }

          if (pageNumber > 0 && pageNumber <= numOfPages.value) {
            processedOutline.push({
              title: item.title,
              pageNumber: pageNumber,
            })
          }
        }

        outlineItems.value = processedOutline
        console.log('✅ PDF大纲加载成功:', outlineItems.value.length, '个条目')
        console.log('📚 处理后的大纲数据:', outlineItems.value)
      } else {
        console.log('⚠️ PDF没有大纲信息')
        outlineItems.value = []
      }
    } catch (outlineError) {
      console.error('❌ 加载PDF大纲失败:', outlineError)
      outlineItems.value = []
    }
  } catch (error) {
    console.error('❌ PDF加载失败:', error)
    isLoading.value = false
    loadError.value = true
    ms.error('PDF加载失败，请检查文件链接')
  }
}

// 添加页面观察器
let pageObserver: IntersectionObserver | null = null

// 修复的滚动监听，正确计算页面位置
const handleScroll = debounce(() => {
  if (!pdfContainerRef.value || isLoading.value || loadError.value) return

  const container = pdfContainerRef.value
  const scrollTop = container.scrollTop
  const containerHeight = container.clientHeight
  const containerCenter = scrollTop + containerHeight / 2

  // 只查找真实渲染的PDF页面，不包括占位符
  const pages = container.querySelectorAll('.pdf-page:not(.pdf-page-placeholder)')

  console.log(
    '📜 滚动检测 - 容器滚动位置:',
    scrollTop,
    '容器高度:',
    containerHeight,
    '中心位置:',
    containerCenter
  )
  console.log('📜 找到', pages.length, '个真实页面')

  // 找到距离容器中心最近的页面
  let closestPage = 1
  let minDistance = Infinity

  pages.forEach(page => {
    // 使用 offsetTop 获取相对于容器的位置，而不是相对于视口
    const htmlElement = page as HTMLElement
    const pageTop = htmlElement.offsetTop
    const pageHeight = htmlElement.offsetHeight
    const pageCenter = pageTop + pageHeight / 2
    const distance = Math.abs(pageCenter - containerCenter)

    // 确保页面在可视区域内
    const pageBottom = pageTop + pageHeight
    const isVisible = pageBottom > scrollTop && pageTop < scrollTop + containerHeight

    if (isVisible) {
      if (distance < minDistance) {
        minDistance = distance
        const pageNum = parseInt(page.getAttribute('data-page-number') || '1')
        closestPage = pageNum
        console.log(
          '📍 页面',
          pageNum,
          'offsetTop:',
          pageTop,
          '中心位置:',
          pageCenter,
          '距离容器中心:',
          distance.toFixed(2),
          'px'
        )
      }
    }
  })

  // 更新当前页码
  if (closestPage !== currentPage.value) {
    console.log('📑 页码更新为:', closestPage, '(距离容器中心最近)')
    currentPage.value = closestPage
  }

  // 检查是否需要加载更多页面
  if (closestPage >= renderRange.value.end - 2 && renderRange.value.end < numOfPages.value) {
    console.log('🔄 触发懒加载扩展')
    expandRenderRange()
  }
}, 150)

// 扩展渲染范围（懒加载）
const expandRenderRange = () => {
  const newEnd = Math.min(renderRange.value.end + 5, numOfPages.value)
  if (newEnd > renderRange.value.end) {
    renderRange.value.end = newEnd
    console.log('📄 扩展渲染范围至:', renderRange.value)
  }
}

// 计算是否应该渲染特定页面
const shouldRenderPage = (pageNumber: number) => {
  return pageNumber >= renderRange.value.start && pageNumber <= renderRange.value.end
}

// 简化页面观察逻辑 - 只用于懒加载检测，页码跟踪交给滚动事件
const setupPageObserver = () => {
  // 如果已经有观察器，先清理
  if (pageObserver) {
    pageObserver.disconnect()
    pageObserver = null
  }

  // 简化的观察器，只用于检测是否需要扩展渲染范围
  pageObserver = new IntersectionObserver(
    entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const pageNum = parseInt(entry.target.getAttribute('data-page-number') || '1')
          // 如果接近渲染范围末尾，触发扩展
          if (pageNum >= renderRange.value.end - 1 && renderRange.value.end < numOfPages.value) {
            console.log('👁️ 观察器检测到需要扩展渲染范围，当前页面:', pageNum)
            expandRenderRange()
          }
        }
      })
    },
    {
      root: pdfContainerRef.value,
      threshold: [0.1],
      rootMargin: '100px',
    }
  )

  // 只观察占位符页面，用于触发懒加载
  if (pdfContainerRef.value) {
    const placeholders = pdfContainerRef.value.querySelectorAll('.pdf-page-placeholder')
    placeholders.forEach(placeholder => {
      pageObserver?.observe(placeholder)
    })
    console.log('👁️ 已设置页面观察器，观察', placeholders.length, '个占位符')
  }
}

// 处理页面渲染完成
const handlePageRendered = (page: number) => {
  console.log('📄 页面渲染完成:', page)

  // 标记页面已渲染
  renderedPages.value.add(page)

  // 初次渲染完成后设置观察器
  if (renderedPages.value.size === 1) {
    console.log('🔧 首页渲染完成，设置观察器')
    setTimeout(() => {
      setupPageObserver()
    }, 200)
  }

  // 如果用户通过大纲跳转到特定页面，确保滚动到该页面
  if (pdfContainerRef.value && page === currentPage.value) {
    const pageElement = pdfContainerRef.value.querySelector(`[data-page-number="${page}"]`)
    if (pageElement) {
      pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }
}

// 处理大纲项点击
const handleOutlineItemClick = (pageNumber: number) => {
  if (pageNumber <= 0 || pageNumber > numOfPages.value) {
    console.warn('⚠️ 无效的页码:', pageNumber)
    return
  }

  console.log('📑 从大纲跳转到页面:', pageNumber)
  currentPage.value = pageNumber

  // 滚动到对应页面
  if (pdfContainerRef.value) {
    const pageElement = pdfContainerRef.value.querySelector(`[data-page-number="${pageNumber}"]`)
    if (pageElement) {
      pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
      console.log('✅ 已滚动到页面', pageNumber)
    } else {
      console.warn('⚠️ 找不到页面元素:', pageNumber)
    }
  }

  // 关闭大纲面板
  showOutline.value = false
}

// 处理文字选择
const handleMouseUp = (event: MouseEvent) => {
  console.log('🔍 检测到鼠标抬起事件')

  const selection = window.getSelection()
  const text = selection?.toString().trim()

  if (text && text.length > 0) {
    console.log('📝 检测到选择文字:', text)
    selectedText.value = text
      selectedText.value = text
    // 重置子菜单状态
    activeOption.value = null
    console.log('🔄 重置子菜单状态')
      console.log('🔄 重置子菜单状态')
    const range = selection?.getRangeAt(0)
    const rect = range?.getBoundingClientRect()
      const rect = range?.getBoundingClientRect()
    if (rect) {
      // 计算相对于预览器容器的位置
      const containerRect = previewerContainerRef.value?.getBoundingClientRect()
      if (containerRect) {
        const menuX = rect.left - containerRect.left + rect.width / 2
          const menuX = rect.left - containerRect.left + rect.width / 2
        // 判断是否应该在选择文本上方或下方显示菜单
        const showMenuAbove = rect.top > containerRect.height / 2
          const showMenuAbove = rect.top > containerRect.height / 2
        // 设置菜单位置
        let menuY
        if (showMenuAbove) {
          // 在选择文本上方显示
          menuY = rect.top - containerRect.top - 10
          console.log('📍 菜单将显示在选择文本上方')
        } else {
          // 在选择文本下方显示
          menuY = rect.bottom - containerRect.top + 10
          console.log('📍 菜单将显示在选择文本下方')
        }
          }
        // 确保菜单在可视区域内
        selectionPosition.value = {
          x: Math.max(10, Math.min(menuX, containerRect.width - 10)),
          y: Math.max(10, menuY),
        }
          }
        // 根据菜单位置决定子菜单的显示位置
        showSubMenusBelow.value = showMenuAbove
        console.log('📍 子菜单将显示在', showSubMenusBelow.value ? '下方' : '上方')
          console.log('📍 子菜单将显示在', showSubMenusBelow.value ? '下方' : '上方')
        console.log('📍 菜单位置:', selectionPosition.value)
        console.log('✅ 显示快捷操作菜单')
        showQuickActions.value = true
      }
    }
  } else {
    console.log('⚠️ 无有效选择，隐藏菜单')
    hideQuickActions()
  }
  }, 50)
}

// 显示子菜单选项
const showSubOptions = (optionValue: string) => {
  activeOption.value = optionValue
  console.log('📋 显示子菜单:', optionValue)
}

// 重置子菜单选项
const resetSubOptions = () => {
  activeOption.value = null
  console.log('🔒 重置子菜单状态')
}

// 处理菜单点击
const handleMenuClick = (option: any) => {
  console.log('🎯 菜单点击:', option.value, '是否有子选项:', !!option.subOptions)
  if (!option.subOptions) {
    handleOptionClick(option.value)
  }
}

// 处理菜单悬停
const handleMenuHover = (option: any) => {
  console.log('🖱️ 菜单悬停:', option.value, '是否有子选项:', !!option.subOptions)
  if (option.subOptions) {
    showSubOptions(option.value)
  }
}

// 处理菜单选项点击
const handleOptionClick = (optionValue: string) => {
  console.log('🎯 选择菜单选项:', optionValue)

  // 处理对应的操作
  handleQuickAction(optionValue)

  // 隐藏菜单
  hideQuickActions()
}

// 隐藏快捷操作
const hideQuickActions = () => {
  console.log('🚫 隐藏快捷操作菜单')
  showQuickActions.value = false
  activeOption.value = null
  selectedText.value = ''
}

// 快捷菜单相关变量
let hideMenuTimer: ReturnType<typeof setTimeout> | null = null

// 简化后不再需要这个配置

// 处理快捷操作点击
const handleQuickAction = (action: string) => {
  if (!selectedText.value) return

  let prompt = ''
  switch (action) {
    case 'explain':
      prompt = `请解释这段文字的含义：\n\n${selectedText.value}`
      break
    case 'detail':
      prompt = `请详细解释这段文字：\n\n${selectedText.value}`
      break
    case 'simplify':
      prompt = `请将这段文字简化，使其更容易理解，但保留核心含义：\n\n${selectedText.value}`
      break
    case 'formal':
      prompt = `请将这段文字改写成正式、专业的表达方式，适合正式场合使用：\n\n${selectedText.value}`
      break
    case 'casual':
      prompt = `请将这段文字改写成日常口语表达方式，使其更加通俗易懂：\n\n${selectedText.value}`
      break
    case 'summarize':
      prompt = `请总结这段文字的要点：\n\n${selectedText.value}`
      break
    case 'translateToChinese':
      prompt = `请将这段文字准确翻译成中文，保持原文的格式和专业术语：\n\n${selectedText.value}`
      break
    case 'translateToEnglish':
      prompt = `请将这段文字准确翻译成英文，保持原文的格式和专业术语：\n\n${selectedText.value}`
      break
    case 'translateToJapanese':
      prompt = `请将这段文字准确翻译成日语，保持原文的格式和专业术语：\n\n${selectedText.value}`
      break
    case 'translateToKorean':
      prompt = `请将这段文字准确翻译成韩语，保持原文的格式和专业术语：\n\n${selectedText.value}`
      break
    case 'translateToFrench':
      prompt = `请将这段文字准确翻译成法语，保持原文的格式和专业术语：\n\n${selectedText.value}`
      break
    case 'translateToGerman':
      prompt = `请将这段文字准确翻译成德语，保持原文的格式和专业术语：\n\n${selectedText.value}`
      break
    default:
      prompt = `请处理这段文字：\n\n${selectedText.value}`
  }

  console.log('💬 发送提问:', prompt)

  // 通过事件将提问传递给父组件
  emit('ask-question', prompt)

  // 隐藏快捷操作菜单
  hideQuickActions()
}

// 打开预览时折叠侧边栏
const collapseSidebar = () => {
  sidebarStateBeforePreview.value = appStore.siderCollapsed
  if (!appStore.siderCollapsed) {
    appStore.setSiderCollapsed(true)
  }
}

// 关闭预览时恢复侧边栏状态
const restoreSidebar = () => {
  if (!isMobile.value && !sidebarStateBeforePreview.value && appStore.siderCollapsed) {
    appStore.setSiderCollapsed(false)
  }
}

// 关闭PDF预览器
const handleClose = () => {
  // 恢复侧边栏状态
  if (!isMobile.value && !sidebarStateBeforePreview.value && appStore.siderCollapsed) {
    appStore.setSiderCollapsed(false)
  }

  // 关闭预览器
  props.close()

  // 如果是移动端，确保全屏状态正确恢复
  if (isMobile.value) {
    document.documentElement.classList.remove('overflow-hidden')
    document.body.classList.remove('overflow-hidden')
  }

  // 重置页面标题
  document.title = siteName.value
}

// 监听PDF URL变化
watch(
  () => props.pdfUrl,
  newUrl => {
    if (newUrl) {
      loadPdf()
    }
  },
  { immediate: true }
)

// 缩放预设选项
const zoomOptions = [
  { value: 50, label: '50%' },
  { value: 75, label: '75%' },
  { value: 100, label: '100%' },
  { value: 125, label: '125%' },
  { value: 150, label: '150%' },
  { value: 175, label: '175%' },
  { value: 200, label: '200%' },
]

// 缩放菜单状态
const isZoomMenuOpen = ref(false)

// 选择缩放级别
const selectZoomLevel = debounce((value: number) => {
  if (zoomLevel.value !== value) {
    isZooming.value = true
    zoomLevel.value = value
    console.log('🔍 缩放级别设置为:', value, '%')
    setTimeout(() => {
      isZooming.value = false
    }, 300)
  }
}, 100)

// 生命周期钩子
onMounted(() => {
  console.log('🚀 PDF预览器组件挂载完成')

  // 设置标题
  document.title = `${pageTitle.value} - ${siteName.value}`

  // 添加移动端全屏样式
  if (isMobile.value) {
    document.documentElement.classList.add('overflow-hidden')
    document.body.classList.add('overflow-hidden')
  }

  document.addEventListener('fullscreenchange', syncFullscreenState)

  // 折叠侧边栏
  collapseSidebar()

  // 添加滚动监听
  pdfContainerRef.value?.addEventListener('scroll', handleScroll)

  // 点击其他区域关闭快捷操作菜单
  document.addEventListener('click', e => {
    const target = e.target as HTMLElement
    // 检查是否点击在快捷菜单或PDF页面上
    if (!target.closest('.selection-menu') && !target.closest('.pdf-page')) {
      console.log('📱 点击外部区域，隐藏快捷菜单')
      hideQuickActions()
    }
  })

  // 添加调试日志
  console.log('🛠️ 初始菜单状态 - 活跃选项:', activeOption.value)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', syncFullscreenState)

  // 移除滚动监听
  pdfContainerRef.value?.removeEventListener('scroll', handleScroll)

  // 清理页面观察器
  if (pageObserver) {
    pageObserver.disconnect()
    pageObserver = null
    console.log('👁️ 已清理页面观察器')
  }

  // 清理定时器
  if (hideMenuTimer) {
    clearTimeout(hideMenuTimer)
  }

  // 确保在组件卸载时也能恢复侧边栏状态
  restoreSidebar()

  // 针对移动端，移除全屏样式
  if (isMobile.value) {
    document.documentElement.classList.remove('overflow-hidden')
    document.body.classList.remove('overflow-hidden')
  }

  // 重置页面标题
  document.title = siteName.value
})
</script>

<template>
  <div
    ref="previewerContainerRef"
    class="h-full w-full flex flex-col pdf-previewer-container transition-all duration-300 transform animate-fade-in shadow-previewer"
    :class="{ fullscreen: isFullscreen }"
  >
    <!-- Header with Controls -->
    <div
      class="flex justify-between items-center h-16 border-b dark:border-gray-700 bg-white dark:bg-gray-900 flex-shrink-0"
      :class="{ 'px-2': isMobile, 'px-4': !isMobile }"
    >
      <!-- Left side: Title and Info -->
      <div class="flex items-center space-x-3">
        <!-- Mobile back button -->
        <button v-if="isMobile" @click="handleClose" class="btn-icon btn-sm mr-2" aria-label="返回">
          <Left :size="20" />
        </button>

        <!-- 大纲按钮 -->
        <button
          v-if="!isLoading && !loadError"
          @click="showOutline = !showOutline"
          class="btn-icon btn-sm mr-2 relative"
          aria-label="大纲"
        >
          <ListTwo :size="20" />
          <div v-if="!isMobile" class="tooltip tooltip-bottom">大纲</div>
        </button>

        <!-- 页码信息 -->
        <div class="flex items-center">
          <span class="text-lg font-medium text-gray-900 dark:text-gray-100">
            {{ currentPage }} / {{ numOfPages }}
          </span>
          <span
            v-if="!isLoading && !loadError && numOfPages > 0"
            class="ml-2 text-sm text-gray-500 dark:text-gray-400"
          >
            页
          </span>
        </div>
      </div>

      <!-- Right side: Controls -->
      <div class="flex items-center space-x-1">
        <!-- 缩放控制 -->
        <div v-if="!loadError" class="flex items-center space-x-1 mr-3">
          <div class="relative group">
            <button
              @click="zoomOut"
              :disabled="zoomLevel <= 50"
              class="btn-icon btn-md"
              aria-label="缩小"
            >
              <ZoomOut :size="20" />
            </button>
            <div v-if="!isMobile" class="tooltip tooltip-bottom">缩小</div>
          </div>

          <!-- 缩放菜单 -->
          <DropdownMenu v-model="isZoomMenuOpen" position="bottom-left">
            <template #trigger>
              <button
                class="menu-trigger flex items-center px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-750"
              >
                <span class="text-sm text-gray-600 dark:text-gray-400 min-w-[3rem] text-center">
                  {{ zoomLevel }}%
                </span>
                <svg
                  class="ml-1 w-4 h-4 transition-transform duration-200"
                  :class="{ 'rotate-180': isZoomMenuOpen }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            </template>
            <template #menu="{ close }">
              <div>
                <div
                  v-for="option in zoomOptions"
                  :key="option.value"
                  class="menu-item menu-item-md"
                  :class="{ 'menu-item-active': zoomLevel === option.value }"
                  @click="
                    () => {
                      selectZoomLevel(option.value)
                      close()
                    }
                  "
                  role="menuitem"
                  tabindex="0"
                >
                  <div class="flex-grow">{{ option.label }}</div>
                </div>
              </div>
            </template>
          </DropdownMenu>

          <div class="relative group">
            <button
              @click="zoomIn"
              :disabled="zoomLevel >= 200"
              class="btn-icon btn-md"
              aria-label="放大"
            >
              <ZoomIn :size="20" />
            </button>
            <div v-if="!isMobile" class="tooltip tooltip-bottom">放大</div>
          </div>

          <div class="relative group">
            <button @click="resetZoom" class="btn-icon btn-md" aria-label="重置缩放">
              <Refresh :size="20" />
            </button>
            <div v-if="!isMobile" class="tooltip tooltip-bottom">重置缩放</div>
          </div>
        </div>

        <!-- 下载按钮 -->
        <div class="relative group mx-1">
          <button @click="downloadPdf" class="btn-icon btn-md" aria-label="下载PDF">
            <Download :size="20" />
          </button>
          <div v-if="!isMobile" class="tooltip tooltip-bottom">下载PDF</div>
        </div>

        <!-- 刷新按钮 -->
        <div v-if="loadError" class="relative group mx-1">
          <button @click="refreshPdf" class="btn-icon btn-md" aria-label="刷新">
            <Refresh :size="20" />
          </button>
          <div v-if="!isMobile" class="tooltip tooltip-bottom">刷新</div>
        </div>

        <!-- 全屏按钮 -->
        <div v-if="!isMobile" class="relative group mx-1">
          <button
            @click="toggleFullscreen"
            class="btn-icon btn-md"
            :aria-label="isFullscreen ? '退出全屏' : '全屏'"
          >
            <OffScreen v-if="isFullscreen" :size="20" />
            <FullScreen v-else :size="20" />
          </button>
          <div v-if="!isMobile" class="tooltip tooltip-bottom">
            {{ isFullscreen ? '退出全屏' : '全屏' }}
          </div>
        </div>

        <!-- 关闭按钮 -->
        <div v-if="!isMobile" class="relative group">
          <button @click="handleClose" class="btn-icon btn-md" aria-label="关闭预览">
            <Close :size="20" />
          </button>
          <div v-if="!isMobile" class="tooltip tooltip-bottom">关闭预览</div>
        </div>
      </div>
    </div>

    <!-- Content Area -->
    <div class="flex-grow overflow-hidden relative bg-gray-100 dark:bg-gray-800">
      <!-- 大纲面板 -->
      <div
        v-if="showOutline"
        class="absolute left-0 top-0 h-full w-64 bg-white dark:bg-gray-900 shadow-lg z-30 overflow-y-auto"
      >
        <div class="p-3 border-b dark:border-gray-700 flex justify-between items-center">
          <h3 class="font-medium text-gray-900 dark:text-gray-100">PDF大纲</h3>
          <button @click="showOutline = false" class="btn-icon btn-sm" aria-label="关闭大纲">
            <Close :size="16" />
          </button>
        </div>
        <div class="p-0">
          <div v-if="outlineItems.length > 0">
            <button
              v-for="item in outlineItems"
              :key="item.title"
              @click="handleOutlineItemClick(item.pageNumber)"
              class="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 border-b border-gray-100 dark:border-gray-800"
            >
              {{ item.title }}
              <span class="text-xs text-gray-500 ml-1">({{ item.pageNumber }})</span>
            </button>
          </div>
          <div v-else class="px-3 py-4 text-sm text-gray-500 dark:text-gray-400 text-center">
            <p>此PDF文档没有大纲信息</p>
            <p class="mt-2">您可以通过滚动浏览文档内容</p>
          </div>
        </div>
      </div>

      <!-- 加载指示器 -->
      <div
        v-if="isLoading"
        class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 z-20"
      >
        <div class="flex flex-col items-center">
          <LoadingOne :size="40" class="animate-spin text-blue-500 mb-4" />
          <p class="text-gray-600 dark:text-gray-400">正在加载PDF文档...</p>
        </div>
      </div>

      <!-- 缩放指示器 -->
      <div
        v-if="isZooming && !isLoading && !loadError"
        class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-10 dark:bg-opacity-30 z-20 pointer-events-none"
      >
        <div
          class="flex flex-col items-center bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-lg"
        >
          <LoadingOne :size="24" class="animate-spin text-blue-500 mb-2" />
          <p class="text-sm text-gray-600 dark:text-gray-400">调整缩放比例...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div
        v-else-if="loadError"
        class="absolute inset-0 flex items-center justify-center bg-white dark:bg-gray-900 z-20"
      >
        <div class="flex flex-col items-center text-center max-w-md px-4">
          <div class="text-6xl mb-4">📄</div>
          <h3 class="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">PDF加载失败</h3>
          <p class="text-gray-600 dark:text-gray-400 mb-6">
            无法加载PDF文档，请检查文件链接是否有效，或稍后重试。
          </p>
          <div class="flex space-x-3">
            <button @click="refreshPdf" class="btn btn-primary btn-md">
              <Refresh :size="16" class="mr-2" />
              重新加载
            </button>
            <button @click="handleClose" class="btn btn-secondary btn-md">关闭预览</button>
          </div>
        </div>
      </div>

      <!-- PDF查看器 (使用VuePdf组件) -->
      <div
        v-if="props.pdfUrl && !loadError && !isLoading && numOfPages > 0"
        class="pdf-viewer-container w-full h-full overflow-auto"
        ref="pdfContainerRef"
      >
        <div class="flex flex-col items-center py-4">
          <!-- 懒加载渲染页面 -->
          <template v-for="page in numOfPages" :key="`page-${page}`">
            <VuePdf
              v-if="shouldRenderPage(page)"
              :key="`${props.pdfUrl}-page-${page}-${zoomLevel}`"
              :src="props.pdfUrl"
              :page="page"
              :scale="scale"
              @page-rendered="handlePageRendered"
              @mouseup="handleMouseUp"
              class="pdf-page shadow-lg mb-4 bg-white"
              :data-page-number="page"
            />
            <!-- 占位符，为未渲染的页面保留空间 -->
            <div
              v-else
              :data-page-number="page"
              class="pdf-page-placeholder shadow-lg mb-4 bg-gray-100 dark:bg-gray-700 flex items-center justify-center"
              :style="{
                width: `${scale * 595}px`,
                height: `${scale * 842}px`,
                minHeight: '400px',
              }"
            >
              <div class="text-gray-500 dark:text-gray-400 text-center">
                <div class="text-2xl mb-2">📄</div>
                <div>第 {{ page }} 页</div>
                <div class="text-sm">滚动查看</div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 文字选择后的快捷操作菜单 -->
      <div
        v-show="showQuickActions && selectedText"
        class="selection-menu absolute z-50"
        :style="{
          left: `${selectionPosition.x}px`,
          top: `${selectionPosition.y}px`,
          transform: 'translateX(-50%)',
        }"
        @click.stop
      >
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-xl flex items-stretch overflow-hidden border border-gray-200 dark:border-gray-700"
          @click.stop
        >
          <!-- 动态生成菜单项 -->
          <template v-for="(option, index) in menuOptions" :key="option.value">
            <!-- 分隔线 -->
            <div
              v-if="index > 0"
              class="w-px h-auto self-stretch bg-gray-200 dark:bg-gray-700"
            ></div>

            <!-- 菜单项 -->
            <div class="relative group">
              <button
                class="selection-menu-btn"
                :title="option.label"
                @click="handleMenuClick(option)"
                @mouseenter="handleMenuHover(option)"
              >
                <component :is="option.icon" :size="16" class="mr-1" />
                <span>{{ option.label }}</span>
              </button>

              <!-- 子菜单 -->
              <div
                v-if="option.subOptions && activeOption === option.value"
                class="absolute left-0 top-full mt-1 z-[9999] py-1 w-40 bg-white dark:bg-gray-800 rounded-md shadow-xl border border-gray-200 dark:border-gray-700"
                @mouseleave="resetSubOptions"
                @mouseenter="() => {}"
                style="position: fixed; z-index: 9999"
                :style="{
                  left: `${selectionPosition.x}px`,
                  top: `${selectionPosition.y + 50}px`,
                }"
              >
                <button
                  v-for="subOption in option.subOptions"
                  :key="subOption.value"
                  @click="handleOptionClick(subOption.value)"
                  class="w-full px-4 py-2 text-sm text-left text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 whitespace-nowrap"
                >
                  {{ subOption.label }}
                </button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 预览器边框样式 */
.pdf-previewer-container {
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  border-top-left-radius: 16px;
  border-bottom-left-radius: 16px;
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.05);
}

.dark .pdf-previewer-container {
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: -5px 0 15px rgba(0, 0, 0, 0.2);
}

/* 全屏模式下移除边框样式 */
.fullscreen.pdf-previewer-container {
  border-radius: 0;
  border-left: none;
  box-shadow: none;
  background-color: #ffffff;
}

.dark .fullscreen.pdf-previewer-container {
  background-color: #1f2937;
}

/* 移动端适配样式 */
@media (max-width: 768px) {
  .pdf-previewer-container {
    margin: 0;
    padding: 0;
  }

  .pdf-previewer-container button {
    min-width: 32px;
    min-height: 32px;
  }
}

/* 添加fade-in动画 */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1.5s linear infinite;
}

/* PDF查看器容器样式 */
.pdf-viewer-container {
  background-color: #f5f5f5;
}

.dark .pdf-viewer-container {
  background-color: #333;
}

/* PDF页面样式 */
.pdf-page {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-out;
}

/* 添加缩放过渡动画 */
.pdf-viewer-container .pdf-page {
  transform-origin: top center;
  will-change: transform;
}

/* 禁用的按钮样式 */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:disabled:hover {
  background-color: transparent;
}

/* 选择菜单样式 */
.selection-menu {
  z-index: 9999;
  position: absolute;
}

.selection-menu-btn {
  @apply flex items-center px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700;
  min-height: 36px;
  white-space: nowrap;
}

.selection-menu-btn:hover {
  @apply text-blue-600 dark:text-blue-400;
}

.selection-menu-btn:first-child {
  @apply rounded-l-lg;
}

.selection-menu-btn:last-child {
  @apply rounded-r-lg;
}

/* 翻译下拉菜单样式 */
.translate-dropdown,
.rewrite-dropdown {
  position: relative;
  padding-bottom: 10px; /* 为子菜单留出空间 */
}

.translate-dropdown .selection-menu-btn,
.rewrite-dropdown .selection-menu-btn {
  border-radius: 0;
}

/* 子菜单样式 */
.translate-dropdown > div,
.rewrite-dropdown > div {
  z-index: 10000; /* 确保子菜单在最顶层 */
  min-width: 140px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 确保下拉菜单在小屏幕上也能正常显示 */
@media (max-width: 640px) {
  .translate-dropdown > div,
  .rewrite-dropdown > div {
    left: 0;
    min-width: 140px;
  }
}
</style>
