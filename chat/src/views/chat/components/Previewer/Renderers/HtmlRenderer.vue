<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps<{
  content: string
  previewSize?: 'desktop' | 'tablet' | 'mobile'
}>()

const iframe = ref<HTMLIFrameElement | null>(null)

const renderHtml = () => {
  if (iframe.value) {
    console.log('🔍 渲染 HTML 内容到 iframe，内容长度:', props.content?.length || 0)
    iframe.value.srcdoc = props.content
  } else {
    console.warn('⚠️ HTML iframe尚未准备好')
  }
}

// 导出相关方法
const exportAsPng = async () => {
  console.log('🖼️ HTML渲染器收到PNG导出事件，开始导出...')

  if (!iframe.value || !iframe.value.contentDocument) {
    console.error('⚠️ Iframe 或其内容尚未准备好，无法导出。')
    document.dispatchEvent(
      new CustomEvent('show-message', {
        detail: { type: 'error', message: '预览尚未准备好，请稍后再试' },
      })
    )
    return
  }

  try {
    // 通知父组件开始导出
    document.dispatchEvent(
      new CustomEvent('html-export-start', {
        detail: { type: 'PNG' },
      })
    )
    // 动态导入html-to-image
    const { toPng } = await import('html-to-image')

    const targetElement = iframe.value.contentDocument.documentElement

    // 使用 iframe 内容的实际滚动尺寸来捕捉整个页面
    const canvasWidth = targetElement.scrollWidth
    const canvasHeight = targetElement.scrollHeight

    console.log('📸 直接从 iframe 内容截图，尺寸:', {
      width: canvasWidth,
      height: canvasHeight,
    })

    const dataUrl = await toPng(targetElement, {
      backgroundColor: '#ffffff',
      pixelRatio: 2, // 提高分辨率以获得更清晰的图像
      canvasWidth,
      canvasHeight,
      // 此选项有助于加载外部图像，但可能会因CORS策略而失败
      fetchRequestInit: {
        mode: 'cors',
        credentials: 'omit',
      },
    })

    // 从 iframe 的 document 中获取标题以生成文件名
    const title = iframe.value.contentDocument.title
    const filename = title ? `${title.trim()}.png` : 'webpage.png'

    // 下载图片
    const link = document.createElement('a')
    link.href = dataUrl
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    console.log('✅ PNG 导出成功，文件名:', filename)
    document.dispatchEvent(
      new CustomEvent('show-message', {
        detail: { type: 'success', message: 'PNG 已开始下载' },
      })
    )
  } catch (error) {
    console.error('❌ 导出PNG时发生错误:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    document.dispatchEvent(
      new CustomEvent('show-message', {
        detail: { type: 'error', message: '导出PNG失败: ' + errorMessage },
      })
    )
  } finally {
    // 通知父组件导出结束
    document.dispatchEvent(
      new CustomEvent('html-export-end', {
        detail: { type: 'PNG' },
      })
    )
  }
}

const exportAsHtml = async () => {
  console.log('📄 开始导出 HTML...')

  try {
    // 获取HTML内容
    const content = props.content
    if (!content) {
      document.dispatchEvent(
        new CustomEvent('show-message', {
          detail: { type: 'error', message: '无法获取网页内容' },
        })
      )
      return
    }

    // 通知父组件开始导出
    document.dispatchEvent(
      new CustomEvent('html-export-start', {
        detail: { type: 'HTML' },
      })
    )

    // 创建Blob
    const htmlBlob = new Blob([content], { type: 'text/html;charset=utf-8' })

    // 创建下载链接
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(htmlBlob)

    // 生成文件名
    const titleMatch = content.match(/<title>(.*?)<\/title>/i)
    const filename = titleMatch && titleMatch[1] ? `${titleMatch[1].trim()}.html` : 'index.html'

    downloadLink.download = filename

    // 触发下载
    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)

    console.log('✅ HTML 导出成功')
    document.dispatchEvent(
      new CustomEvent('show-message', {
        detail: { type: 'success', message: 'HTML 文件已开始下载' },
      })
    )
  } catch (error) {
    console.error('❌ 导出 HTML 时发生错误:', error)
    document.dispatchEvent(
      new CustomEvent('show-message', {
        detail: { type: 'error', message: '导出 HTML 失败' },
      })
    )
  } finally {
    // 通知父组件导出结束
    document.dispatchEvent(
      new CustomEvent('html-export-end', {
        detail: { type: 'HTML' },
      })
    )
  }
}

onMounted(() => {
  console.log('🚀 HTML渲染器已挂载')
  if (iframe.value) {
    renderHtml()
  } else {
    // 如果iframe还没准备好，稍后尝试
    setTimeout(renderHtml, 50)
  }

  // 监听来自父组件的导出事件
  console.log('📡 HTML渲染器开始监听导出事件')
  document.addEventListener('html-export-png', exportAsPng)
  document.addEventListener('html-export-html', exportAsHtml)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('html-export-png', exportAsPng)
  document.removeEventListener('html-export-html', exportAsHtml)
})

// 响应内容变化
watch(
  () => props.content,
  newContent => {
    console.log('📝 HTML内容已更新，长度:', newContent?.length || 0)
    renderHtml()
  }
)

// 计算iframe样式
const iframeStyle = computed(() => {
  switch (props.previewSize) {
    case 'tablet':
      return {
        width: '600px',
        height: '80%',
        margin: 'auto',
        border: '1px solid #ccc',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        transition: 'width 0.3s ease-in-out',
      }
    case 'mobile':
      return {
        width: '320px',
        height: '80%',
        margin: 'auto',
        border: '1px solid #ccc',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
        transition: 'width 0.3s ease-in-out',
      }
    default: // desktop
      return {
        width: '100%',
        height: '100%',
        border: 'none',
        borderRadius: '0',
        boxShadow: 'none',
        transition: 'width 0.3s ease-in-out',
      }
  }
})
</script>

<template>
  <div class="html-renderer h-full w-full flex items-center justify-center">
    <iframe
      ref="iframe"
      :style="iframeStyle"
      class="border dark:border-gray-600 bg-white shadow-lg rounded-lg"
      frameborder="0"
      sandbox="allow-scripts allow-popups allow-forms"
    ></iframe>
  </div>
</template>

<style scoped>
.html-renderer {
  width: 100%;
  height: 100%;
}
</style>
