<script lang="ts" setup>
import { fetchQuerySingleChatLogAPI } from '@/api/chatLog'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'
import { useChatStore } from '@/store'
import { Close, Copy, Delete, LoadingFour, Plus } from '@icon-park/vue-next'
import mdKatex from '@traptitech/markdown-it-katex'
import axios from 'axios'
import hljs from 'highlight.js'
import MarkdownIt from 'markdown-it'
import mila from 'markdown-it-link-attributes'
import { computed, inject, onUnmounted, ref, watch } from 'vue'

interface Props {
  chatId?: number
  inversion?: boolean
  error?: boolean
  content?: string
  modelType?: number
  status?: number
  loading?: boolean
  asRawText?: boolean
  videoUrl?: string
  model?: string
  drawId?: string
  customId?: string
  modelName?: string
  taskId?: string
  isLast?: boolean
  index: number
}

interface Emit {
  (ev: 'regenerate'): void
  (ev: 'delete'): void
  (ev: 'copy'): void
}

const props = defineProps<Props>()

const emit = defineEmits<Emit>()

const { isMobile } = useBasicLayout()
let intervalId: number | undefined

// 添加拓展模式状态变量
const isExtendMode = ref(false)

/**
 * 监听状态变化，管理定时器
 * 当状态为2（处理中）时，启动定时器定期查询消息状态
 * 当状态变为其他值时，清除定时器
 */
watch(
  () => props.status,
  currentStatus => {
    // 清除可能已经存在的定时器
    if (intervalId !== undefined) {
      clearInterval(intervalId)
      intervalId = undefined
    }

    // 当status为2（处理中）且有chatId时，启动定时器
    if (currentStatus === 2) {
      console.log(`开始监控视频消息状态: ${props.chatId}`)

      // 设置定时器，每10秒查询一次消息状态
      intervalId = window.setInterval(async () => {
        try {
          if (props.chatId) {
            const response = (await fetchQuerySingleChatLogAPI({
              chatId: props.chatId,
            })) as any

            const result = response.data

            if (result.status !== 2) {
              console.log(`视频消息处理完成: ${props.chatId}, 状态: ${result.status}`)
              clearInterval(intervalId)
              intervalId = undefined
            }
            chatStore.updateGroupChatSome(props.index, {
              status: result.status,
              content: result.content,
              imageUrl: result.imageUrl,
              taskId: result.taskId,
              videoUrl: result.videoUrl,
            })
          }
        } catch (error) {
          console.error('查询视频消息状态失败:', error)
        }
      }, 5000) // 每5秒执行一次
    }
  },
  { immediate: true }
)

// 组件卸载时清除定时器
onUnmounted(() => {
  if (intervalId !== undefined) {
    clearInterval(intervalId)
  }
})

const textRef = ref<HTMLElement>()
const chatStore = useChatStore()
const mdi = new MarkdownIt({
  linkify: true,
  highlight(code: string, language: string) {
    const validLang = !!(language && hljs.getLanguage(language))
    if (validLang) {
      const lang = language ?? ''
      return highlightBlock(hljs.highlight(code, { language: lang }).value, lang)
    }
    return highlightBlock(hljs.highlightAuto(code).value, '')
  },
})

const videoFileInfo = computed(() => props.videoUrl)
const onConversation = inject<any>('onConversation')

mdi.use(mila, { attrs: { target: '_blank', rel: 'noopener' } })
mdi.use(mdKatex, {
  blockClass: 'katexmath-block rounded-md p-[10px]',
  errorColor: ' #cc0000',
})

const text = computed(() => {
  const value = props.content ?? ''
  if (!props.asRawText) return mdi.render(value)
  return value
})

const extendMessage = ref('')

const fileInput = ref()
const isUploading = ref(false)
const selectedFile = ref<File | null>(null)
const previewUrl = ref('')
const uploadUrl = ref(`${import.meta.env.VITE_GLOB_API_URL}/upload/file`)

// 切换到拓展模式
const toggleExtendMode = () => {
  isExtendMode.value = !isExtendMode.value
  if (!isExtendMode.value) {
    clearSelectedFile()
    extendMessage.value = ''
  }
}

// 添加文件上传相关方法
const triggerFileUpload = () => {
  fileInput?.value?.click()
}

const handleFileSelect = async (event: Event) => {
  const input = event.target as HTMLInputElement
  const file = input?.files?.[0]
  if (file) {
    if (file.type.startsWith('image/')) {
      selectedFile.value = file
      // 创建临时预览URL
      previewUrl.value = URL.createObjectURL(file)
    }
    input.value = ''
  }
}

const clearSelectedFile = () => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
  selectedFile.value = null
  previewUrl.value = ''
}

const uploadFiles = async (file: File): Promise<string> => {
  try {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const currentDate = `${year}${month}/${day}`
    const dir = `userFiles/${currentDate}`

    const form = new FormData()
    form.append('file', file)

    const res = await axios.post(`${uploadUrl.value}?dir=${encodeURIComponent(dir)}`, form, {
      headers: { 'Content-Type': 'multipart/form-data' },
    })
    return res?.data?.data
  } catch (error) {
    console.error('上传文件失败:', error)
    return ''
  }
}

async function handleSubmitExtend() {
  if (!extendMessage.value.trim()) return

  let fileUrl = ''
  if (selectedFile.value) {
    isUploading.value = true
    fileUrl = await uploadFiles(selectedFile.value)
    isUploading.value = false
  }

  await onConversation({
    msg: extendMessage.value,
    action: 'extend',
    taskId: props.taskId,
    modelType: 2,
    model: 'luma-video',
    modelName: props.modelName,
    fileUrl: fileUrl,
  })

  isExtendMode.value = false
  extendMessage.value = ''
  clearSelectedFile()
}

function highlightBlock(str: string, lang?: string) {
  return `<pre class="code-block-wrapper"><div class="code-block-header"><span class="code-block-header__lang">${lang}</span><span class="code-block-header__copy">${t(
    'chat.copyCode'
  )}</span></div><code class="hljs code-block-body ${lang}">${str}</code></pre>`
}

function handleCopy() {
  emit('copy')
}

function handleDelete() {
  emit('delete')
}

defineExpose({ textRef })

const playbackState = ref('paused') // 默认状态为'paused'

const buttonGroupClass = computed(() => {
  return playbackState.value !== 'paused' ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
})

onUnmounted(() => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
})
</script>

<template>
  <div class="flex flex-col group max-w-full w-full">
    <div ref="textRef" class="leading-relaxed break-words w-full">
      <div class="w-full flex flex-col items-start" :class="isMobile ? '' : 'pr-10'">
        <!-- 卡片式视频/拓展容器 -->
        <div
          class="w-full text-base text-gray-800 dark:text-gray-100 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-850 p-0 border border-gray-100 dark:border-gray-700 shadow-sm flex flex-col relative font-sans leading-7 tracking-wide transition-all duration-300"
          :class="isMobile ? 'min-h-[300px]' : 'min-h-[150px]'"
          style="width: 100%"
        >
          <!-- 卡片标题 -->
          <div
            class="px-5 pt-3 pb-3 flex-shrink-0 border-b border-gray-200/30 dark:border-gray-700/30"
          >
            <div
              class="font-medium mb-0 text-gray-900 dark:text-gray-200 text-center text-lg pb-2 relative"
            >
              {{ isExtendMode ? '视频拓展' : 'AI 生成视频' }}
              <div
                class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[60px] h-[1px] bg-gradient-to-r from-transparent via-primary-500 to-transparent rounded"
              ></div>
            </div>
          </div>

          <!-- 卡片内容区域 -->
          <div
            class="px-5 flex-1 overflow-hidden relative"
            :class="isExtendMode ? 'h-auto' : 'h-auto'"
          >
            <!-- 正常视频显示模式 -->
            <div v-if="!isExtendMode && videoFileInfo" class="py-4 flex justify-center h-[40vh]">
              <video
                :src="videoFileInfo"
                controls
                :preload="props.isLast && props.status === 2 ? 'none' : 'auto'"
                class="rounded-md w-full"
              >
                您的浏览器不支持视频标签。
              </video>
            </div>

            <!-- 加载状态 -->
            <div v-if="status === 2" class="py-8 flex flex-col items-center justify-center h-full">
              <div class="loading-animation mb-3"><span></span></div>
              <span class="text-gray-500 dark:text-gray-400">正在生成视频，请稍候...</span>
            </div>

            <!-- 拓展模式 -->
            <div v-if="isExtendMode" class="py-4 h-[40vh]" style="animation: fadeIn 0.2s ease">
              <!-- 拓展输入区域 -->
              <div class="mb-4">
                <input
                  v-model="extendMessage"
                  type="text"
                  class="w-full px-3 py-2 border rounded-md bg-transparent dark:border-gray-600 dark:text-gray-200 focus:outline-none focus:ring-1 focus:ring-primary-500"
                  placeholder="请输入拓展描述"
                />
              </div>

              <!-- 图片上传区域 -->
              <div class="mt-4">
                <div
                  v-if="!selectedFile"
                  class="w-full h-40 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 dark:hover:border-primary-500 transition-colors"
                  @click="triggerFileUpload"
                >
                  <Plus class="text-gray-400 dark:text-gray-500 mb-2" size="24" />
                  <span class="text-sm text-gray-500 dark:text-gray-400">点击上传图片（可选）</span>
                </div>

                <div v-else class="relative w-full h-40">
                  <img
                    :src="previewUrl"
                    class="w-full h-full object-contain rounded-lg"
                    alt="预览图片"
                  />
                  <button
                    class="absolute top-2 right-2 p-1 rounded-full bg-black/50 hover:bg-black/70 text-white transition-colors"
                    @click="clearSelectedFile"
                  >
                    <Close size="16" />
                  </button>
                </div>
              </div>

              <input
                ref="fileInput"
                type="file"
                class="hidden"
                @change="handleFileSelect"
                accept="image/*"
              />
            </div>
          </div>

          <!-- 底部按钮区域 -->
          <div
            v-if="status === 3 && videoFileInfo"
            class="px-3 py-3 border-t border-gray-200 dark:border-gray-700 mt-auto bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-b-xl"
          >
            <div class="flex justify-between items-center">
              <div>
                <!-- 切换拓展模式按钮 -->
                <button
                  @click="toggleExtendMode"
                  class="btn btn-secondary btn-md px-4 py-2 w-24"
                  :class="{ 'btn-select': isExtendMode }"
                >
                  {{ isExtendMode ? '取消' : '拓展' }}
                </button>
              </div>

              <!-- 提交按钮 - 仅在拓展模式下显示 -->
              <button
                v-if="isExtendMode"
                @click="handleSubmitExtend"
                :disabled="isUploading || !extendMessage.trim()"
                class="btn btn-primary btn-md px-4 py-2"
              >
                <LoadingFour v-if="isUploading" size="20" class="animate-spin" />
                <span v-else>提交</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="flex transition-opacity duration-300 text-gray-700 opacity-0 group-hover:opacity-100"
    >
      <div class="mt-2 flex group overflow-visible">
        <div class="relative group-btn overflow-visible">
          <button
            class="btn-icon btn-sm btn-icon-action mx-1"
            @click="handleCopy"
            aria-label="复制"
          >
            <Copy />
          </button>
          <div v-if="!isMobile" class="tooltip tooltip-top">{{ t('chat.copy') }}</div>
        </div>

        <div class="relative group-btn overflow-visible">
          <button
            class="btn-icon btn-sm btn-icon-action mx-1"
            @click="handleDelete"
            aria-label="删除"
          >
            <Delete />
          </button>
          <div v-if="!isMobile" class="tooltip tooltip-top">{{ t('chat.delete') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 自定义滚动条样式 */
.scroll-content::-webkit-scrollbar {
  width: 4px;
}

.scroll-content::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-content::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.scroll-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}

/* 暗黑模式下的滚动条样式 */
:deep(.dark) .scroll-content::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

:deep(.dark) .scroll-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(107, 114, 128, 0.7);
}

/* 默认隐藏滚动条，悬停时显示 */
.scroll-content {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.scroll-content:hover {
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}
</style>
