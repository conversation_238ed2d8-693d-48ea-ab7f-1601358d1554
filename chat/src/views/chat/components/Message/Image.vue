<script lang="ts" setup>
import { fetchQuerySingleChatLogAPI } from '@/api/chatLog'
import { useChatStore } from '@/store'
import { message } from '@/utils/message'
import { Copy, Delete, NewPicture } from '@icon-park/vue-next'
import { computed, inject, onUnmounted, ref, watch } from 'vue'

import { useBasicLayout } from '@/hooks/useBasicLayout'
import { t } from '@/locales'

interface Props {
  inversion?: boolean
  content?: string
  modelType?: number
  status?: number
  loading?: boolean
  asRawText?: boolean
  imageUrl?: string
  progress?: string
  model?: string
  drawId?: string
  customId?: string
  modelName?: string
  index?: number
  chatId?: number
}

interface Emit {
  (ev: 'delete'): void
  (ev: 'copy'): void
}

const onConversation = inject<any>('onConversation')

const imageUrlArray = computed(() => {
  return props.imageUrl ? props.imageUrl.split(',') : []
})

const props = defineProps<Props>()

const emit = defineEmits<Emit>()
const ms = message()

const { isMobile } = useBasicLayout()
let intervalId: number | undefined

watch(
  () => props.status,
  currentStatus => {
    // 清除可能已经存在的定时器
    if (intervalId !== undefined) {
      clearInterval(intervalId)
      intervalId = undefined
    }

    // 当status为2时，启动定时器
    if (currentStatus === 2) {
      // 设置定时器，每5秒查询一次消息状态
      intervalId = window.setInterval(async () => {
        try {
          if (props.chatId && props.index !== undefined) {
            const response = (await fetchQuerySingleChatLogAPI({
              chatId: props.chatId,
            })) as any

            const result = response.data

            chatStore.updateGroupChatSome(props.index, {
              status: result.status,
              content: result.content,
              imageUrl: result.imageUrl,
              drawId: result.drawId,
              customId: result.customId,
              taskData: result.taskData,
              modelType: result.modelType,
            })
            if (result.status !== 2) {
              clearInterval(intervalId)
              intervalId = undefined
              // ms.success('创作已完成')
            }
          }
        } catch (error) {
          // 删除console.error行
        }
      }, 5000) // 每5秒执行一次
    }
  },
  { immediate: true }
)

// 组件卸载时清除定时器
onUnmounted(() => {
  if (intervalId !== undefined) {
    clearInterval(intervalId)
  }
  // 不再需要移除键盘事件，因为我们不再使用本地预览
  // window.removeEventListener('keydown', handleKeyDown)
  // 不再需要关闭预览，因为它现在不在此组件中
  // imagePreviewVisible.value = false
})

const textRef = ref<HTMLElement>()
const chatStore = useChatStore()

const imageUrl = computed(() => props.imageUrl)

const text = computed(() => {
  const value = props.content ?? ''
  return value
})

function handleCopy() {
  emit('copy')
}

function handleDelete() {
  emit('delete')
}

/* 提交放大绘制任务 */
async function handleUpsample(order: number) {
  try {
    if (!props.customId) return
    let extendObj
    extendObj = JSON.parse(props.customId)
    const button = extendObj.find((btn: any) => btn.customId.includes(`upsample::${order}`))
    if (button) {
      const drawCustomId = button.customId
      // ms.success('提交放大绘制任务成功、请等待绘制结束！');
      await onConversation({
        msg: t('chat.enlargeImagePrefix') + order + t('chat.enlargeImageSuffix'),
        action: 'UPSCALE',
        drawId: props.drawId,
        customId: drawCustomId,
        modelType: 2,
        model: 'midjourney',
        modelName: props.modelName,
      })
    } else {
    }
  } catch (error) {
    ms.error('提交放大绘制任务失败')
  }
}

/* 提交变换绘制任务 */
async function handleVariation(order: number) {
  try {
    if (!props.customId) return
    let extendObj
    extendObj = JSON.parse(props.customId)
    const button = extendObj.find((btn: any) => btn.customId.includes(`variation::${order}`))
    if (button) {
      const drawCustomId = button.customId
      // ms.success('提交变换绘制任务成功、请等待绘制结束！');
      await onConversation({
        msg: t('chat.transformImagePrefix') + order + t('chat.transformImageSuffix'),
        action: 'UPSCALE',
        drawId: props.drawId,
        customId: drawCustomId,
        modelType: 2,
        model: 'midjourney',
        modelName: props.modelName,
      })
    } else {
    }
  } catch (error) {
    // 删除ms.error行
  }
}

/* 提交绘制任务 */
async function handlePicReader(order: number) {
  try {
    if (!props.customId) return
    let extendObj
    extendObj = JSON.parse(props.customId)
    const button = extendObj.find((btn: any) =>
      btn.customId.includes(`MJ::Job::PicReader::${order}`)
    )
    if (button) {
      const drawCustomId = button.customId
      // ms.success('提交变换绘制任务成功、请等待绘制结束！');
      await onConversation({
        msg: `绘制第 ${order}张图片`,
        action: 'PICREADER',
        drawId: props.drawId,
        customId: drawCustomId,
        modelType: 2,
        model: 'midjourney',
        modelName: props.modelName,
      })
    } else {
    }
  } catch (error) {
    // 删除ms.error行
  }
}

/* 提交扩图绘制任务 */
async function handleOutpaint(order: number) {
  try {
    if (!props.customId) return
    let extendObj
    extendObj = JSON.parse(props.customId)
    const button = extendObj.find((btn: any) => btn.customId.includes(`Outpaint::${order}`))
    if (button) {
      const drawCustomId = button.customId
      // ms.success('提交扩图绘制任务成功、请等待绘制结束！');
      await onConversation({
        msg: t('chat.expandDrawing'),
        action: 'UPSCALE',
        drawId: props.drawId,
        customId: drawCustomId,
        modelType: 2,
        model: 'midjourney',
        modelName: props.modelName,
      })
    } else {
    }
  } catch (error) {
    ms.error('提交扩图绘制任务失败')
  }
}

/* 提交高级变换绘制任务 */
async function handleSuperVariation(order: string) {
  try {
    if (!props.customId) return
    let extendObj
    extendObj = JSON.parse(props.customId)
    const button = extendObj.find((btn: any) => btn.customId.includes(`${order}`))
    if (button) {
      const drawCustomId = button.customId
      // ms.success('提交变换绘制任务成功、请等待绘制结束！');
      await onConversation({
        msg: t('chat.advancedTransform'),
        action: 'UPSCALE',
        drawId: props.drawId,
        customId: drawCustomId,
        modelType: 2,
        model: 'midjourney',
        modelName: props.modelName,
      })
    } else {
    }
  } catch (error) {
    ms.error('提交变换绘制任务失败')
  }
}

/* 提交平移绘制任务 */
async function handlePan(order: string) {
  try {
    if (!props.customId) return
    let extendObj
    extendObj = JSON.parse(props.customId)

    const button = extendObj.find((btn: any) => btn.customId.includes(`pan_${order}`))
    if (button) {
      const drawCustomId = button.customId

      await onConversation({
        msg: t('chat.translateImage'),
        action: 'UPSCALE',
        drawId: props.drawId,
        customId: drawCustomId,
        modelType: 2,
        model: 'midjourney',
        modelName: props.modelName,
      })
    } else {
    }
  } catch (error) {}
}

// 打开图片预览的方法
function openImagePreview(index: number) {
  // 通知父组件打开预览器
  if (onOpenImagePreviewer) {
    // 准备MJ相关数据
    const mjData = {
      model: props.model,
      status: props.status,
      customId: props.customId,
      drawId: props.drawId,
      modelName: props.modelName,
    }

    // 将MJ数据传递给预览器
    onOpenImagePreviewer(imageUrlArray.value, index, mjData)
  }
}

const onOpenImagePreviewer =
  inject<(imageUrls: string[], initialIndex: number, mjData?: any) => void>('onOpenImagePreviewer')

defineExpose({ textRef })
</script>

<template>
  <div class="flex flex-col group w-full overflow-visible">
    <div ref="textRef" class="leading-relaxed break-words w-full overflow-visible">
      <div class="flex flex-col items-start w-full overflow-visible">
        <div class="w-full overflow-visible">
          <div v-text="text" class="whitespace-pre-wrap" />
        </div>
      </div>
    </div>
    <div class="text-wrap rounded-lg min-w-12 text-gray-800 dark:text-gray-400 overflow-visible">
      <div class="overflow-visible">
        <div class="overflow-visible">
          <div
            class="my-1 flex w-auto overflow-visible"
            :style="{
              maxWidth: isMobile ? '100%' : '',
              maxHeight: isMobile ? '' : '30vh',
              objectFit: 'contain',
            }"
          >
            <!-- 图像加载区域容器 -->
            <div class="relative flex w-full overflow-visible">
              <!-- 使用单一transition组件和mode="out-in"确保先出后进 -->
              <transition name="fade-transition" mode="out-in">
                <!-- 预加载框 - 当status为2时显示 -->
                <div
                  v-if="status === 2 && !imageUrl"
                  key="loading"
                  class="w-full flex items-center justify-center rounded-lg bg-gray-50 dark:bg-gray-800 mb-1 mr-4 shadow-sm border border-gray-100 dark:border-gray-700"
                  :style="{
                    width: isMobile ? '100%' : '30vh',
                    height: isMobile ? 'calc(100vw - 32px)' : '30vh',
                    maxWidth: '100%',
                    maxHeight: isMobile ? '' : '30vh',
                    aspectRatio: '1',
                  }"
                >
                  <div class="relative flex items-center justify-center">
                    <!-- 简约现代圆形进度条 -->
                    <svg class="w-16 h-16" viewBox="0 0 100 100">
                      <!-- 静态背景圆 -->
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="none"
                        stroke-width="4"
                        stroke="rgba(200, 200, 200, 0.2)"
                        class="dark:stroke-gray-700"
                      />

                      <!-- 主旋转环 -->
                      <circle
                        cx="50"
                        cy="50"
                        r="40"
                        fill="none"
                        stroke-width="4"
                        stroke="currentColor"
                        stroke-linecap="round"
                        class="text-primary-500 dark:text-primary-400 origin-center animate-spin"
                        stroke-dasharray="80 180"
                        style="animation-duration: 2s"
                      />
                    </svg>

                    <!-- 中间的图标 - 使用Icon-Park的Picture图标 -->
                    <NewPicture
                      class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-primary-500 dark:text-primary-400"
                      theme="outline"
                      size="24"
                      fill="currentColor"
                    />
                  </div>
                </div>

                <!-- 图片显示区域 -->
                <div v-else-if="imageUrlArray.length > 0" key="images" class="flex w-full">
                  <img
                    v-for="(imageSrc, index) in imageUrlArray"
                    :key="index"
                    :src="imageSrc"
                    alt="图片"
                    @click="openImagePreview(index)"
                    class="rounded-lg flex mb-1 mr-4 cursor-pointer hover:opacity-90 transition-opacity"
                    :style="{
                      maxWidth: '100%',
                      maxHeight: '100%',
                      objectFit: 'contain',
                    }"
                  />
                </div>
              </transition>
            </div>
          </div>

          <div v-if="model?.includes('midjourney') && imageUrl && status === 3">
            <div
              v-if="customId?.includes('::upsample::1::')"
              class="mt-2 flex flex-wrap items-center justify-between"
            >
              <div class="flex flex-wrap w-full">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 my-1">
                  <button
                    @click="handleUpsample(1)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.U1') }}
                  </button>
                  <button
                    @click="handleUpsample(2)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.U2') }}
                  </button>
                  <button
                    @click="handleUpsample(3)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.U3') }}
                  </button>
                  <button
                    @click="handleUpsample(4)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.U4') }}
                  </button>
                </div>
              </div>
            </div>
            <div
              v-if="customId?.includes('::variation::1::')"
              class="mt-2 flex items-center justify-between"
            >
              <div class="flex">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 my-1">
                  <button
                    @click="handleVariation(1)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.V1') }}
                  </button>
                  <button
                    @click="handleVariation(2)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.V2') }}
                  </button>
                  <button
                    @click="handleVariation(3)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.V3') }}
                  </button>
                  <button
                    @click="handleVariation(4)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.V4') }}
                  </button>
                </div>
              </div>
            </div>
            <div
              v-if="customId?.includes('::PicReader::')"
              class="mt-2 flex items-center justify-between"
            >
              <div class="flex">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 my-1">
                  <button
                    @click="handlePicReader(1)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    绘制 1️⃣
                  </button>
                  <button
                    @click="handlePicReader(2)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    绘制 2️⃣
                  </button>
                  <button
                    @click="handlePicReader(3)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    绘制 3️⃣
                  </button>
                  <button
                    @click="handlePicReader(4)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    绘制 4️⃣
                  </button>
                </div>
              </div>
            </div>
            <div
              v-if="customId?.includes('::pan_left::1::')"
              class="mt-2 flex items-center justify-between"
            >
              <div class="flex">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 my-1">
                  <button
                    @click="handlePan('left')"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.panLeft') }}
                  </button>
                  <button
                    @click="handlePan('right')"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.panRight') }}
                  </button>
                  <button
                    @click="handlePan('up')"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.panUp') }}
                  </button>
                  <button
                    @click="handlePan('down')"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.panDown') }}
                  </button>
                </div>
              </div>
            </div>
            <div
              v-if="customId?.includes('Outpaint::50')"
              class="mt-2 flex items-center justify-between"
            >
              <div class="flex">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 my-1">
                  <button
                    v-if="customId?.includes('Outpaint::50')"
                    @click="handleOutpaint(75)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.zoomIn15x') }}
                  </button>
                  <button
                    v-if="customId?.includes('Outpaint::50')"
                    @click="handleOutpaint(50)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.zoomIn2x') }}
                  </button>

                  <button
                    v-if="customId?.includes('low_variation')"
                    @click="handleSuperVariation('low')"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.minorTransform') }}
                  </button>
                  <button
                    v-if="customId?.includes('low_variation')"
                    @click="handleSuperVariation('high')"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800"
                  >
                    {{ t('chat.strongTransform') }}
                  </button>

                  <!-- <button
                    @click="handleVariation(drawItemInfo, 1)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800 "
                  >
                    区域重绘
                  </button> -->
                </div>
              </div>
            </div>
          </div>

          <!-- <div v-if="model?.includes('dall') && imageUrl">
            <div class="my-2 flex items-center justify-between">
              <div class="flex">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <button
                    @click="handleUpsample(1)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800 "
                  >
                    🔄 再画一张
                  </button>
                  <button
                    @click="handleUpsample(2)"
                    class="w-24 shadow-sm rounded-md py-1 text-sm hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-gray-400 ring-1 ring-inset ring-gray-100 dark:bg-gray-800 dark:ring-gray-800 "
                  >
                    🔀 换种风格
                  </button>
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <div
      class="flex opacity-0 transition-opacity duration-300 group-hover:opacity-100 text-gray-700"
    >
      <div>
        <div class="mt-1 flex">
          <div class="relative group overflow-visible">
            <button
              class="btn-icon btn-sm btn-icon-action mx-1"
              @click="handleCopy"
              aria-label="复制"
            >
              <Copy class="flex" />
            </button>
            <div v-if="!isMobile" class="tooltip tooltip-top">{{ t('chat.copy') }}</div>
          </div>
          <div class="relative group overflow-visible">
            <button
              class="btn-icon btn-sm btn-icon-action mx-1"
              @click="handleDelete"
              aria-label="删除"
            >
              <Delete class="flex" />
            </button>
            <div v-if="!isMobile" class="tooltip tooltip-top">{{ t('chat.delete') }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.98);
}

/* 确保图片和加载框位置一致，避免移动效果 */
.flex > div {
  position: relative;
}

/* 确保Safari浏览器中的旋转效果正常 */
@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance: none) {
    .animate-spin {
      animation-name: spin;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
    }

    @keyframes spin {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  }
}

/* 文字渐变效果 */
.text-gradient {
  background-image: linear-gradient(to right, #7928ca, #ff0080);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

/* 文字淡入效果 */
.fade-in-text {
  animation: fadeIn 1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 淡入淡出过渡效果 */
.fade-transition-enter-active,
.fade-transition-leave-active {
  transition: opacity 0.35s ease;
}

.fade-transition-enter-from,
.fade-transition-leave-to {
  opacity: 0;
}
</style>
