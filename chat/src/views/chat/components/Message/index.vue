<script setup lang="ts">
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { message } from '@/utils/message'
import { computed, inject, provide, ref } from 'vue'
import Avatar from './Avatar.vue'
import ImageComponent from './Image.vue'
import MermaidComponent from './Mermaid/index.vue'
import MindMapComponent from './MindMap/index.vue'
import MusicComponent from './Music.vue'
import TextComponent from './Text/index.vue'
import VideoComponent from './Video.vue'

import { useGlobalStoreWithOut } from '@/store'
import { copyText } from '@/utils/format'

interface Props {
  chatId?: number
  dateTime?: string
  content?: string
  model?: string
  modelName?: string
  modelType?: number
  status?: number
  role?: string
  loading?: boolean
  imageUrl?: string
  ttsUrl?: string
  useFileSearch?: boolean
  fileUrl?: string
  videoUrl?: string
  audioUrl?: string
  drawId?: string
  extend?: string
  customId?: string
  modelAvatar?: string
  action?: string
  taskData?: string
  pluginParam?: string
  progress?: string
  index: number
  promptReference?: string
  networkSearchResult?: string
  fileVectorResult?: string
  tool_calls?: string
  isLast?: boolean
  usingNetwork?: boolean
  usingDeepThinking?: boolean
  usingMcpTool?: boolean
  reasoningText?: string
  taskId?: string
  isWorkflowMessage?: boolean
  nodeType?: string
  stepName?: string
  workflowProgress?: number
}

// 添加计算属性判断是否是用户消息
const isUserMessage = computed(() => props.role === 'user')

// 添加判断是否应显示工作流状态框
const showWorkflowCard = computed(() => {
  return !isUserMessage.value && props.isWorkflowMessage
  // return !isUserMessage.value
})

interface Emit {
  (ev: 'regenerate'): void
  (ev: 'delete'): void
}

const { isMobile } = useBasicLayout()
const globalStore = useGlobalStoreWithOut()

// 获取预览器状态
const isPreviewerVisible = computed(
  () =>
    globalStore.showHtmlPreviewer ||
    globalStore.showTextEditor ||
    globalStore.showImagePreviewer ||
    globalStore.isMarkdownPreviewerVisible
)

const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const ms = message()

const textRef = ref<HTMLElement>()

const messageRef = ref<HTMLElement>()

// 从父组件接收onOpenImagePreviewer
const onOpenImagePreviewer =
  inject<(imageUrls: string[], initialIndex: number, mjData?: any) => void>('onOpenImagePreviewer')

// 将onOpenImagePreviewer提供给子组件，确保依赖注入链不断
provide('onOpenImagePreviewer', onOpenImagePreviewer)

function handleDetele() {
  emit('delete')
}

function handleCopy() {
  copyText({ text: props.content ?? '' })
  props.content && ms.success('复制成功！')
}

function handleRegenerate() {
  messageRef.value?.scrollIntoView()
  emit('regenerate')
}
</script>

<template>
  <div ref="messageRef" class="flex w-full my-2 overflow-visible items-start flex-row">
    <div
      v-if="!isUserMessage && !isMobile && !isPreviewerVisible"
      class="items-center justify-center mr-2 rounded-full group-btn relative flex-shrink-0"
    >
      <Avatar
        v-if="!isUserMessage"
        :image="isUserMessage"
        :model="model"
        :modelAvatar="modelAvatar"
      />
      <div class="tooltip tooltip-top">{{ modelName }}</div>
    </div>

    <div class="overflow-visible text-sm items-start w-full">
      <!-- 工作流状态卡片 - 当消息被标记为工作流状态消息时显示 -->
      <div v-if="showWorkflowCard" class="w-full">
        <div
          @click="() => globalStore.updateWorkflowPreviewer(!globalStore.showWorkflowPreviewer)"
          class="workflow-card flex flex-col gap-6 py-6 shadow-sm w-full cursor-pointer hover:bg-accent/50 transition-colors"
        >
          <div class="grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6">
            <div class="leading-none font-semibold">
              <span class="workflow-card-title text-lg">{{ nodeType || '研究中' }}</span>
            </div>
          </div>
          <div class="flex items-center px-6">
            <div class="flex w-full flex-col">
              <!-- 显示当前步骤名称 -->
              <span
                class="relative flex h-[2em] items-center overflow-hidden text-muted-foreground flex-grow text-sm"
              >
                <div
                  class="absolute w-fit"
                  style="transition: 0.3s ease-in-out; opacity: 1; transform: none"
                >
                  {{ stepName || '正在处理中...' }}
                </div>
              </span>

              <!-- 进度条 -->
              <div
                v-if="workflowProgress"
                class="w-full bg-gray-200 rounded-full h-2.5 mt-2 dark:bg-gray-700 relative overflow-hidden"
              >
                <!-- 进度条填充部分 -->
                <div
                  class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                  :style="{ width: `${workflowProgress * 100}%` }"
                ></div>

                <!-- 光带效果 - 当loading为true时显示 -->
                <div v-if="loading" class="glow-band"></div>
              </div>

              <div class="flex justify-end mt-3">
                <button
                  @click.stop="() => globalStore.updateWorkflowPreviewer(false)"
                  class="btn btn-secondary btn-md workflow-card-button"
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-else class="flex items-end gap-1 flex-row">
        <MindMapComponent
          v-if="pluginParam === 'mind-map' && !isUserMessage"
          ref="textRef"
          :isUserMessage="isUserMessage"
          :drawId="drawId"
          :extend="extend"
          :customId="customId"
          :content="content"
          :modelType="modelType"
          :imageUrl="imageUrl"
          :ttsUrl="ttsUrl"
          :model="model"
          :modelName="modelName"
          :loading="loading"
          :status="status"
          :index="index"
          @regenerate="handleRegenerate"
          @copy="handleCopy"
          @delete="handleDetele"
        />
        <MermaidComponent
          v-else-if="pluginParam === 'mermaid' && !isUserMessage"
          ref="textRef"
          :isUserMessage="isUserMessage"
          :drawId="drawId"
          :extend="extend"
          :customId="customId"
          :content="content"
          :modelType="modelType"
          :ttsUrl="ttsUrl"
          :model="model"
          :modelName="modelName"
          :loading="loading"
          :status="status"
          :index="index"
          @regenerate="handleRegenerate"
          @copy="handleCopy"
          @delete="handleDetele"
        />
        <VideoComponent
          v-else-if="(model === 'luma-video' || model === 'cog-video') && !isUserMessage"
          ref="textRef"
          :chatId="chatId"
          :isUserMessage="isUserMessage"
          :drawId="drawId"
          :extend="extend"
          :customId="customId"
          :content="content"
          :modelType="modelType"
          :videoUrl="videoUrl"
          :model="model"
          :modelName="modelName"
          :loading="loading"
          :status="status"
          :action="action"
          :taskData="taskData"
          :taskId="taskId"
          :modelAvatar="modelAvatar"
          :isLast="isLast"
          :index="index"
          @regenerate="handleRegenerate"
          @copy="handleCopy"
          @delete="handleDetele"
        />
        <MusicComponent
          v-else-if="model === 'suno-music' && !isUserMessage"
          ref="textRef"
          :chatId="chatId"
          :isUserMessage="isUserMessage"
          :drawId="drawId"
          :extend="extend"
          :customId="customId"
          :content="content"
          :modelType="modelType"
          :imageUrl="imageUrl"
          :ttsUrl="ttsUrl"
          :audioUrl="audioUrl"
          :videoUrl="videoUrl"
          :model="model"
          :modelName="modelName"
          :loading="loading"
          :status="status"
          :action="action"
          :taskData="taskData"
          :modelAvatar="modelAvatar"
          :index="index"
          @regenerate="handleRegenerate"
          @copy="handleCopy"
          @delete="handleDetele"
        />

        <ImageComponent
          v-else-if="modelType === 2 && !isUserMessage"
          ref="textRef"
          :isUserMessage="isUserMessage"
          :drawId="drawId"
          :extend="extend"
          :customId="customId"
          :content="content"
          :modelType="modelType"
          :imageUrl="imageUrl"
          :ttsUrl="ttsUrl"
          :model="model"
          :modelName="modelName"
          :loading="loading"
          :status="status"
          :progress="progress"
          :index="index"
          :chatId="chatId"
          @regenerate="handleRegenerate"
          @copy="handleCopy"
          @delete="handleDetele"
        />
        <TextComponent
          v-else
          ref="textRef"
          :index="index"
          :modelName="modelName"
          :chatId="chatId"
          :isUserMessage="isUserMessage"
          :content="content"
          :modelType="modelType"
          :imageUrl="imageUrl"
          :videoUrl="videoUrl"
          :ttsUrl="ttsUrl"
          :fileUrl="fileUrl"
          :useFileSearch="useFileSearch"
          :model="model"
          :loading="loading"
          :promptReference="promptReference"
          :networkSearchResult="networkSearchResult"
          :fileVectorResult="fileVectorResult"
          :tool_calls="tool_calls"
          :isLast="isLast"
          :usingNetwork="usingNetwork"
          :usingDeepThinking="usingDeepThinking"
          :usingMcpTool="usingMcpTool"
          :reasoningText="reasoningText"
          :isWorkflowMessage="isWorkflowMessage"
          @regenerate="handleRegenerate"
          @copy="handleCopy"
          @delete="handleDetele"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less">
/* 工作流状态框组件样式 */
.workflow-card {
  --border: rgba(0, 0, 0, 0.1);
  --card: #ffffff;
  --card-foreground: rgba(0, 0, 0, 0.72);
  --background: #ffffff;
  --input: rgba(0, 0, 0, 0.1);
  --ring: rgba(0, 0, 0, 0.2);
  --accent: rgba(0, 0, 0, 0.05);
  --accent-foreground: rgba(0, 0, 0, 0.9);
  --muted-foreground: rgba(0, 0, 0, 0.5);

  border-color: var(--border);
  background-color: var(--card);
  color: var(--card-foreground);
  border-radius: 0.625rem;
  border-width: 1px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dark .workflow-card {
  --border: rgba(255, 255, 255, 0.1);
  --card: rgba(30, 30, 30, 1);
  --card-foreground: rgba(255, 255, 255, 0.9);
  --background: rgba(30, 30, 30, 1);
  --input: rgba(255, 255, 255, 0.1);
  --ring: rgba(255, 255, 255, 0.2);
  --accent: rgba(255, 255, 255, 0.05);
  --accent-foreground: rgba(255, 255, 255, 0.9);
  --muted-foreground: rgba(255, 255, 255, 0.5);
}

.workflow-card-title {
  background-image: linear-gradient(45deg, #3b82f6, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* 光带效果动画 */
@keyframes glowBandAnim {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.glow-band {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    rgba(255, 255, 255, 0.5),
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: glowBandAnim 1.5s infinite linear;
  pointer-events: none;
  z-index: 1;
}

.dark .glow-band {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.05),
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05),
    transparent
  );
}
</style>
