{"language": "en", "common": {"add": "Add", "addSuccess": "Add Success", "edit": "Edit", "editSuccess": "Edit Success", "delete": "Delete", "deleteSuccess": "Delete Success", "save": "Save", "saveSuccess": "Save Success", "reset": "Reset", "action": "Action", "export": "Export", "exportSuccess": "Export Success", "import": "Import", "importSuccess": "Import Success", "clear": "Clear", "clearSuccess": "Clear Success", "yes": "Yes", "no": "No", "update": "Update", "download": "Download", "noData": "No Data", "wrong": "Something went wrong, please try again later.", "success": "Success", "failed": "Failed", "verify": "Verify", "unauthorizedTips": "Unauthorized, please verify first.", "confirm": "Confirm", "cancel": "Cancel"}, "chat": {"newChatButton": "New Chat", "placeholder": "Ask me anything...(Shift + Enter = line break)", "placeholderMobile": "Ask me anything...", "copy": "Copy", "copied": "<PERSON>pied", "copyCode": "Copy Code", "clearChat": "Clear Chat", "clearChatConfirm": "Are you sure to clear this chat?", "exportImage": "Export Image", "exportImageConfirm": "Are you sure to export this chat to png?", "exportSuccess": "Export Success", "exportFailed": "Export Failed", "usingContext": "Context Mode", "turnOnContext": "In the current mode, sending messages will carry previous chat records.", "turnOffContext": "In the current mode, sending messages will not carry previous chat records.", "deleteMessage": "Delete Message", "deleteMessageConfirm": "Are you sure to delete this message?", "deleteHistoryConfirm": "Are you sure to clear this history?", "clearHistoryConfirm": "Are you sure to clear chat history?", "preview": "Preview", "showRawText": "Show as raw text", "size": "Size:", "generatedContentDisclaimer": "AI can make mistakes. Consider checking important information. All rights reserved ©", "square1": "Square [1:1]", "illustration": "Illustration [4:3]", "wallpaper": "Wallpaper [16:9]", "media": "Media [3:4]", "poster": "Poster [9:16]", "square": "Square", "landscape": "Landscape", "portrait": "Portrait", "chatDialogue": "Chat <PERSON>", "startNewConversationPrompt": "Click the button below to start a new conversation", "newConversation": "New Conversation", "networkModeEnabledContextInvalid": "Network mode enabled, context invalidated!", "networkModeDisabled": "Network mode disabled!", "pointsMall": "Points Mall", "toggleTheme": "Toggle Theme", "signInReward": "Sign-In Reward", "networkMode": "Network Mode", "searchHistoryConversations": "Search History Conversations", "announcement": "Announcement", "clear": "Clear", "remaining": "", "ordinaryPoints": "Ordinary Points", "advancedPoints": "Advanced Points", "drawingPoints": "Drawing Points", "points": "Points", "clearConversation": "Clear Conversation", "clearAllNonFavoriteConversations": "Clear all non-favorite conversations?", "more": "More", "collapse": "Collapse", "myApps": "My Apps", "appSquare": "App Square", "favorites": "Favorites", "todayConversations": "Today", "Conversations": "", "historyConversations": "History", "favoriteConversations": "Favorite", "unfavorite": "Unfavorite", "rename": "<PERSON><PERSON>", "deleteConversation": "Delete", "me": "You", "onlineSearch": "Online Search", "mindMap": "Mind Map", "fileAnalysis": "File Analysis", "delete": "Delete", "regenerate": "Regenerate", "pause": "Pause", "loading": "Loading...", "readAloud": "<PERSON>", "vipCenter": "VIP Center", "U1": "🔍 U1", "U2": "🔍 U2", "U3": "🔍 U3", "U4": "🔍 U4", "V1": "🪄 V1", "V2": "🪄 V2", "V3": "🪄 V3", "V4": "🪄 V4", "panLeft": "⬅️ Pan Left", "panRight": "➡️ Pan Right", "panUp": "⬆️ Pan Up", "panDown": "⬇️ Pan Down", "zoomIn15x": "↔️ Zoom 1.5x", "zoomIn2x": "↔️ Zoom 2x", "minorTransform": "Vary(Subtle)", "strongTransform": "<PERSON><PERSON>(Strong)", "enlargeImage": "Enlarge image {order}", "transformImage": "Transform image {order}", "expandDrawing": "Expand drawing", "advancedTransform": "Advanced transform", "translateImage": "Translate image", "enlargeImagePrefix": "Enlarge image ", "enlargeImageSuffix": "", "transformImagePrefix": "Transform image ", "transformImageSuffix": "", "imageToImage": "Image to Image", "faceConsistency": "Face Consistency", "styleConsistency": "Style Consistency", "selectAppOrTopic": "Select an application or topic for quick conversation"}, "app": {"sampleTemplate": "Sample Template", "exploreInfinitePossibilities": "Explore infinite possibilities, create a smart future with AI", "searchAppNameQuickFind": "Search app names, quick find applications...", "allCategories": "All Categories", "noModelConfigured": "No specific application model has been configured by the administrator, please contact them to set it up~"}, "setting": {"setting": "Setting", "general": "General", "advanced": "Advanced", "config": "Config", "avatarLink": "Avatar Link", "name": "Name", "description": "Description", "role": "Role", "resetUserInfo": "Reset UserInfo", "chatHistory": "ChatHistory", "theme": "Theme", "language": "Language", "api": "API", "reverseProxy": "Reverse Proxy", "timeout": "Timeout", "socks": "Socks", "httpsProxy": "HTTPS Proxy", "balance": "API Balance", "sign": "Signature"}, "store": {"siderButton": "Prompt Store", "local": "Local", "online": "Online", "title": "Title", "description": "Description", "clearStoreConfirm": "Whether to clear the data?", "importPlaceholder": "Please paste the JSON data here", "addRepeatTitleTips": "Title duplicate, please re-enter", "addRepeatContentTips": "Content duplicate: {msg}, please re-enter", "editRepeatTitleTips": "Title conflict, please revise", "editRepeatContentTips": "Content conflict {msg} , please re-modify", "importError": "Key value mismatch", "importRepeatTitle": "Title repeatedly skipped: {msg}", "importRepeatContent": "Content is repeatedly skipped: {msg}", "onlineImportWarning": "Note: Please check the JSON file source!", "downloadError": "Please check the network status and JSON file validity"}, "draw": {"use": "Use", "download": "Download", "delete": "Delete", "zoom": "Zoom:", "U1": "U1", "U2": "U2", "U3": "U3", "U4": "U4", "regenerateOnce": "Regenerate", "transform": "Transform:", "V1": "V1", "V2": "V2", "V3": "V3", "V4": "V4", "pan": "Pan:", "panLeft": "⬅️", "panRight": "➡️", "panUp": "⬆️", "panDown": "⬇️", "transformZoom": "Zoom Transform", "zoom1_5x": "Zoom 1.5x", "zoom2x": "Zoom 2x", "minorTransform": "Minor Transform", "strongTransform": "Strong Transform", "regionalRedraw": "Regional Redraw", "regionalRedraw1": "Regional Redraw (Select the Area to Change)", "submitTask": "Submit Task", "selectSuiteForZoom": "Action: Select a suite to zoom", "selectSuiteForTransform": "Action: Select a suite for transformation", "regeneratingImage": "Action: Regenerating the image", "drawingInProgress": "Action: Rapid drawing in progress...", "tryDifferentPrompt": "Execute: Try a different prompt!", "statusWaiting": "Waiting", "statusDrawing": "Drawing", "statusSuccess": "Success", "statusFailure": "Failure", "statusTimeout": "Timeout", "downloadImageTitle": "Download Image", "downloadImageContent": "Download the current image", "downloadButtonText": "Download", "cancelButtonText": "Cancel", "deleteRecordTitle": "Delete Record", "deleteRecordContent": "Delete the current drawing record?", "deleteButtonText": "Delete", "submitZoomDrawingSuccess": "Zoom drawing task submitted successfully, please wait for it to finish!", "submitRedrawSuccess": "Redraw task submitted successfully, please wait for it to finish!", "submitTransformDrawingSuccess": "Transform drawing task submitted successfully, please wait for it to finish!", "submitEnlargeDrawingSuccess": "Enlarge drawing task submitted successfully, please wait for it to finish!", "submitAdvancedTransformDrawingSuccess": "Advanced transform drawing task submitted successfully, please wait for it to finish!", "submitRegionalRedrawSuccess": "Regional redraw task submitted successfully, please wait for it to finish!", "drawingRecordDeleted": "Drawing record has been deleted!", "queueing": "Queueing...", "drawing": "Drawing...", "storing": "Storing image...", "drawingFailed": "Drawing Failed", "pointsRefunded": "Points Refunded!", "submitDrawingTaskSuccess": "Drawing task submitted successfully, please wait for it to finish!", "defaultStyle": "Default Style", "expressiveStyle": "Expressive Style", "cuteStyle": "Cute Style", "scenicStyle": "Scenic Style", "standardQuality": "Standard", "generalQuality": "General", "highDefinitionQuality": "High Definition", "ultraHighDefinitionQuality": "Ultra High Definition", "enterDescription": "Please enter descriptive words!", "optimizationFailed": "Optimization failed!", "professionalDrawing": "Professional Drawing", "parameterExplanation": "Parameter Explanation: Generate image size ratio", "imageSize": "Image Size", "modelSelection": "Model Selection", "tooltipMJ": "MJ: General-purpose realistic model", "tooltipNIJI": "NIJI: Anime style, suitable for 2D models", "version": "Version", "style": "Style", "parameters": "Parameters", "parametersTooltip": "Use parameters wisely to achieve more ideal results!", "quality": "Quality", "chaos": "Chaos", "chaosDescription": "Value range: 0-100, --chaos or --c", "chaosExplanation": "Chaos level, can be understood as the space for AI to think outside the box", "chaosAdvice": "The smaller the value, the more reliable, with the default of 0 being the most precise", "stylization": "Stylization", "stylizationDescription": "Stylization: --stylize or --s, range 1-1000", "parameterExplanation1": "Parameter explanation: The higher the number, the richer and more artistic the visual presentation", "setting": "Setting", "carryParameters": "Carry Parameters", "autoCarryParameters": "Whether to automatically carry parameters", "carryOn": "On: Carries the parameters we have configured", "carryOff": "Off: Uses the parameters we customize in the command", "imageToImage": "Image to Image", "clickOrDrag": "Click or drag an image here to use as input", "supportFormats": "Supports PNG and JPG formats", "remainingPoints": "Remaining Points", "refresh": "Refresh", "accountInfo": "Account Information", "points": "Points", "paintingSingleUse": "Painting:", "imageGenerationSingleUse": "Generation:", "enlargementSingleUse": "Enlargement:", "submitDrawingTask": "Enter keywords, submit drawing task", "optimize": "Optimize", "enterDrawingKeywords": "Enter drawing keywords. For example: A colorful cat, cute, cartoon", "unnecessaryElements": "Unnecessary Elements", "exclusionPrompt": "Example: Generate a room image, but exclude the bed, you can fill in 'bed'!", "workingContents": "Working Contents", "currentTasks": "Current tasks in progress", "goToAIDrawingSquare": "Click to go to the AI Drawing Square", "tasksInProgress": "tasks are currently in progress. Please wait patiently for the drawing to complete. You can visit other pages and return later to see the results!", "myDrawings": "My Drawings", "aiDrawingSquare": "AI Drawing Square", "sizeAdjustment": "Size Adjustment", "keywordSearchPlaceholder": "Prompt Keyword Search"}, "pay": {"membershipMarket": "Membership Market", "sizeAdjustment": "Size Adjustment", "memberPackage": "Limited Time Member Package", "permanentAddOnCard": "Permanent Add-On Card", "baseModelQuota": "Base Model Quota", "advancedModelQuota": "Advanced Model Quota", "MJDrawingQuota": "MJ Drawing Quota", "packageValidity": "Package Validity", "days": "days", "permanent": "Permanent", "points": "Points", "welcomeTipMobile": "Explore freely, welcome to our online store!", "welcomeTipDesktop": "Explore freely, welcome to our online store, thank you for choosing us, let's start a delightful shopping journey together!", "paymentNotEnabled": "Payment has not been enabled by the admin!", "purchaseSuccess": "Purchase successful, enjoy your product!", "paymentNotComplete": "You have not completed the payment yet!", "wechat": "WeChat", "alipay": "Alipay", "wechatPay": "WeChat Pay", "alipayPay": "Alipay Pay", "paymentSuccess": "Congratulations, your payment was successful. Enjoy your purchase!", "paymentTimeout": "Payment timeout, please place your order again!", "productPayment": "Product Payment", "amountDue": "Amount Due:", "packageName": "Package Name:", "packageDescription": "Package Description:", "siteAdminEnabledRedirect": "The site administrator has enabled redirect payment", "clickToPay": "Click to Proceed to Payment", "completePaymentWithin": "Please complete the payment within", "timeToCompletePayment": "!", "open": "Open", "scanToPay": "<PERSON>an to Pay"}, "mindmap": {"title": "Mind Map", "yourNeeds": "Your Needs?", "inputPlaceholder": "Please enter a brief description of the content you want to generate, AI will produce a complete markdown content and its mind map for you!", "generateMindMapButton": "Generate Mind Map", "contentRequirements": "Content Requirements", "tryDemoButton": "Try a Demo", "usageCredits": "Base credits per use: 1", "exportHTML": "Export HTML", "exportPNG": "Export PNG", "exportSVG": "Export SVG"}, "usercenter": {"defaultSignature": "I am an AI robot based on deep learning and natural language processing technologies, aimed at providing users with efficient, accurate, and personalized intelligent services.", "syncComplete": "Data synchronization completed", "personalCenter": "Personal Center", "logOut": "Log Out", "myUsageRecord": "My Usage Record on This Site", "basicModelCredits": "Basic Model Credits:", "advancedModelCredits": "Advanced Model Credits:", "basicModelUsage": "Basic Model Usage:", "advancedModelUsage": "Advanced Model Usage:", "drawingUsageCredits": "Drawing Usage Credits:", "bindWeChat": "Bind WeChat:", "clickToBindWeChat": "Click to Bind WeChat", "weChatBound": "WeChat Bound", "syncVisitorData": "Click to Sync Visitor Data", "points": "Points", "membershipExpiration": "Membership Expiration Date:", "editInfoDescription": "Edit personal information, view more details", "myDetails": "My Details", "myWallet": "My Wallet", "basicInfo": "Basic Information", "userBasicSettings": "User Basic Settings", "avatarPlaceholder": "Please enter your avatar URL", "usernamePlaceholder": "Edit your username", "signaturePlaceholder": "Edit your signature", "passwordManagement": "Password Management", "inviteBenefits": "Invite for Benefits", "clickToLogin": "Log In", "notLoggedIn": "Not Logged In", "avatar": "Avatar", "username": "Username", "email": "Email", "inviteeStatus": "Invitee Status", "inviteTime": "Invite Time", "rewardStatus": "Reward Status", "certified": "Certified", "notActivated": "Not Activated", "rewardReceived": "<PERSON><PERSON> Received", "waitingConfirmation": "Waiting for Confirmation", "linkGeneratedSuccess": "Invitation link generated successfully", "generateLinkFirst": "Please generate your exclusive invitation link first!", "linkCopiedSuccess": "Exclusive invitation link copied successfully!", "copyNotSupported": "Automatic copying is not supported on this device, please copy manually!", "inviteForBenefits": "Invite Users, Earn Benefits!", "myInviteCode": "My Invitation Code", "generateInviteCode": "Generate Exclusive Invite Code", "copyInviteLink": "Copy Exclusive Invite Link", "inviteOneUser": "Inviting a user grants", "basicModelCredits1": "basic model credits+", "advancedModelCredits1": "advanced model credits+", "mjDrawingCredits": "MJ drawing credits", "receiveInvitation": "Invited users receive", "creditsEnd": "credits", "invitationRecord": "Invitation Record", "passwordMinLength": "The minimum password length is 6 characters", "passwordMaxLength": "The maximum password length is 30 characters", "enterPassword": "Please enter a password", "reenterPassword": "Please re-enter your password", "passwordsNotMatch": "The passwords do not match", "passwordUpdateSuccess": "Password updated successfully, please log in again!", "changeYourPassword": "Change Your Password", "oldPassword": "Old Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "reloginAfterPasswordChange": "You will need to log in again after updating your password!", "updateYourPassword": "Update Your Password", "passwordRequirements": "Password Requirements", "newPasswordInstructions": "To create a new password, you must meet all the following requirements:", "minimumCharacters": "At least 6 characters", "maximumCharacters": "No more than 30 characters", "requireNumber": "Must contain at least one number", "orderNumber": "Order Number", "rechargeType": "Recharge Type", "basicModelQuota": "Basic Model Quota", "advancedModelQuota": "Advanced Model Quota", "mjDrawingQuota": "MJ Drawing Quota", "validity": "Validity", "rechargeTime": "Recharge Time", "enterCardSecret": "Please enter the card secret first!", "cardRedeemSuccess": "Card redeemed successfully, enjoy your use!", "userWalletBalance": "User Wallet Balance", "basicModelBalance": "Basic Model Balance", "creditUsageNote": "Each conversation consumes different credits depending on the model!", "advancedModelBalance": "Advanced Model Balance", "modelConsumptionNote": "Each conversation consumes different credits depending on the model!", "mjDrawingBalance": "MJ Drawing Balance", "drawingConsumptionNote": "Different credits are consumed based on drawing actions!", "cardRecharge": "Card Recharge", "enterCardDetails": "Please paste or enter your card details!", "pleaseEnterCardDetails": "Please enter card details", "exchange": "Exchange", "buyCardSecret": "Buy Card Secret", "rechargeRecords": "Recharge Records", "packagePurchase": "Package Purchase", "buyPackage": "Buy Package"}, "siderBar": {"signInReward": "Sign-in Reward", "themeSwitch": "Theme Switch", "personalCenter": "Personal Center", "loginAccount": "Log In Account"}, "notice": {"doNotRemind24h": "Do not remind again for 24 hours"}, "login": {"enterUsername": "Please enter your username", "usernameLength": "Username must be between 2 and 30 characters", "enterPassword": "Please enter your password", "passwordLength": "Password must be between 6 and 30 characters", "enterEmail": "Please enter your email address", "emailValid": "Please enter a valid email address", "enterCaptcha": "Please enter the captcha", "emailPhone": "Email / Phone", "email": "Email", "phone": "Phone", "registrationSuccess": "Account registration successful, start your experience!", "loginSuccess": "Account login successful, start your experience!", "registerTitle": "Register", "enterContact": "Please provide your ", "enterCode": "Please enter the verification code", "sendVerificationCode": "Send Verification Code", "optionalInvitationCode": "Invitation Code [Optional]", "registerAccount": "Register Account", "alreadyHaveAccount": "Already have an account?", "goToLogin": "Go to Login", "password": "Password", "enterYourPassword": "Please enter your password", "rememberAccount": "Remember account", "forgotPassword": "Forgot password?", "loginAccount": "Log In Account", "noAccount": "Don't have an account?", "register": "Register", "orUse": "or use", "scanLogin": "Scan to Log In", "wechatLogin": "<PERSON><PERSON><PERSON>", "wechatScanFailed": "Failed WeChat QR code login? Use", "useWechatScan": "Use WeChat to Scan and Log In"}, "share": {"orderAmount": "Order Amount", "productType": "Product Type", "status": "Status", "commissionRate": "Commission Rate", "commission": "Commission", "orderTime": "Order Time", "purchasePackage": "Purchase Package", "accounted": "Accounted", "generateInviteCodeSuccess": "Invitation code generated successfully", "withdrawalTime": "<PERSON><PERSON>wal Time", "withdrawalAmount": "<PERSON><PERSON><PERSON> Amount", "withdrawalChannel": "Withdrawal Channel", "withdrawalStatus": "Withdrawal Status", "withdrawalRemarks": "Withdrawal Remarks", "auditor": "Auditor", "alipay": "Alipay", "wechat": "WeChat", "paid": "Paid", "rejected": "Rejected", "inReview": "In Review", "avatar": "Avatar", "username": "Username", "email": "Email", "inviteeStatus": "Invitee Status", "registered": "Registered", "pendingActivation": "Pending Activation", "registrationTime": "Registration Time", "lastLogin": "Last Login", "requestInviteCodeFirst": "Please request your invitation code first", "linkCopiedSuccess": "Share link copied successfully", "title": "Referral Program", "description": "Join us and share in success! Welcome to our distribution page, become our partner and create a bright future together!", "defaultSalesOutletName": "Rookie Referral Officer", "myReferrals": "My Referrals", "currencyUnit": "Yuan", "remainingAmount": "Remaining Withdra<PERSON><PERSON> Amount", "withdrawingAmount": "Amount in Withdrawal", "withdrawNow": "Withdraw Now", "minimumWithdrawalPrefix": "Minimum", "minimumWithdrawalSuffix": "Yuan Withdrawable", "purchaseOrderCount": "Purchase Order Count", "promotionLinkVisits": "Promotion Link Visits", "registeredUsers": "Registered Users", "referralEarnings": "Referral Earnings", "referralEarningsDescription": "Commission amount returned after referred users register and buy products", "percentage": "Percentage", "applyForAdvancedAgent": "Apply to Become an Advanced Agent", "contactAdminForAdvancedAgent": "Contact the site owner to apply for an advanced agent to enjoy high commissions", "joinAsPartner": "Join Us as a Partner", "partnerDescription": "Join us as a partner to co-operate the community, win-win cooperation!", "winTogether": "Win Together, Advance Together", "referralLink": "Referral Link:", "apply": "Apply", "referralRecordsTab": "Referral Records", "withdrawalRecordsTab": "Withdrawal Records", "registeredUsersTab": "Registered Users", "inviteFriends": "Invite friends, gift meal cards, and enjoy recharge commissions!", "inviteLink": "Invite Link", "copy": "Copy", "inviteBenefits1": "Both parties enjoy a certain amount of permanent card rewards when inviting friends.", "inviteBenefits2Prefix": "Earn a ", "inviteBenefits2Suffix": "% commission on your friend's recharge amount.", "enterWithdrawalAmount": "Please enter your withdrawal amount!", "selectWithdrawalChannel": "Please select your withdrawal channel!", "enterContactInfo": "Please provide your contact information and remark!", "optionalRemark": "If there are any special circumstances, please remark!", "withdrawalSuccess": "Withdrawal application successful, please wait for approval!", "withdrawalApplicationForm": "Withdrawal Application Form", "contactInformation": "Contact Information", "withdrawalRemark": "Withdrawal Remark", "enterWithdrawalRemark": "Please enter your withdrawal remarks", "applyWithdrawal": "Apply for Withdrawal"}, "goods": {"purchaseSuccess": "Purchase successful, enjoy your item!", "paymentNotSuccessful": "You have not completed the payment yet!", "orderConfirmationTitle": "Order Confirmation", "orderConfirmationContent": "Welcome to purchase, are you sure you want to buy ", "thinkAgain": "Let me think again", "confirmPurchase": "Confirm Purchase", "paymentNotEnabled": "Payment has not been enabled by the administrator!", "selectProducts": "Select Products", "basicModelQuota": "Basic Model Quota", "advancedModelQuota": "Advanced Model Quota", "drawingQuota": "Drawing Quota", "buyPackage": "Buy Package"}, "rechargeTypes": {"1": "Registration Bonus", "2": "Invitation Bonus", "3": "Referring Others Bonus", "4": "Purchase via Card Code", "5": "Admin Bonus", "6": "QR Code Purchase", "7": "MJ Drawing Failure Refund", "8": "Sign-in Reward"}, "orderStatus": {"0": "Not Paid", "1": "Paid", "2": "Payment Failed", "3": "Payment Timeout"}, "messages": {"logoutSuccess": "Successfully logged out!"}}