#!/bin/bash

set -e

cp ./docs/CHANGELOG.md ./admin/src/assets/CHANGELOG.md

cd admin/
pnpm install
pnpm build
cd ..

cd chat/
pnpm install
pnpm build
cd ..

cd service/
pnpm install
pnpm build
cd ..

rm -rf ../AIWeb/dist/* ../AIWeb/public/admin/* ../AIWeb/public/chat/*
mkdir -p ../AIWeb/dist ../AIWeb/public/admin ../AIWeb/public/chat

cp ./docs/CHANGELOG.md ../AIWeb/CHANGELOG.md

cp service/pm2.conf.json ../AIWeb/pm2.conf.json
cp service/package.json ../AIWeb/package.json

cp service/.env.example ../AIWeb/.env.example
cp service/.env.docker ../AIWeb/.env.docker
cp service/Dockerfile ../AIWeb/Dockerfile
cp service/docker-compose.yml ../AIWeb/docker-compose.yml
# cp service/deploy.sh ../AIWeb/deploy.sh
cp service/.dockerignore ../AIWeb/.dockerignore

cp -a service/dist/* ../AIWeb/dist
cp -r admin/dist/* ../AIWeb/public/admin
cp -r chat/dist/* ../AIWeb/public/chat

echo "打包完成"