name: Build Electron App

on:
  # push:
  #   branches: [ main ]
  push:
    tags:
      - "v*"
  # pull_request:
  #   branches: [ main ]
  workflow_dispatch:

jobs:
  build-mac:
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          cd chat
          pnpm install

      - name: Build for Mac
        env:
          VITE_GLOB_API_URL: https://asst.lightai.cloud/api
        run: |
          cd chat
          pnpm run electron:mac-universal

      - name: Upload Mac artifacts
        uses: actions/upload-artifact@v4
        with:
          name: mac-builds
          path: chat/release/*.dmg

  build-windows:
    runs-on: windows-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: Install pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          cd chat
          pnpm install

      - name: Build for Windows x64
        env:
          VITE_GLOB_API_URL: https://asst.lightai.cloud/api
        run: |
          cd chat
          pnpm run electron:win-x64

      - name: Upload Windows artifacts
        uses: actions/upload-artifact@v4
        with:
          name: windows-builds
          path: |
            chat/release/*.exe
            chat/release/*.zip

  create-release:
    needs: [build-mac, build-windows]
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts

      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            artifacts/mac-builds/*
            artifacts/windows-builds/*
          draft: false
          prerelease: false
