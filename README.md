# 🚀 99AI - 聚合 AI 服务平台

<div align="center">

[![GitHub stars](https://img.shields.io/github/stars/vastxie/99AI?style=social)](https://github.com/vastxie/99AI/stargazers)
[![开源协议](https://img.shields.io/badge/license-Apache%202.0-blue.svg)](LICENSE)
[![开发指南](https://img.shields.io/badge/开发指南-orange.svg)](./docs/DEVELOPMENT.md)
[![功能介绍](https://img.shields.io/badge/功能介绍-green.svg)](./docs/FEATURES.md)

> 🎓 新手入门？查看[部署指南](./docs/DEPLOYMENT.md)，快速上手项目部署！
>
> 📢 遇到问题？加入[交流群](#交流群)，获取最新动态，与社区伙伴一起学习讨论，共同进步！

</div>

## 🌟 项目介绍

99AI 是一个**可商业化的 AI Web 平台**，提供一站式的人工智能服务解决方案。支持私有化部署，内置多用户管理，适合企业、团队或个人快速构建 AI 服务。

### 🚀 核心优势

- **开箱即用**：基于 Node.js 完整打包，支持 Docker 一键部署
- **功能丰富**：集成主流 AI 能力，覆盖多场景应用
- **安全可控**：支持私有化部署，数据自主管理
- **开发友好**：提供源码级访问，支持二次开发与功能扩展
- **商业支持**：内置多种支付方式，支持商业化运营，助力变现

### 💡 主要功能

- 🤖 **AI 对话**：支持自定义模型参数，灵活配置
- 🔍 **智能搜索**：联网实时搜索，突破知识边界
- 💡 **深度思考**：支持深度思考模型
- 🔧 **应用拓展**：自定义应用预设，灵活配置场景
- 🎨 **AI 创作**：绘画、音乐、视频一站式生成
- 📝 **文件分析**：支持多模态分析，智能解析文档
- 🗺️ **思维导图**：可视化思维整理工具
- 🛡️ **风控管理**：支持敏感词过滤，内容安全管控
- 💼 **知识库**：预设知识模板，提升交互效率

👉 [更多功能介绍及截图](./docs/FEATURES.md)

## 🔥 版本对比

| 特性     | 稳定版         | 开发版   |
| -------- | -------------- | -------- |
| 商用许可 | ✅ 支持        | ✅ 支持  |
| 源码状态 | 未编译，可修改 | 已编译   |
| 获取方式 | 开源免费       | 需授权   |
| 功能特性 | 基础功能       | 优先更新 |

可在[更新日志](./docs/CHANGELOG.md)中查看比对详细的版本差异，或访问[LightAI 助手](https://asst.lightai.cloud)体验最新开发版

授权方式可参阅[99AI 开发版商业化运作公告](https://mp.weixin.qq.com/s/kGCodiHUQSBn8cdJz7Plhg)

## 📦 项目结构

```tree
99AI/
├── AIWebQuickDeploy/   # 快速部署整合包
├── src/           # 源码目录
│   ├── admin/     # 管理后台
│   ├── chat/      # 用户界面
│   └── service/   # 后端服务
└── docs/          # 文档资源
```

## 💬 交流学习

### 社区参与

- 欢迎提交 [Issue](https://github.com/vastxie/99AI/issues) 反馈问题或 Pull Request 共同维护
- 本项目采用 [Apache 2.0](LICENSE) 开源协议，使用本项目时请保留项目署名和链接
- 如果觉得项目不错，欢迎 Star ⭐️

### 交流群

添加微信备注「99」进群交流，作者不提供私聊技术咨询，请优先阅读群公告

<img src="https://github.com/user-attachments/assets/9fed8343-73ae-43b0-9ce7-dc1a4c30c7a5" width="200">
