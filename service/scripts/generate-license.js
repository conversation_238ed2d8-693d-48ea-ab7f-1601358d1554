#!/usr/bin/env node

/**
 * 授权码生成工具
 * 生成适用于新格式的带有效期的授权码
 *
 * 用法: node scripts/generate-license.js <machine-id> <expiry-date> [private-key-path]
 * 参数:
 *   <machine-id>: 目标机器的机器码
 *   <expiry-date>: 授权到期日期 (格式: YYYY-MM-DD)
 *   [private-key-path]: 私钥文件路径 (默认: ./private.pem)
 */

const fs = require('fs');
const crypto = require('crypto');
const path = require('path');

// 默认私钥路径
const DEFAULT_PRIVATE_KEY_PATH = path.join(__dirname, '../private.pem');

// 命令行参数
const machineId = process.argv[2];
const expiryDate = process.argv[3];
const privateKeyPath = process.argv[4] || DEFAULT_PRIVATE_KEY_PATH;

// 验证参数
if (!machineId || !expiryDate) {
    console.error('用法: node scripts/generate-license.js <machine-id> <expiry-date> [private-key-path]');
    console.error('参数:');
    console.error('  <machine-id>: 目标机器的机器码');
    console.error('  <expiry-date>: 授权到期日期 (格式: YYYY-MM-DD)');
    console.error('  [private-key-path]: 私钥文件路径 (默认: ./private.pem)');
    process.exit(1);
}

// 验证过期日期格式
const expiryTimestamp = new Date(expiryDate);
if (isNaN(expiryTimestamp.getTime())) {
    console.error('错误: 无效的过期日期格式。请使用 YYYY-MM-DD 格式。');
    process.exit(1);
}

// 检查过期日期是否在未来
const currentDate = new Date();
if (expiryTimestamp <= currentDate) {
    console.error('错误: 过期日期必须在未来。');
    process.exit(1);
}

// 验证私钥文件存在
if (!fs.existsSync(privateKeyPath)) {
    console.error(`错误: 私钥文件不存在: ${privateKeyPath}`);
    process.exit(1);
}

try {
    // 读取私钥
    const privateKey = fs.readFileSync(privateKeyPath);

    // 创建授权数据
    const licenseData = {
        machineId: machineId,
        expiryDate: expiryTimestamp.toISOString(),
        createdAt: new Date().toISOString()
    };

    // 将授权数据转换为JSON字符串
    const licensePayload = JSON.stringify(licenseData);

    // 使用私钥对数据进行签名
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(licensePayload);
    sign.end();

    const signature = sign.sign(
        { key: privateKey, padding: crypto.constants.RSA_PKCS1_PADDING },
        'base64'
    );

    // 组装最终的授权码：Base64编码的载荷 + '.' + 签名
    const encodedPayload = Buffer.from(licensePayload).toString('base64');
    const licenseKey = `${encodedPayload}.${signature}`;

    // 输出结果
    console.log('\n=== 授权码生成成功 ===\n');
    console.log('授权信息:');
    console.log(`- 机器码: ${machineId}`);
    console.log(`- 到期日期: ${expiryTimestamp.toLocaleDateString()}`);
    console.log(`- 创建时间: ${new Date().toLocaleString()}`);
    console.log('\n授权码:');
    console.log(licenseKey);
    console.log('\n将此授权码添加到项目的 .env 文件中的 LICENSE_KEY= 后面\n');

} catch (error) {
    console.error('生成授权码时发生错误:', error);
    process.exit(1);
}
