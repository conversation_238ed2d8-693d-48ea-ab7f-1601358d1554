'use strict';

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

const KEYS_DIR = path.resolve(__dirname, '../keys'); // 保存密钥的目录

/**
 * 生成一个指定长度的 AES 密钥 (Buffer)
 * @param {number} lengthInBytes - 密钥长度 (例如 32 for AES-256)
 * @returns {Buffer} - 生成的 AES 密钥 Buffer
 */
function generateAesKey(lengthInBytes = 32) {
  console.log(`正在生成 ${lengthInBytes * 8}-bit AES 密钥...`);
  const key = crypto.randomBytes(lengthInBytes);
  console.log('AES 密钥生成完成。');
  return key;
}

/**
 * 生成 RSA 密钥对
 * @param {number} modulusLength - 模数长度 (例如 2048)
 * @returns {{privateKey: string, publicKey: string}} - PEM 格式的密钥对
 */
function generateRsaKeyPair(modulusLength = 2048) {
  console.log(`正在生成 ${modulusLength}-bit RSA 密钥对...`);
  const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: modulusLength,
    publicKeyEncoding: {
      type: 'spki', // SubjectPublicKeyInfo standard format
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8', // Public-Key Cryptography Standards #8 standard format
      format: 'pem'
      // Optionally add passphrase: 'your-secure-passphrase'
    }
  });
  console.log('RSA 密钥对生成完成。');
  return { privateKey, publicKey };
}

/**
 * 保存密钥到文件
 * @param {string} filename - 相对于 keys 目录的文件名
 * @param {string | Buffer} keyData - 要保存的密钥数据
 */
function saveKeyToFile(filename, keyData) {
  const filePath = path.join(KEYS_DIR, filename);
  try {
    // 确保目录存在
    fs.mkdirSync(KEYS_DIR, { recursive: true });
    fs.writeFileSync(filePath, keyData);
    console.log(`密钥已保存到: ${filePath}`);
  } catch (error) {
    console.error(`保存密钥到 ${filePath} 失败:`, error);
    throw error; // Rethrow the error to halt if needed
  }
}

// 主执行逻辑: 当直接运行此脚本时生成并保存密钥
if (require.main === module) {
  console.log('开始生成和保存密钥...');

  try {
    // 1. 生成并保存 AES 密钥 (保存为 Base64 字符串)
    const aesKey = generateAesKey(32); // AES-256
    saveKeyToFile('aes.key', aesKey.toString('base64'));

    // 2. 生成并保存 RSA 密钥对
    const { privateKey, publicKey } = generateRsaKeyPair(2048);
    saveKeyToFile('root-priv.pem', privateKey);
    saveKeyToFile('root-pub.pem', publicKey);

    console.log('所有密钥已成功生成并保存到 ./keys/ 目录。');
  } catch (error) {
    console.error('密钥生成或保存过程中发生错误:', error);
    process.exit(1); // Exit with error code
  }
}

// 导出函数供其他脚本使用
module.exports = {
  generateAesKey,
  generateRsaKeyPair,
  saveKeyToFile,
  KEYS_DIR
};
