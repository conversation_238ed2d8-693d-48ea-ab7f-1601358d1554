/**
 * 密钥生成工具
 *
 * 用于生成新的RSA密钥对和AES密钥片段
 * 生成的密钥将保存到指定目录
 */

'use strict';

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 创建输出目录
const OUTPUT_DIR = path.resolve(__dirname, '../keys');
if (!fs.existsSync(OUTPUT_DIR)) {
  fs.mkdirSync(OUTPUT_DIR, { recursive: true });
}

/**
 * 生成RSA密钥对
 * @param {number} keySize - 密钥长度 (位)
 * @returns {Object} - 包含私钥和公钥的对象
 */
function generateRsaKeyPair(keySize = 2048) {
  console.log(`正在生成${keySize}位RSA密钥对...`);

  // 生成密钥对
  const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: keySize,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });

  // 写入文件
  const privatePath = path.join(OUTPUT_DIR, 'root-priv.pem');
  const publicPath = path.join(OUTPUT_DIR, 'root-pub.pem');

  fs.writeFileSync(privatePath, privateKey);
  fs.writeFileSync(publicPath, publicKey);

  console.log(`RSA密钥对已生成:`);
  console.log(`- 私钥: ${privatePath}`);
  console.log(`- 公钥: ${publicPath}`);

  return { privateKey, publicKey };
}

/**
 * 生成随机的AES密钥片段
 * @returns {Object} - 包含四个片段的对象
 */
function generateAesKeySegments() {
  console.log('正在生成AES密钥片段...');

  // 生成四个随机片段
  const segment1 = crypto.randomBytes(8).toString('base64');
  const segment2 = crypto.randomBytes(8).toString('base64');
  const segment3 = Array.from(crypto.randomBytes(6))
    .map(byte => byte + 5); // 加5是为了与launcher-template.js中的解码逻辑匹配
  const segment4 = Buffer.from(crypto.randomBytes(7)).toString('hex');

  // 生成固定密钥片段
  const fixedPart1 = crypto.randomBytes(16).toString('base64');
  const fixedPart2 = crypto.randomBytes(16).toString('base64');
  const fixedPart3 = Array.from(crypto.randomBytes(6))
    .map(byte => byte + 5); // 同样加5
  const fixedPart4 = Buffer.from(crypto.randomBytes(16)).toString('hex');

  // 输出到一个配置文件
  const configPath = path.join(OUTPUT_DIR, 'key-segments.json');
  const config = {
    segments: {
      KEY_SEGMENT_1: segment1,
      APP_SECURITY_LEVEL: segment2,
      ENCODED_CHARS: segment3,
      CONFIG_HASH: segment4
    },
    fixedSegments: {
      CIPHER_PART1: fixedPart1,
      CIPHER_PART2: fixedPart2,
      CIPHER_PART3: fixedPart3,
      CIPHER_PART4: fixedPart4
    }
  };

  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  console.log(`AES密钥片段已生成并保存到: ${configPath}`);

  // 创建用于launcher-template.js的替换内容
  const launcherReplacements = {
    // launcher-template.js中的替换项
    '__KEY_SEGMENT_1_BASE64__': segment1,
    '__SECURITY_LEVEL_BASE64__': segment2,
    '__ENCODED_CHARS_ARRAY__': JSON.stringify(segment3),
    '__CONFIG_HASH_HEX__': segment4,

    // 固定密钥片段替换项
    '__CIPHER_PART1_BASE64__': fixedPart1,
    '__CIPHER_PART2_BASE64__': fixedPart2,
    '__CIPHER_PART3_ASCII__': JSON.stringify(fixedPart3),
    '__CIPHER_PART4_HEX__': fixedPart4
  };

  const replacementsPath = path.join(OUTPUT_DIR, 'launcher-replacements.json');
  fs.writeFileSync(replacementsPath, JSON.stringify(launcherReplacements, null, 2));
  console.log(`launcher模板替换配置已保存到: ${replacementsPath}`);

  // 测试组合基础密钥
  testCombineKey([segment1, segment2,
    String.fromCharCode(...segment3.map(c => c - 5)), // 解码
    hexToString(segment4)
  ]);

  testCombineKey([
    Buffer.from(fixedPart1, 'base64').toString(),
    Buffer.from(fixedPart2, 'base64').toString(),
    String.fromCharCode(...fixedPart3.map(c => c - 5)), // 解码
    hexToString(fixedPart4)
  ]);

  return { segment1, segment2, segment3, segment4, fixedPart1, fixedPart2, fixedPart3, fixedPart4 };
}

/**
 * 十六进制字符串转为普通字符串
 * @param {string} hex - 十六进制字符串
 * @returns {string} - 解码后的字符串
 */
function hexToString(hex) {
  let result = '';
  for (let i = 0; i < hex.length; i += 2) {
    result += String.fromCharCode(parseInt(hex.substring(i, i + 2), 16));
  }
  return result;
}

/**
 * 测试组合密钥并生成最终AES密钥
 * @param {string[]} segments - 要组合的片段
 */
function testCombineKey(segments) {
  // 组合基础密钥
  const baseKey = segments.join('');

  // 生成SHA-256哈希作为最终密钥
  const keyBuffer = crypto.createHash('sha256')
    .update(baseKey)
    .digest();

  // 转为base64编码
  const base64Key = keyBuffer.toString('base64');

  console.log(`组合密钥测试:`);
  console.log(`- 基础长度: ${baseKey.length}`);
  console.log(`- 最终密钥长度: ${keyBuffer.length} 字节`);
  console.log(`- Base64格式: ${base64Key} (${base64Key.length} 字符)`);
}

/**
 * 生成和测试所有密钥
 */
function generateAllKeys() {
  console.log('====== 开始生成新密钥 ======\n');

  // 生成RSA密钥对
  const rsaKeys = generateRsaKeyPair();
  console.log('');

  // 生成AES密钥片段
  const aesSegments = generateAesKeySegments();
  console.log('');

  console.log('====== 密钥生成完成 ======');
  console.log('请确保将生成的密钥安全保存，并按照说明更新launcher-template.js中的相应部分。');
  console.log('注意: 私钥文件(root-priv.pem)应该被严格保护，不要将其包含在分发包中。');
}

// 执行主函数
generateAllKeys();
