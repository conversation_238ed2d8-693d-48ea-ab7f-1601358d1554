/**
 * 授权码生成工具 (新版)
 *
 * 使用新生成的RSA密钥对生成正式授权码
 * 注意：这是正式授权工具，不是测试工具
 */

'use strict';

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 获取命令行参数
const args = process.argv.slice(2);
if (args.length < 2) {
  console.log('使用方法: node generate-license-new.js <机器码> <有效期天数>');
  console.log('示例: node generate-license-new.js f954b0fa-6d0e-5eb1-8200-9db80b2d4b17 365');
  process.exit(1);
}

const machineId = args[0];
const days = parseInt(args[1], 10);

if (isNaN(days) || days <= 0) {
  console.error('错误: 有效期天数必须是正整数');
  process.exit(1);
}

// 私钥路径
const privateKeyPath = path.resolve(__dirname, '../keys/root-priv.pem');

// 检查私钥是否存在
if (!fs.existsSync(privateKeyPath)) {
  console.error(`错误: 找不到RSA私钥文件: ${privateKeyPath}`);
  console.error('请先运行 generate-keys.js 生成密钥对。');
  process.exit(1);
}

// 计算过期时间
const expiryTimestamp = new Date();
expiryTimestamp.setDate(expiryTimestamp.getDate() + days);

try {
  // 读取私钥
  const privateKey = fs.readFileSync(privateKeyPath, 'utf8');

  // 创建授权数据
  const licenseData = {
    machineId: machineId,
    expiryDate: expiryTimestamp.toISOString(),
    createdAt: new Date().toISOString()
  };

  // 将授权数据转换为JSON字符串
  const licensePayload = JSON.stringify(licenseData);

  // Base64编码载荷
  const encodedPayload = Buffer.from(licensePayload).toString('base64');

  // 使用私钥对编码后的载荷进行签名
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(encodedPayload);
  sign.end();

  const signature = sign.sign(
    privateKey,
    'base64'
  );

  // 组装最终的授权码：Base64编码的载荷 + '.' + 签名
  const licenseKey = `${encodedPayload}.${signature}`;

  // 输出结果
  console.log('\n=== 授权码生成成功 ===\n');
  console.log('授权信息:');
  console.log(`- 机器码: ${machineId}`);
  console.log(`- 到期日期: ${expiryTimestamp.toLocaleDateString()}`);
  console.log(`- 创建时间: ${new Date().toLocaleString()}`);
  console.log('\n授权码:');
  console.log(licenseKey);
  console.log('\n将此授权码添加到项目的 .env 文件中的 LICENSE_KEY= 后面\n');

  // 保存到文件(可选)
  const licenseFilePath = path.resolve(__dirname, `../keys/license-${machineId.substring(0, 8)}.txt`);
  const licenseFileContent =
    `授权信息:
- 机器码: ${machineId}
- 到期日期: ${expiryTimestamp.toLocaleDateString()}
- 创建时间: ${new Date().toLocaleString()}

授权码:
${licenseKey}

# 将此授权码添加到项目的 .env 文件中的 LICENSE_KEY= 后面
`;

  fs.writeFileSync(licenseFilePath, licenseFileContent);
  console.log(`授权信息已保存到文件: ${licenseFilePath}`);

  // 验证生成的授权码
  validateLicense(licenseKey, machineId);

} catch (error) {
  console.error('生成授权码时发生错误:', error);
  process.exit(1);
}

/**
 * 验证生成的授权码
 * @param {string} licenseKey - 生成的授权码
 * @param {string} machineId - 机器码
 */
function validateLicense(licenseKey, machineId) {
  try {
    // 公钥路径
    const publicKeyPath = path.resolve(__dirname, '../keys/root-pub.pem');
    const publicKey = fs.readFileSync(publicKeyPath, 'utf8');

    // 解析授权码
    const parts = licenseKey.split('.');
    if (parts.length !== 2) {
      throw new Error('授权码格式无效');
    }

    const encodedPayload = parts[0];
    const signature = parts[1];

    // 解析授权数据
    const licensePayload = Buffer.from(encodedPayload, 'base64').toString();
    const licenseData = JSON.parse(licensePayload);

    // 验证机器码
    if (licenseData.machineId !== machineId) {
      throw new Error('授权码机器码不匹配');
    }

    // 验证签名 - 使用和签名时相同的方法
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(encodedPayload); // 验证编码后的载荷
    verify.end();

    const isSignatureValid = verify.verify(
      publicKey,
      signature,
      'base64'
    );

    if (!isSignatureValid) {
      throw new Error('授权码签名验证失败');
    }

    console.log('\n✓ 授权码验证成功 - 签名有效');
  } catch (error) {
    console.error('\n✗ 授权码验证失败:', error.message);
    process.exit(1);
  }
}
