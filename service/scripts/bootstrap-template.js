'use strict';

// 注意：这个脚本最终会被复制到 dist 目录下作为 _bootstrap.js 运行
// 因此，所有的 require 路径都应该是相对于 dist 目录的

const fs = require('fs');
const path = require('path');
const crypto = require('crypto'); // 确保 crypto 被导入

// --- 哈希校验相关 ---
// 这个占位符会被 build.js 脚本替换为实际的 main.js.enc 的 SHA256 哈希值
const EXPECTED_MAIN_ENC_HASH = '__EXPECTED_MAIN_ENC_HASH__';
const MAIN_ENC_PATH = path.resolve(__dirname, 'main.js.enc'); // 预先定义路径

/**
 * 验证 main.js.enc 文件的哈希值
 * @returns {boolean} - 如果哈希值匹配则返回 true，否则返回 false
 */
function verifyMainEncHash() {
  // console.log('[_bootstrap.js] 正在验证主加密文件 (main.js.enc) 的哈希值...'); // 精简掉
  try {
    if (EXPECTED_MAIN_ENC_HASH === '__EXPECTED_MAIN_ENC_HASH__') {
      // 如果哈希未注入，这通常是构建问题，保留警告
      console.warn('警告: 预期哈希值未被注入到 _bootstrap.js 中。跳过哈希校验。请检查构建脚本。');
      return true;
    }

    if (!fs.existsSync(MAIN_ENC_PATH)) {
      console.error(`错误: 主加密文件未找到: ${MAIN_ENC_PATH}`);
      return false;
    }

    // 计算当前文件的哈希值
    const currentContent = fs.readFileSync(MAIN_ENC_PATH);
    const currentHash = crypto.createHash('sha256').update(currentContent).digest('hex');
    // console.log(`  计算得到当前 main.js.enc 的哈希: ${currentHash}`); // 精简掉
    // console.log(`  预期的 main.js.enc 哈希: ${EXPECTED_MAIN_ENC_HASH}`); // 精简掉

    if (currentHash === EXPECTED_MAIN_ENC_HASH) {
      // console.log('[_bootstrap.js] 主加密文件哈希校验成功。'); // 精简掉
      return true;
    } else {
      console.error('\n错误：主加密文件完整性验证失败！');
      console.error(`文件 ${path.basename(MAIN_ENC_PATH)} 可能已被修改、损坏或替换。`);
      console.error('请确保使用的是原始未修改的文件。');
      return false;
    }
  } catch (error) {
    console.error('[_bootstrap.js] 验证主加密文件哈希时发生严重错误:', error);
    return false;
  }
}

/**
 * 验证 _bootstrap.js 自身内容的完整性
 */
function verifyIntegrity() {
  console.log('[_bootstrap.js] 正在验证文件完整性...');
  const bootstrapPath = __filename; // __filename 在 Node.js 中指向当前文件路径
  const signaturePath = path.resolve(__dirname, '_bootstrap.sig');
  const publicKeyPath = path.resolve(__dirname, 'root-pub.pem');

  try {
    // 1. 检查文件是否存在
    if (!fs.existsSync(signaturePath)) {
      throw new Error(`签名文件未找到: ${signaturePath}`);
    }
    if (!fs.existsSync(publicKeyPath)) {
      throw new Error(`公钥文件未找到: ${publicKeyPath}`);
    }

    // 2. 读取公钥、签名和自身内容
    const publicKey = fs.readFileSync(publicKeyPath); // 读取为 Buffer
    const fileSignature = fs.readFileSync(signaturePath, 'utf8'); // 修改：读取为utf8而不是base64
    const bootstrapContent = fs.readFileSync(bootstrapPath); // 保持为 Buffer
    console.log('  读取公钥、签名和自身内容成功。');

    const hash = crypto.createHash('sha256').update(bootstrapContent).digest('hex');
    console.log(`  [VERIFY] _bootstrap.js content hash: ${hash}`);

    // 3. 创建验证对象并更新内容
    console.log('  正在使用公钥验证签名...');
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(bootstrapContent);
    verify.end();

    // 4. 验证签名
    const isValid = verify.verify(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PADDING
      },
      fileSignature,
      'base64'  // 指定签名格式为base64
    );

    if (isValid) {
      console.log('[_bootstrap.js] 文件完整性验证成功！');
      return true;
    } else {
      console.error('[_bootstrap.js] 错误：文件完整性验证失败！脚本可能已被篡改。');
      return false;
    }

  } catch (error) {
    console.error('[_bootstrap.js] 验证完整性时发生错误:', error);
    return false;
  }
}

// 获取 AES 密钥的函数 (关键点，后续会被 launcher.js 覆盖或提供)
// 临时实现：直接从 dist/lib/.key (假设存在) 或硬编码读取 (不安全)
// 或者更实际的是，假设 launcher.js 会将密钥设置到环境变量或全局变量中
function getAesKey() {
  // console.log('[_bootstrap.js] 尝试获取 AES 密钥...'); // 精简掉
  const keyFromEnv = process.env.APP_AES_KEY;
  if (keyFromEnv) {
    // console.log('[_bootstrap.js] 从环境变量 APP_AES_KEY 获取到密钥。'); // 精简掉
    return keyFromEnv; // 假设是 Base64 编码
  }

  // 备选：尝试从文件读取 (需要 launcher.js 创建这个文件)
  const keyFilePath = path.resolve(__dirname, 'lib', '.key');
  if (fs.existsSync(keyFilePath)) {
    // console.log(`[_bootstrap.js] 从文件 ${keyFilePath} 读取密钥...`); // 精简掉
    try {
      const keyFromFile = fs.readFileSync(keyFilePath, 'utf8').trim();
      if (keyFromFile) {
        return keyFromFile; // 假设是 Base64
      }
    } catch (err) {
      console.error(`[_bootstrap.js] 读取密钥文件 ${keyFilePath} 失败:`, err); // 保留错误
    }
  }

  // 如果都失败，抛出错误
  console.error('[_bootstrap.js] 错误：无法获取 AES 密钥！请确保启动器正确设置了密钥。'); // 保留错误
  throw new Error('未能获取 AES 密钥');
}

/**
 * 检测调试环境
 * @returns {boolean} - 如果检测到调试环境则返回true
 */
function detectDebugger() {
  try {
    // 方法1: 检测Node调试标志
    const isDebugMode = process.execArgv.some(arg =>
      /--inspect|--debug|--debug-brk/.test(arg)
    );

    if (isDebugMode) {
      console.error('检测到Node.js调试模式，禁止启动。');
      return true;
    }

    // 方法2: 检测调试器连接
    const startTime = Date.now();
    const debuggerStatement = new Function('debugger; return Date.now();');
    const endTime = debuggerStatement();

    // 如果debugger语句执行时间过长，说明有调试器连接
    if ((endTime - startTime) > 100) {
      console.error('检测到调试器连接，禁止启动。');
      return true;
    }

    // 暂时注释掉可能干扰正常启动的检测
    // if (typeof process._debugProcess === 'function' ||
    //     typeof v8debug !== 'undefined' ||
    //     /--inspect|--debug/.test(process.env.NODE_OPTIONS || '')) {
    //   console.error('检测到调试工具或环境变量，禁止启动。');
    //   return true;
    // }

    return false;
  } catch (error) {
    // 静默处理错误，避免防护机制本身出错导致程序无法启动
    return false;
  }
}

/**
 * 检查系统完整性
 * @returns {boolean} - 如果系统完整性检查通过返回true
 */
function checkSystemIntegrity() {
  try {
    // 检查关键文件是否存在
    const requiredFiles = [
      path.resolve(__dirname, '_bootstrap.js'),
      path.resolve(__dirname, '_bootstrap.sig'),
      path.resolve(__dirname, 'root-pub.pem'),
      path.resolve(__dirname, 'main.js.enc'),
      path.resolve(__dirname, 'lib/aes-loader.js'),
      path.resolve(__dirname, 'launcher.js')
    ];

    for (const filePath of requiredFiles) {
      if (!fs.existsSync(filePath)) {
        console.error(`系统完整性检查失败: 缺少关键文件 ${path.basename(filePath)}`);
        return false;
      }
    }

    // 检查文件大小是否符合预期
    const mainEncStat = fs.statSync(path.resolve(__dirname, 'main.js.enc'));
    if (mainEncStat.size < 1000) { // 假设加密后的文件至少有1KB
      console.error('系统完整性检查失败: main.js.enc 文件大小异常');
      return false;
    }

    return true;
  } catch (error) {
    console.error('系统完整性检查过程中发生错误:', error);
    return false;
  }
}

/**
 * 主引导逻辑
 */
function bootstrap() {
  // 步骤1: 检测调试环境
  if (detectDebugger()) {
    console.error('安全警告: 检测到调试环境，程序终止启动。');
    process.exit(1);
  }

  // 步骤2: 检查系统完整性
  if (!checkSystemIntegrity()) {
    console.error('安全警告: 系统完整性检查失败，程序终止启动。');
    process.exit(1);
  }

  // 步骤3: 验证自身完整性 (签名校验)
  if (!verifyIntegrity()) {
    console.error('安全警告: 引导程序完整性验证失败，程序终止启动。');
    process.exit(1);
  }

  // 步骤4: 验证 main.js.enc 哈希
  if (!verifyMainEncHash()) {
    console.error('安全警告: 主加密文件校验失败，程序终止启动。');
    process.exit(1);
  }

  let aesLoader;
  let decryptedCode = null;

  try {
    // 1. 加载 AES Loader (相对于 dist/_bootstrap.js 的路径)
    // console.log('[_bootstrap.js] 加载 aes-loader...'); // 精简掉
    const aesLoaderPath = path.resolve(__dirname, 'lib', 'aes-loader.js');
    if (!fs.existsSync(aesLoaderPath)) {
      throw new Error(`AES Loader 未找到: ${aesLoaderPath}`); // 保留错误
    }
    aesLoader = require(aesLoaderPath);
    // console.log('[_bootstrap.js] aes-loader 加载成功。'); // 精简掉

    // 2. 获取 AES 密钥
    const aesKey = getAesKey(); // 调用上面的函数

    // 3. 确定加密文件路径
    // console.log(`[_bootstrap.js] 查找加密文件: ${MAIN_ENC_PATH}`); // 精简掉
    if (!fs.existsSync(MAIN_ENC_PATH)) {
      // 理论上 verifyMainEncHash 已经检查过，但保留以防万一
      throw new Error(`加密的主文件未找到: ${MAIN_ENC_PATH}`);
    }

    // 4. 读取加密文件
    // console.log('[_bootstrap.js] 读取加密文件...'); // 精简掉
    const encryptedData = fs.readFileSync(MAIN_ENC_PATH);
    // console.log(`[_bootstrap.js] 读取 ${encryptedData.length} 字节.`); // 精简掉

    // 5. 解密数据
    // console.log('[_bootstrap.js] 解密主文件数据...'); // 精简掉
    const decryptedBuffer = aesLoader.decryptData(encryptedData, aesKey);

    if (!decryptedBuffer) {
      // decryptData 内部应该已经打印了错误
      throw new Error('解密主文件失败。');
    }
    // console.log('[_bootstrap.js] 主文件解密成功。'); // 精简掉 (如果 aes-loader 有成功日志，否则可考虑保留)
    decryptedCode = decryptedBuffer.toString('utf8');

    // 6. 执行解密后的代码
    // console.log('[_bootstrap.js] 执行解密后的代码...'); // 精简掉
    const simulatedModule = {
      exports: {}
    };
    const mainModule = new Function('require', 'module', 'exports', '__filename', '__dirname', decryptedCode);
    const fakeMainPath = path.resolve(__dirname, 'main.js');
    mainModule(require, simulatedModule, simulatedModule.exports, fakeMainPath, __dirname);
    // console.log('[_bootstrap.js] 解密代码执行已启动。'); // 精简掉

  } catch (error) {
    // console.error('[_bootstrap.js] 启动时发生错误:', error);
    console.error('\n[_bootstrap.js] 引导过程中发生错误 (加密/解密/执行阶段):', error);
    console.error(error.stack || error);
    process.exit(1);
  }
}

// bootstrap(); // <-- 移除错误的直接调用

// 导出 bootstrap 函数供 launcher 使用
module.exports = bootstrap; // <-- 重新添加导出

// console.log('[_bootstrap.js] 脚本已加载 (等待 launcher 调用 bootstrap 函数)。'); // 精简掉
