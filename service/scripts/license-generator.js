#!/usr/bin/env node
'use strict';

/**
 * 授权码生成工具
 * 支持设置授权码过期时间
 * 使用方法: node license-generator.js <机器码> [过期日期YYYY-MM-DD]
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// 私钥路径
const PRIVATE_KEY_PATH = path.resolve(__dirname, '../keys/root-priv.pem');

/**
 * 生成带过期时间的授权码
 * @param {string} machineId - 机器码
 * @param {string} expiresAt - 过期时间，格式为YYYY-MM-DD (可选)
 * @returns {string} - 格式为 data.signature 的授权码
 */
function generateLicense(machineId, expiresAt) {
  try {
    // 检查机器码格式
    if (!machineId || machineId.length < 10) {
      throw new Error('无效的机器码格式');
    }

    // 读取私钥
    if (!fs.existsSync(PRIVATE_KEY_PATH)) {
      throw new Error(`私钥文件未找到: ${PRIVATE_KEY_PATH}`);
    }
    const privateKey = fs.readFileSync(PRIVATE_KEY_PATH);

    // 构建授权数据
    const licenseData = {
      machineId,
      issuedAt: '2024-05-01T00:00:00.000Z', // 固定发布日期，避免系统时间问题
    };

    // 如果提供了过期时间，添加到数据中
    if (expiresAt) {
      const expiryDate = new Date(expiresAt);
      if (isNaN(expiryDate.getTime())) {
        throw new Error('过期时间格式无效，请使用YYYY-MM-DD格式');
      }
      // 同时添加两个字段以兼容不同验证逻辑
      licenseData.expiryDate = expiryDate.toISOString();
      licenseData.expiresAt = expiryDate.toISOString();
    }

    // 序列化并编码数据
    const dataString = JSON.stringify(licenseData);
    const dataBase64 = Buffer.from(dataString).toString('base64');

    // 使用私钥对数据进行签名
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(dataBase64);
    sign.end();

    // 生成签名 (Base64 格式)
    const signature = sign.sign(privateKey, 'base64');

    // 组合授权码：数据.签名
    return `${dataBase64}.${signature}`;
  } catch (error) {
    console.error('生成授权码失败:', error);
    return null;
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const machineId = args[0];
  const expiresAt = args[1]; // 可选的过期时间，格式为YYYY-MM-DD

  if (!machineId) {
    console.log('\n=== 99AI 授权码生成工具 ===');
    console.log('\n使用方法: ');
    console.log('node license-generator.js <机器码> [过期日期YYYY-MM-DD]');
    console.log('\n示例:');
    console.log('node license-generator.js f954b0fa-6d0e-5eb1-8200-9db80b2d4b17');
    console.log('node license-generator.js f954b0fa-6d0e-5eb1-8200-9db80b2d4b17 2025-12-31');
    process.exit(1);
  }

  const license = generateLicense(machineId, expiresAt);

  if (license) {
    console.log('\n=== 授权码生成成功 ===');
    console.log(`机器码: ${machineId}`);
    if (expiresAt) {
      console.log(`过期时间: ${expiresAt}`);
    } else {
      console.log('过期时间: 永久有效');
    }

    console.log('\nLICENSE_KEY:');
    console.log(license);
    console.log('\n将上面的LICENSE_KEY添加到.env文件中');
  }
}

module.exports = {
  generateLicense
};
