'use strict';

const { machineId, machineIdSync } = require('node-machine-id');

/**
 * 获取本机的机器 ID (异步)
 * @returns {Promise<string>} - 返回机器 ID 字符串
 * @throws {Error} - 如果无法获取 ID 则抛出错误
 */
async function getMachineId() {
    try {
        // 使用异步版本，通常是首选，因为它可能涉及 I/O 操作
        const id = await machineId({ original: true }); // original: true 尝试获取原始硬件 ID
        if (!id) {
            throw new Error('未能获取到有效的机器 ID。');
        }
        return id;
    } catch (error) {
        console.error('[machine-code.js] 获取机器 ID 时出错:', error);
        throw error; // 将错误向上抛出
    }
}

/**
 * 获取本机的机器 ID (同步，如果可能)
 * 注意：同步版本可能不适用于所有平台或情况，异步是更可靠的选择。
 * @returns {string} - 返回机器 ID 字符串
 * @throws {Error} - 如果无法获取 ID 则抛出错误
 */
function getMachineIdSync() {
    try {
        const id = machineIdSync({ original: true });
        if (!id) {
            throw new Error('未能获取到有效的机器 ID (同步)。');
        }
        return id;
    } catch (error) {
        console.error('[machine-code.js] 获取机器 ID (同步) 时出错:', error);
        throw error;
    }
}

// 测试逻辑: 当直接运行此脚本时执行测试
if (require.main === module) {
    console.log('[machine-code.js] 开始测试...');
    (async () => {
        try {
            console.log('--- 测试异步获取 --- ');
            const asyncId = await getMachineId();
            console.log(`异步获取成功: ${asyncId}`);

            console.log('\n--- 测试同步获取 --- ');
            const syncId = getMachineIdSync();
            console.log(`同步获取成功: ${syncId}`);

            if (asyncId === syncId) {
                console.log('\n测试通过：异步和同步获取的 ID 一致。');
            } else {
                console.warn('\n测试注意：异步和同步获取的 ID 不一致。通常应优先使用异步结果。');
            }

        } catch (error) {
            console.error('\n[machine-code.js] 测试过程中发生错误:', error);
            process.exit(1);
        }
    })();
}

module.exports = {
    getMachineId,
    getMachineIdSync // 也导出同步版本，以备不时之需，但优先使用异步
};
