/**
 * 启动器模板更新工具
 *
 * 用于将生成的密钥片段更新到launcher-template.js中
 */

'use strict';

const fs = require('fs');
const path = require('path');

// 文件路径
const LAUNCHER_TEMPLATE_PATH = path.resolve(__dirname, './launcher-template.js');
const REPLACEMENTS_PATH = path.resolve(__dirname, '../keys/launcher-replacements.json');
const OUTPUT_PATH = path.resolve(__dirname, './launcher-template-updated.js');

/**
 * 更新启动器模板
 */
function updateLauncherTemplate() {
  // 检查文件是否存在
  if (!fs.existsSync(LAUNCHER_TEMPLATE_PATH)) {
    console.error(`错误: 找不到启动器模板文件: ${LAUNCHER_TEMPLATE_PATH}`);
    process.exit(1);
  }

  if (!fs.existsSync(REPLACEMENTS_PATH)) {
    console.error(`错误: 找不到替换配置文件: ${REPLACEMENTS_PATH}`);
    console.error('请先运行 generate-keys.js 生成密钥。');
    process.exit(1);
  }

  // 读取文件内容
  let templateContent = fs.readFileSync(LAUNCHER_TEMPLATE_PATH, 'utf8');
  const replacements = JSON.parse(fs.readFileSync(REPLACEMENTS_PATH, 'utf8'));

  console.log('正在更新launcher-template.js...');

  // 替换密钥片段
  templateContent = templateContent
    .replace(/Buffer\.from\('__KEY_SEGMENT_1_BASE64__'.*?\)\.toString\(\)/,
      `Buffer.from('${replacements['__KEY_SEGMENT_1_BASE64__']}', 'base64').toString()`)

    .replace(/Buffer\.from\('b3F5dHI='.*?\)\.toString\(\)/,
      `Buffer.from('${replacements['__SECURITY_LEVEL_BASE64__']}', 'base64').toString()`)

    .replace(/\[107, 106, 115, 113, 119, 101\]/,
      replacements['__ENCODED_CHARS_ARRAY__'])

    .replace(/CONFIG_HASH = '7a396b74643271'/,
      `CONFIG_HASH = '${replacements['__CONFIG_HASH_HEX__']}'`)

    .replace(/Buffer\.from\('__CIPHER_PART1_BASE64__'.*?\)\.toString\(\)/,
      `Buffer.from('${replacements['__CIPHER_PART1_BASE64__']}', 'base64').toString()`)

    .replace(/Buffer\.from\('__CIPHER_PART2_BASE64__'.*?\)\.toString\(\)/,
      `Buffer.from('${replacements['__CIPHER_PART2_BASE64__']}', 'base64').toString()`)

    .replace(/__CIPHER_PART3_ASCII__/,
      replacements['__CIPHER_PART3_ASCII__'])

    .replace(/__CIPHER_PART4_HEX__/,
      `'${replacements['__CIPHER_PART4_HEX__']}'`);

  // 写入更新后的文件
  fs.writeFileSync(OUTPUT_PATH, templateContent);

  console.log(`启动器模板已更新，新文件保存为: ${OUTPUT_PATH}`);
  console.log('请检查更新后的文件，确认所有密钥片段已正确替换。');
  console.log('确认无误后，您可以将更新后的文件重命名为 launcher-template.js');
}

// 执行更新
updateLauncherTemplate();
