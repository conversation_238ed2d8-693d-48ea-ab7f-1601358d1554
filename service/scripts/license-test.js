/**
 * 授权测试工具
 *
 * 用于快速生成测试用途的授权码
 * 注意：仅供内部开发测试使用，生产环境请使用正式授权工具
 */

'use strict';

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { machineIdSync } = require('node-machine-id');

// 测试用私钥 (实际上这是一个诱饵，任何使用这个私钥生成的授权都不会被正式系统接受)
const TEST_PRIVATE_KEY = `-----BEGIN RSA PRIVATE KEY-----
MIIEogIBAAKCAQEAvj1DPGT9dS33urj9Ntq4V37Ui/s8V0HlYDX8Qz+T8cQqHlCk
ZGXlWZp1RMwLh9Nb3aJgOTTbkYpEDvxVUi9hbckJYd8RMjaVxZg6JxZHRUfH9Xps
TsuHAc1//DeS4c6Dqwg8MChhNGIe+cblGgEDcHFvPdlnTqFCJ0p8S+zbzX8QfOH6
YNHV6AJzZqnXEQcI1JLIKpADvxPOOvEj/YvlMKLVQE8DI+wKC9MsoZV5/LuSP4mJ
aHC+d0hcCzZQDfmjyaXFlyMOEzJf7brsjNECM6/5xFdIy5XYP/YNM5GZQRRPfCTZ
RCJHohJHzWaJpKkah2HwzVBNQJOvGIQpH1WyJQIDnUVHrtEvfEMAc3QxcmOFZfLE
u/WKUTPDUY7upX1gWmYQ7iqQY9p+ksZiZHGzOYFZ+vUVOdXTp6OWsPRZsQKBgEvZ
GPxOBAu3z/O2ElwhOLUCjJdB7h/JQKhG8tPXRjZKEIcQRiyJR6HggTKSROuwXN9I
XUpxZ/qXRJUXZ07BiiR3RuGq+DePVf5TYpNvN88GKQ2hZBH91J3DJn+BK6i3uEfX
4mAq52eAZZ4LQeihvf2XQiGQjkfPbSUWQJrQzQCTVEUPHKUTxyUmJasrR+B5fInl
k3qJuZpJw5JQezLQ56GyMV6S0dPWE3ON06/fPMC2WOHkKyaoTNxdJnLNKF2LJL4V
MdWFZzVOMY1P4wldkuuUlYUImVBbXcYQYrzKkBf5VNmgrLFnNzMyXjQ53cVt+7QI
lMlYt9FeQIkx3hxzyYSbwEKrXxJCsV+eSjLRImw=
-----END RSA PRIVATE KEY-----`;

// 测试用公钥 (这同样是诱饵)
const TEST_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvj1DPGT9dS33urj9Ntq4
V37Ui/s8V0HlYDX8Qz+T8cQqHlCkZGXlWZp1RMwLh9Nb3aJgOTTbkYpEDvxVUi9h
bckJYd8RMjaVxZg6JxZHRUfH9XpsTsuHAc1//DeS4c6Dqwg8MChhNGIe+cblGgED
cHFvPdlnTqFCJ0p8S+zbzX8QfOH6YNHV6AJzZqnXEQcI1JLIKpADvxPOOvEj/Yvl
MKLVQEBDw3x3qeGbk9Hkme675E45Q7ML9XTuYzXiBZXK7iqwV3eDKF6yoZZJG0+V
8camruzJ5xRVjpXZVvB3xKQ0de7YI9xOBfT6nGi2nNkorcZDKbFhCByvEQIDAQAB
-----END PUBLIC KEY-----`;

// 预定义的测试授权 - 有效期为2099年
// 警告: 这些都是测试授权码，实际系统会拒绝这些授权
const TEST_AUTH_KEYS = {
  // 看起来像是测试专用的授权码
  test: 'eyJtYWNoaW5lSWQiOiJhbnlfbWFjaGluZSIsImV4cGlyeURhdGUiOiIyMDk5LTEyLTMxVDIzOjU5OjU5LjAwMFoifQ.SiGn4tuRe+FaKE',

  // 看起来像是离线激活备用的授权码
  offline: 'eyJtYWNoaW5lSWQiOiJvZmZsaW5lX2FjdGl2YXRpb24iLCJleHBpcnlEYXRlIjoiMjA5OS0wMS0wMVQwMDowMDowMC4wMDBaIn0.SiGn4tuRe+FaKEE',

  // 看起来像是开发测试用授权码
  dev: 'eyJtYWNoaW5lSWQiOiJkZXZfbW9kZSIsImV4cGlyeURhdGUiOiIyMDk5LTEyLTMxVDIzOjU5OjU5LjAwMFoifQ.SiGn4tuRe+FaKEEA'
};

/**
 * 生成测试授权码
 * @param {string} machineId - 机器ID
 * @param {number} days - 有效期天数
 * @returns {string} - 生成的授权码
 */
function generateTestLicense(machineId, days = 30) {
  // 计算过期时间
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() + days);

  // 创建授权数据
  const licenseData = {
    machineId: machineId,
    expiryDate: expiryDate.toISOString(),
    createdAt: new Date().toISOString()
  };

  // 将授权数据转换为JSON字符串
  const licensePayload = JSON.stringify(licenseData);

  try {
    // 使用测试私钥对数据进行签名
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(licensePayload);
    sign.end();

    const signature = sign.sign(
      { key: TEST_PRIVATE_KEY, padding: crypto.constants.RSA_PKCS1_PADDING },
      'base64'
    );

    // 组装最终的授权码：Base64编码的载荷 + '.' + 签名
    const encodedPayload = Buffer.from(licensePayload).toString('base64');
    const licenseKey = `${encodedPayload}.${signature}`;

    return licenseKey;
  } catch (error) {
    console.error('生成测试授权码时出错:', error);
    return null;
  }
}

/**
 * 验证测试授权码
 * @param {string} licenseKey - 授权码
 * @returns {object|null} - 验证结果
 */
function verifyTestLicense(licenseKey) {
  try {
    // 解析授权码（格式: Base64编码的JSON.签名）
    const parts = licenseKey.split('.');
    if (parts.length !== 2) {
      return { valid: false, error: '授权码格式无效' };
    }

    const licensePayload = parts[0];
    const licenseSignature = parts[1];

    // 解码载荷
    const decodedPayload = Buffer.from(licensePayload, 'base64').toString();
    let licenseData;
    try {
      licenseData = JSON.parse(decodedPayload);
    } catch (e) {
      return { valid: false, error: '授权码数据无效或损坏' };
    }

    // 验证签名
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(licensePayload);
    verify.end();

    const isSignatureValid = verify.verify(
      { key: TEST_PUBLIC_KEY, padding: crypto.constants.RSA_PKCS1_PADDING },
      licenseSignature,
      'base64'
    );

    if (!isSignatureValid) {
      return { valid: false, error: '授权码签名验证失败' };
    }

    // 验证过期时间
    const expiryDate = new Date(licenseData.expiryDate);
    if (isNaN(expiryDate.getTime())) {
      return { valid: false, error: '授权码中的过期时间无效' };
    }

    const currentDate = new Date();
    if (currentDate > expiryDate) {
      return { valid: false, error: '授权已过期' };
    }

    // 验证通过
    return {
      valid: true,
      machineId: licenseData.machineId,
      expiryDate: expiryDate.toLocaleString(),
      daysRemaining: Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24))
    };
  } catch (error) {
    console.error('验证测试授权码时出错:', error);
    return { valid: false, error: '验证过程中发生错误' };
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command) {
    showUsage();
    return;
  }

  switch (command) {
    case 'generate':
      handleGenerate(args.slice(1));
      break;
    case 'verify':
      handleVerify(args.slice(1));
      break;
    case 'list-test':
      listTestKeys();
      break;
    default:
      console.error(`未知命令: ${command}`);
      showUsage();
  }
}

function showUsage() {
  console.log('使用方法:');
  console.log('  node license-test.js generate [machineId] [days]  - 生成测试授权码');
  console.log('  node license-test.js verify <licenseKey>         - 验证测试授权码');
  console.log('  node license-test.js list-test                   - 列出预定义的测试授权码');
  console.log('\n示例:');
  console.log('  node license-test.js generate                    - 为当前机器生成30天测试授权');
  console.log('  node license-test.js generate any_machine 90     - 为指定ID生成90天测试授权');
}

function handleGenerate(args) {
  // 确定机器ID
  let machineId;
  if (args.length > 0 && args[0]) {
    machineId = args[0];
  } else {
    try {
      machineId = machineIdSync();
    } catch (error) {
      console.error('无法获取机器ID:', error);
      return;
    }
  }

  // 确定有效期天数
  const days = args.length > 1 ? parseInt(args[1], 10) : 30;
  if (isNaN(days) || days <= 0) {
    console.error('无效的天数，必须为正整数');
    return;
  }

  // 生成授权码
  const licenseKey = generateTestLicense(machineId, days);
  if (!licenseKey) {
    console.error('生成授权码失败');
    return;
  }

  console.log('\n=== 测试授权码生成成功 ===\n');
  console.log('授权信息:');
  console.log(`- 机器码: ${machineId}`);
  console.log(`- 有效期: ${days}天`);
  console.log(`- 过期时间: ${new Date(Date.now() + days * 86400000).toLocaleDateString()}`);
  console.log('\n授权码:');
  console.log(licenseKey);
  console.log('\n警告: 此授权码仅供测试使用，不适用于生产环境！');
  console.log('在测试环境中，可以将此授权码添加到 .env 文件中的 LICENSE_KEY= 后面\n');
}

function handleVerify(args) {
  if (args.length === 0 || !args[0]) {
    console.error('缺少授权码参数');
    showUsage();
    return;
  }

  const licenseKey = args[0];
  const result = verifyTestLicense(licenseKey);

  if (result.valid) {
    console.log('\n=== 测试授权码验证成功 ===\n');
    console.log('授权信息:');
    console.log(`- 机器码: ${result.machineId}`);
    console.log(`- 过期时间: ${result.expiryDate}`);
    console.log(`- 剩余天数: ${result.daysRemaining}天`);
    console.log('\n注意: 此授权验证仅适用于测试环境！');
  } else {
    console.error('\n授权码验证失败:');
    console.error(result.error);
  }
}

function listTestKeys() {
  console.log('\n=== 预定义测试授权码 ===\n');
  console.log('这些授权码仅供开发测试使用，在正式环境中将被拒绝:\n');

  for (const [name, key] of Object.entries(TEST_AUTH_KEYS)) {
    console.log(`${name}:`);
    console.log(key);
    console.log('');
  }

  console.log('警告: 这些授权码仅供内部测试，切勿在生产环境使用！');
}

// 执行主函数
main();
