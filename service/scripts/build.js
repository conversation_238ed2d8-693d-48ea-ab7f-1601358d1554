'use strict';

const fs = require('fs-extra'); // 使用 fs-extra 方便目录操作和复制
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto'); // 确保 crypto 被导入
const JavaScriptObfuscator = require('javascript-obfuscator'); // <-- 导入混淆库

// 导入加密模块
const aesLoader = require('./lib/aes-loader.js');

const ROOT_DIR = path.resolve(__dirname, '..');
const DIST_DIR = path.join(ROOT_DIR, 'dist');
const KEYS_DIR = path.join(ROOT_DIR, 'keys');
const SCRIPTS_DIR = path.join(ROOT_DIR, 'scripts');
const DIST_LIB_DIR = path.join(DIST_DIR, 'lib'); // 定义 dist/lib 目录

// 需要复制到 dist 的文件/目录列表
const FILES_TO_COPY = [
  { source: path.join(ROOT_DIR, 'package.json'), dest: path.join(DIST_DIR, 'package.json') },
  { source: path.join(KEYS_DIR, 'root-pub.pem'), dest: path.join(DIST_DIR, 'root-pub.pem') } // 公钥
];

// 安全模块列表 - 添加新的安全模块
const SECURITY_MODULES = [
  { source: path.join(SCRIPTS_DIR, 'lib', 'integrity-check.js'), dest: path.join(DIST_LIB_DIR, 'integrity-check.js') },
  { source: path.join(SCRIPTS_DIR, 'lib', 'anti-debug.js'), dest: path.join(DIST_LIB_DIR, 'anti-debug.js') },
  { source: path.join(SCRIPTS_DIR, 'lib', 'security-honeypot.js'), dest: path.join(DIST_LIB_DIR, 'security-honeypot.js') }
];

/**
 * 清理 dist 目录
 */
function cleanDist() {
  console.log('正在清理旧的构建产物...');
  try {
    if (fs.existsSync(DIST_DIR)) {
      fs.removeSync(DIST_DIR);
      console.log(`已删除旧的目录: ${DIST_DIR}`);
    }
    console.log('清理完成。');
  } catch (error) {
    console.error('清理 dist 目录失败:', error);
    throw error;
  }
}

/**
 * 执行 NestJS 构建命令
 */
function runNestBuild() {
  console.log('正在构建项目 (pnpm exec nest build)...');
  try {
    // 在项目根目录执行构建命令
    execSync('pnpm exec nest build', { cwd: ROOT_DIR, stdio: 'inherit' });
    console.log('项目构建成功。');
  } catch (error) {
    console.error('项目构建失败:', error);
    throw error;
  }
}

/**
 * 复制静态文件到 dist 目录
 */
async function copyStaticFiles() {
  console.log('正在复制静态文件到 dist 目录...');
  const staticFiles = [
    { src: path.join(ROOT_DIR, '.env.example'), dest: path.join(DIST_DIR, '.env.example'), optional: true },
    { src: path.join(KEYS_DIR, 'root-pub.pem'), dest: path.join(DIST_DIR, 'root-pub.pem') },
  ];

  try {
    await Promise.all(staticFiles.map(async (file) => {
      const sourcePath = path.resolve(ROOT_DIR, file.src);
      const destPath = path.resolve(ROOT_DIR, file.dest);
      try {
        if (fs.existsSync(sourcePath)) {
          await fs.copy(sourcePath, destPath);
          console.log(`  已复制: ${path.relative(ROOT_DIR, sourcePath)} -> ${path.relative(ROOT_DIR, destPath)}`);
        } else if (!file.optional) {
          console.warn(`警告: 必要文件未找到，跳过复制: ${sourcePath}`);
          // 可以选择在这里抛出错误
          // throw new Error(`必要文件未找到: ${sourcePath}`);
        } else {
          console.log(`  可选文件未找到，跳过复制: ${sourcePath}`);
        }
      } catch (copyError) {
        console.error(`复制文件 ${sourcePath} 到 ${destPath} 失败:`, copyError);
        throw copyError; // 重新抛出错误以停止 Promise.all
      }
    }));

    // 复制安全模块
    console.log('  正在复制安全模块...');
    fs.ensureDirSync(DIST_LIB_DIR); // 确保 dist/lib 目录存在

    await Promise.all(SECURITY_MODULES.map(async (module) => {
      try {
        if (fs.existsSync(module.source)) {
          await fs.copy(module.source, module.dest);
          console.log(`  已复制安全模块: ${path.relative(ROOT_DIR, module.source)} -> ${path.relative(ROOT_DIR, module.dest)}`);
        } else {
          console.warn(`警告: 安全模块未找到，跳过复制: ${module.source}`);
        }
      } catch (copyError) {
        console.error(`复制安全模块 ${module.source} 到 ${module.dest} 失败:`, copyError);
        throw copyError;
      }
    }));

    // 生成假的AES密钥作为诱饵
    console.log('  正在生成诱饵密钥文件...');
    // 读取真实的AES密钥
    const realKeyPath = path.join(KEYS_DIR, 'aes.key');
    if (!fs.existsSync(realKeyPath)) {
      console.warn('  警告: 真实密钥文件未找到，生成随机诱饵密钥。');
      const randomKey = crypto.randomBytes(32).toString('hex');
      fs.writeFileSync(path.join(DIST_DIR, 'aes.key'), randomKey);
    } else {
      // 读取真实密钥并生成故意打乱的版本
      let realKey = fs.readFileSync(realKeyPath, 'utf8').trim();
      // 打乱密钥（反转+替换部分字符）
      let fakeKey = realKey.split('').reverse().join('');
      // 随机替换25%的字符
      const replaceCount = Math.floor(fakeKey.length * 0.25);
      for (let i = 0; i < replaceCount; i++) {
        const pos = Math.floor(Math.random() * fakeKey.length);
        const charCode = Math.floor(Math.random() * 26) + 97; // a-z
        const replacement = String.fromCharCode(charCode);
        fakeKey = fakeKey.substring(0, pos) + replacement + fakeKey.substring(pos + 1);
      }
      fs.writeFileSync(path.join(DIST_DIR, 'aes.key'), fakeKey);
    }
    console.log('  诱饵密钥文件已生成并写入。');

    console.log('静态文件复制完成。');
  } catch (error) {
    console.error('复制静态文件过程中发生错误:', error);
    throw error; // 确保错误向上传播
  }
}

/**
 * 混淆 dist/main.js
 */
function obfuscateMainJs() {
  console.log('\n正在混淆核心文件 (dist/main.js)...');
  const mainJsPath = path.join(DIST_DIR, 'main.js');

  try {
    if (!fs.existsSync(mainJsPath)) {
      throw new Error(`构建后的 main.js 未找到，无法混淆: ${mainJsPath}`);
    }

    const mainJsContent = fs.readFileSync(mainJsPath, 'utf8');
    console.log(`  读取 dist/main.js 成功 (大小: ${Buffer.byteLength(mainJsContent, 'utf8')} bytes)。`);

    // 配置混淆选项 (可以根据需要调整，这是一个较强的配置示例)
    const obfuscationOptions = {
      compact: true, // 压缩代码
      controlFlowFlattening: true, // 复杂化控制流
      controlFlowFlatteningThreshold: 1, // 100% 应用控制流扁平化
      deadCodeInjection: true, // 注入无用代码
      deadCodeInjectionThreshold: 0.4, // 40% 概率注入无用代码
      debugProtection: false, // 开启后会在开发者工具打开时进入死循环 (可选)
      debugProtectionInterval: 0, // 如果开启 debugProtection，需要设置间隔
      disableConsoleOutput: false, // 禁止 console 输出 (可选，可能影响调试)
      identifierNamesGenerator: 'hexadecimal', // 标识符使用十六进制命名
      log: false, // 不输出混淆日志
      numbersToExpressions: true, // 将数字转换为表达式
      renameGlobals: false, // 不重命名全局变量和函数名 (避免破坏外部依赖)
      selfDefending: true, // 代码自我保护，使其难以格式化或重命名变量
      simplify: true, // 简化代码结构
      splitStrings: true, // 分割字符串文字
      splitStringsChunkLength: 10, // 分割字符串的长度
      stringArray: true, // 将字符串移到一个数组中
      stringArrayCallsTransform: true, // 转换对字符串数组的调用
      stringArrayEncoding: ['base64'], // 字符串数组编码 (可以添加 'rc4')
      stringArrayIndexShift: true, // 移动字符串数组索引
      stringArrayRotate: true, // 旋转字符串数组
      stringArrayShuffle: true, // 打乱字符串数组顺序
      stringArrayWrappersCount: 2, // 字符串数组包装器数量
      stringArrayWrappersChainedCalls: true, // 链式调用字符串数组包装器
      stringArrayWrappersParametersMaxCount: 4, // 包装器参数最大数量
      stringArrayWrappersType: 'function', // 包装器类型
      stringArrayThreshold: 0.75, // 75% 的字符串移入数组
      transformObjectKeys: true, // 转换对象键名
      unicodeEscapeSequence: false // 不使用 Unicode 转义序列 (可能影响性能)
    };


    console.log('  开始混淆...');
    const obfuscationResult = JavaScriptObfuscator.obfuscate(mainJsContent, obfuscationOptions);
    const obfuscatedCode = obfuscationResult.getObfuscatedCode();
    console.log(`  混淆完成 (大小: ${Buffer.byteLength(obfuscatedCode, 'utf8')} bytes)。`);

    // 将混淆后的代码写回 main.js
    fs.writeFileSync(mainJsPath, obfuscatedCode);
    console.log(`  混淆后的代码已写回: ${path.relative(ROOT_DIR, mainJsPath)}`);

    console.log('核心文件混淆完成。');

  } catch (error) {
    console.error('\n混淆核心文件失败:', error);
    throw error;
  }
}

// 添加固定密钥生成函数 - 与launcher-template.js中的buildFixedDecryptionKey函数对应
function generateFixedEncryptionKey() {
  try {
    // 读取AES密钥
    const aesKeyPath = path.join(KEYS_DIR, 'aes.key');
    if (!fs.existsSync(aesKeyPath)) {
      throw new Error(`AES密钥文件未找到: ${aesKeyPath}`);
    }
    const aesKey = fs.readFileSync(aesKeyPath, 'utf8').trim();
    console.log('  原始AES密钥已读取。');

    // 分割并编码密钥为不同形式
    const encodedKeyParts = splitAndEncodeKey(aesKey);

    // 重新组合密钥片段（与launcher.js中的buildFixedDecryptionKey逻辑一致）
    const segments = [
      Buffer.from(encodedKeyParts.segment1Base64, 'base64').toString(),
      Buffer.from(encodedKeyParts.segment2Base64, 'base64').toString(),
      // 对ASCII码应用偏移解码
      String.fromCharCode(...encodedKeyParts.segment3Ascii.map(c => c - 5)),
      // 十六进制解码
      (() => {
        const hex = encodedKeyParts.segment4Hex;
        let result = '';
        for (let i = 0; i < hex.length; i += 2) {
          result += String.fromCharCode(parseInt(hex.substring(i, i + 2), 16));
        }
        return result;
      })()
    ];

    // 组合基础密钥
    const baseKey = segments.join('');

    // 生成最终密钥
    const keyBuffer = crypto.createHash('sha256')
      .update(baseKey)
      .digest(); // 直接获取Buffer，正好是32字节

    return keyBuffer.toString('base64');
  } catch (error) {
    console.error('生成固定加密密钥时出错:', error);
    throw error;
  }
}

/**
 * 加密 dist/main.js 并处理相关文件
 */
function encryptMainJs() {
  console.log('\n开始加密核心文件...');
  const mainJsPath = path.join(DIST_DIR, 'main.js');
  const mainEncPath = path.join(DIST_DIR, 'main.js.enc');
  const aesLoaderSourcePath = path.join(SCRIPTS_DIR, 'lib', 'aes-loader.js');
  const aesLoaderDestPath = path.join(DIST_LIB_DIR, 'aes-loader.js');

  try {
    // 1. 检查必要文件是否存在
    if (!fs.existsSync(mainJsPath)) {
      throw new Error(`编译后的 main.js 未找到: ${mainJsPath}`);
    }
    if (!fs.existsSync(aesLoaderSourcePath)) {
      throw new Error(`aes-loader.js 源文件未找到: ${aesLoaderSourcePath}`);
    }

    // 2. 生成加密密钥
    console.log('  正在生成加密密钥...');
    const aesKey = generateFixedEncryptionKey();
    console.log('  加密密钥已生成。');

    // 3. 读取 main.js 内容
    const mainJsContent = fs.readFileSync(mainJsPath);
    console.log(`  读取 dist/main.js 成功 (大小: ${mainJsContent.length} bytes)。`);

    // 4. 加密内容
    const encryptedContent = aesLoader.encryptData(mainJsContent, aesKey);
    console.log('  main.js 内容加密完成。');

    // 5. 写入加密文件
    fs.writeFileSync(mainEncPath, encryptedContent);
    console.log(`  加密文件已写入: ${path.relative(ROOT_DIR, mainEncPath)}`);

    // 6. 删除原始 main.js
    fs.removeSync(mainJsPath);
    console.log(`  已删除原始文件: ${path.relative(ROOT_DIR, mainJsPath)}`);

    // 7. 复制 aes-loader.js 到 dist/lib
    fs.ensureDirSync(DIST_LIB_DIR); // 确保 dist/lib 目录存在
    fs.copySync(aesLoaderSourcePath, aesLoaderDestPath);
    console.log(`  已复制: ${path.relative(ROOT_DIR, aesLoaderSourcePath)} -> ${path.relative(ROOT_DIR, aesLoaderDestPath)}`);

    // 8. 复制 machine-code.js 到 dist/lib
    const machineCodeSourcePath = path.join(SCRIPTS_DIR, 'machine-code.js');
    const machineCodeDestPath = path.join(DIST_LIB_DIR, 'machine-code.js');
    if (fs.existsSync(machineCodeSourcePath)) {
      fs.copySync(machineCodeSourcePath, machineCodeDestPath);
      console.log(`  已复制: ${path.relative(ROOT_DIR, machineCodeSourcePath)} -> ${path.relative(ROOT_DIR, machineCodeDestPath)}`);
    } else {
      console.warn(`警告: machine-code.js 源文件未找到: ${machineCodeSourcePath}`);
    }

    console.log('核心文件加密与相关库复制完成。'); // 更新日志消息

  } catch (error) {
    console.error('\n处理核心文件或相关库时失败:', error); // 更新错误日志消息
    throw error;
  }
}

/**
 * 混淆 dist/launcher.js
 */
function obfuscateLauncherJs() {
  console.log('\n正在混淆启动器文件 (dist/launcher.js)...');
  const launcherJsPath = path.join(DIST_DIR, 'launcher.js');

  try {
    if (!fs.existsSync(launcherJsPath)) {
      throw new Error(`启动器文件未找到，无法混淆: ${launcherJsPath}`);
    }

    const launcherJsContent = fs.readFileSync(launcherJsPath, 'utf8');
    console.log(`  读取 dist/launcher.js 成功 (大小: ${Buffer.byteLength(launcherJsContent, 'utf8')} bytes)。`);

    // 使用与 main.js 类似的强混淆选项，确保 renameGlobals 为 false
    const obfuscationOptions = {
      compact: true,
      controlFlowFlattening: true,
      controlFlowFlatteningThreshold: 0.75, // 可以适当降低，启动器逻辑相对简单
      deadCodeInjection: true,
      deadCodeInjectionThreshold: 0.4,
      debugProtection: false,
      debugProtectionInterval: 0,
      disableConsoleOutput: false, // 保持 false，以便看到启动初期的错误
      identifierNamesGenerator: 'hexadecimal',
      log: false,
      numbersToExpressions: true,
      renameGlobals: false, // 必须为 false，否则会破坏 Node.js 内置模块和依赖
      selfDefending: true,
      simplify: true,
      splitStrings: true,
      splitStringsChunkLength: 15, // 可以稍微长一点
      stringArray: true,
      stringArrayCallsTransform: true,
      stringArrayEncoding: ['base64'],
      stringArrayIndexShift: true,
      stringArrayRotate: true,
      stringArrayShuffle: true,
      stringArrayWrappersCount: 1, // 启动器简单，1个包装器即可
      stringArrayWrappersChainedCalls: true,
      stringArrayWrappersParametersMaxCount: 2,
      stringArrayWrappersType: 'function',
      stringArrayThreshold: 0.75,
      transformObjectKeys: true,
      unicodeEscapeSequence: false
    };

    console.log('  开始混淆启动器...');
    const obfuscationResult = JavaScriptObfuscator.obfuscate(launcherJsContent, obfuscationOptions);
    const obfuscatedCode = obfuscationResult.getObfuscatedCode();
    console.log(`  混淆完成 (大小: ${Buffer.byteLength(obfuscatedCode, 'utf8')} bytes)。`);

    // 将混淆后的代码写回 launcher.js
    fs.writeFileSync(launcherJsPath, obfuscatedCode);
    console.log(`  混淆后的启动器代码已写回: ${path.relative(ROOT_DIR, launcherJsPath)}`);

    console.log('启动器文件混淆完成。');

  } catch (error) {
    console.error('\n混淆启动器文件失败:', error);
    throw error;
  }
}

/**
 * 处理并复制引导程序模板 (_bootstrap.js)
 */
function copyBootstrapTemplate() {
  console.log('\n正在处理并复制引导程序模板...');
  const sourcePath = path.join(SCRIPTS_DIR, 'bootstrap-template.js');
  const destPath = path.join(DIST_DIR, '_bootstrap.js');
  const mainEncPath = path.join(DIST_DIR, 'main.js.enc');
  const placeholder = '__EXPECTED_MAIN_ENC_HASH__'; // 定义占位符

  try {
    // 1. 检查必要文件是否存在
    if (!fs.existsSync(sourcePath)) {
      throw new Error(`引导程序模板未找到: ${sourcePath}`);
    }
    if (!fs.existsSync(mainEncPath)) {
      throw new Error(`加密后的主文件未找到: ${mainEncPath}，无法计算哈希。`);
    }

    // 2. 计算 main.js.enc 的哈希值
    const mainEncContent = fs.readFileSync(mainEncPath);
    const mainEncHash = crypto.createHash('sha256').update(mainEncContent).digest('hex');
    console.log(`  计算得到 main.js.enc 的 SHA256 哈希: ${mainEncHash}`);

    // 3. 读取模板内容
    let templateContent = fs.readFileSync(sourcePath, 'utf8');
    console.log('  引导程序模板已读取。');

    // 4. 替换占位符
    if (templateContent.includes(placeholder)) {
      templateContent = templateContent.replace(placeholder, mainEncHash);
      console.log(`  已将占位符 ${placeholder} 替换为实际哈希值。`);
    } else {
      console.warn(`警告: 在 ${sourcePath} 中未找到占位符 ${placeholder}，哈希值未注入。`);
    }

    // 5. 写入处理后的内容到目标路径
    fs.writeFileSync(destPath, templateContent);
    console.log(`  处理后的引导程序已写入: ${path.relative(ROOT_DIR, destPath)}`);

    console.log('引导程序处理和复制完成。');
  } catch (error) {
    console.error('处理或复制引导程序失败:', error);
    throw error;
  }
}

/**
 * 将AES密钥分割并编码为不同形式的片段
 * @param {string} aesKey - 原始AES密钥
 * @returns {Object} - 包含编码后的密钥片段
 */
function splitAndEncodeKey(aesKey) {
  // 确保密钥有足够长度
  if (!aesKey || aesKey.length < 32) {
    throw new Error('AES密钥长度不足，无法安全分割');
  }

  // 将密钥分成四个部分
  const keyLength = aesKey.length;
  const segmentLength = Math.floor(keyLength / 4);

  const segment1 = aesKey.substring(0, segmentLength);
  const segment2 = aesKey.substring(segmentLength, segmentLength * 2);
  const segment3 = aesKey.substring(segmentLength * 2, segmentLength * 3);
  const segment4 = aesKey.substring(segmentLength * 3);

  // 以不同方式编码各个片段
  const encodedSegment1 = Buffer.from(segment1).toString('base64');
  const encodedSegment2 = Buffer.from(segment2).toString('base64');

  // 将segment3转换为ASCII码数组并增加偏移
  const asciiSegment3 = Array.from(segment3).map(char => char.charCodeAt(0) + 5);

  // 将segment4转换为十六进制
  const hexSegment4 = Array.from(segment4)
    .map(char => char.charCodeAt(0).toString(16))
    .join('');

  return {
    segment1Base64: encodedSegment1,
    segment2Base64: encodedSegment2,
    segment3Ascii: asciiSegment3,
    segment4Hex: hexSegment4
  };
}

/**
 * 处理启动器模板，注入密钥片段
 */
function processLauncherTemplate() {
  console.log('\n正在处理启动器模板...');
  const sourcePath = path.join(SCRIPTS_DIR, 'launcher-template.js');
  const destPath = path.join(DIST_DIR, 'launcher.js');
  const aesKeyPath = path.join(KEYS_DIR, 'aes.key');

  try {
    // 1. 检查必要文件是否存在
    if (!fs.existsSync(sourcePath)) {
      throw new Error(`启动器模板未找到: ${sourcePath}`);
    }
    if (!fs.existsSync(aesKeyPath)) {
      throw new Error(`AES密钥文件未找到: ${aesKeyPath}`);
    }

    // 2. 读取AES密钥和启动器模板
    const aesKey = fs.readFileSync(aesKeyPath, 'utf8').trim();
    let templateContent = fs.readFileSync(sourcePath, 'utf8');
    console.log('  启动器模板和AES密钥已读取。');

    // 3. 分割并编码密钥
    const encodedKeyParts = splitAndEncodeKey(aesKey);
    console.log('  已将AES密钥分割并编码为不同形式。');

    // 4. 定义需要替换的占位符
    const placeholders = {
      '__CIPHER_PART1_BASE64__': encodedKeyParts.segment1Base64,
      '__CIPHER_PART2_BASE64__': encodedKeyParts.segment2Base64,
      '__CIPHER_PART3_ASCII__': JSON.stringify(encodedKeyParts.segment3Ascii),
      '__CIPHER_PART4_HEX__': encodedKeyParts.segment4Hex
    };

    // 5. 替换占位符
    for (const [placeholder, value] of Object.entries(placeholders)) {
      if (templateContent.includes(placeholder)) {
        templateContent = templateContent.replace(new RegExp(placeholder, 'g'), value);
        console.log(`  已替换占位符 ${placeholder}`);
      } else {
        console.warn(`  警告: 在启动器模板中未找到占位符 ${placeholder}`);
      }
    }

    // 6. 写入处理后的内容到目标路径
    fs.writeFileSync(destPath, templateContent);
    console.log(`  处理后的启动器已写入: ${path.relative(ROOT_DIR, destPath)}`);

    console.log('启动器模板处理完成。');
    return true;
  } catch (error) {
    console.error('\n处理启动器模板失败:', error);
    throw error;
  }
}

/**
 * 复制启动器模板
 */
function copyLauncherTemplate() {
  console.log('\n正在处理并复制启动器...');

  try {
    // 调用新函数处理启动器模板
    const success = processLauncherTemplate();

    if (success) {
      console.log('启动器处理和复制完成。');
    } else {
      throw new Error('启动器模板处理失败');
    }
  } catch (error) {
    console.error('处理和复制启动器失败:', error);
    throw error;
  }
}

/**
 * 为 _bootstrap.js 生成签名
 */
function generateSignature() {
  console.log('\n正在为 _bootstrap.js 生成签名...');
  const bootstrapPath = path.join(DIST_DIR, '_bootstrap.js');
  const signaturePath = path.join(DIST_DIR, '_bootstrap.sig');
  const privateKeyPath = path.join(KEYS_DIR, 'root-priv.pem');

  try {
    // 1. 检查文件是否存在
    if (!fs.existsSync(bootstrapPath)) {
      throw new Error(`引导程序脚本未找到: ${bootstrapPath}`);
    }
    if (!fs.existsSync(privateKeyPath)) {
      throw new Error(`私钥文件未找到: ${privateKeyPath}`);
    }

    // 2. 读取私钥和引导程序内容
    const privateKey = fs.readFileSync(privateKeyPath);
    const bootstrapContent = fs.readFileSync(bootstrapPath);
    console.log(`  读取 ${path.relative(ROOT_DIR, bootstrapPath)} 和私钥成功。`);
    const hash = crypto.createHash('sha256').update(bootstrapContent).digest('hex');
    console.log(`  [BUILD] _bootstrap.js content hash: ${hash}`);

    // 3. 创建签名对象并更新内容
    const sign = crypto.createSign('RSA-SHA256');
    sign.update(bootstrapContent);
    sign.end();

    // 4. 生成签名 (Base64 格式)
    const signature = sign.sign(privateKey, 'base64');
    console.log('  签名已生成。');

    // 5. 保存签名到文件
    fs.writeFileSync(signaturePath, signature);
    console.log(`  签名已保存到: ${path.relative(ROOT_DIR, signaturePath)}`);

    console.log('_bootstrap.js 签名生成完成。');

  } catch (error) {
    console.error('\n生成签名失败:', error);
    throw error;
  }
}

/**
 * 混淆安全模块
 */
function obfuscateSecurityModules() {
  console.log('\n正在混淆安全模块...');

  // 使用与launcher.js类似的混淆选项
  const obfuscationOptions = {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.6,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.3,
    debugProtection: false,
    identifierNamesGenerator: 'hexadecimal',
    log: false,
    renameGlobals: false,
    selfDefending: true,
    simplify: true,
    splitStrings: true,
    stringArray: true,
    stringArrayCallsTransform: true,
    stringArrayEncoding: ['base64'],
    stringArrayThreshold: 0.75,
    transformObjectKeys: true
  };

  try {
    // 需要混淆的所有模块路径列表
    const modulesToObfuscate = [
      ...SECURITY_MODULES.map(module => module.dest), // 已有的安全模块
      path.join(DIST_LIB_DIR, 'aes-loader.js'),      // 添加AES加载器
      path.join(DIST_LIB_DIR, 'machine-code.js')     // 添加机器码模块
    ];

    // 混淆每个模块
    for (const modulePath of modulesToObfuscate) {
      if (!fs.existsSync(modulePath)) {
        console.warn(`  警告: 模块文件未找到: ${modulePath}，跳过混淆`);
        continue;
      }

      const moduleContent = fs.readFileSync(modulePath, 'utf8');
      console.log(`  读取模块: ${path.relative(ROOT_DIR, modulePath)} (大小: ${Buffer.byteLength(moduleContent, 'utf8')} bytes)`);

      const obfuscationResult = JavaScriptObfuscator.obfuscate(moduleContent, obfuscationOptions);
      const obfuscatedCode = obfuscationResult.getObfuscatedCode();

      fs.writeFileSync(modulePath, obfuscatedCode);
      console.log(`  混淆完成: ${path.relative(ROOT_DIR, modulePath)} (大小: ${Buffer.byteLength(obfuscatedCode, 'utf8')} bytes)`);
    }

    console.log('安全模块混淆完成。');
  } catch (error) {
    console.error('混淆安全模块失败:', error);
    throw error;
  }
}

/**
 * 混淆引导程序 _bootstrap.js
 */
function obfuscateBootstrap() {
  console.log('\n正在混淆引导程序...');
  const bootstrapPath = path.join(DIST_DIR, '_bootstrap.js');

  try {
    if (!fs.existsSync(bootstrapPath)) {
      throw new Error(`引导程序未找到，无法混淆: ${bootstrapPath}`);
    }

    const bootstrapContent = fs.readFileSync(bootstrapPath, 'utf8');
    console.log(`  读取 _bootstrap.js 成功 (大小: ${Buffer.byteLength(bootstrapContent, 'utf8')} bytes)`);

    // 使用与其他模块类似的混淆选项
    const obfuscationOptions = {
      compact: true,
      controlFlowFlattening: true,
      controlFlowFlatteningThreshold: 0.6,
      deadCodeInjection: true,
      deadCodeInjectionThreshold: 0.3,
      debugProtection: false,
      identifierNamesGenerator: 'hexadecimal',
      log: false,
      renameGlobals: false,
      selfDefending: true,
      simplify: true,
      splitStrings: true,
      stringArray: true,
      stringArrayCallsTransform: true,
      stringArrayEncoding: ['base64'],
      stringArrayThreshold: 0.75,
      transformObjectKeys: true
    };

    console.log('  开始混淆引导程序...');
    const obfuscationResult = JavaScriptObfuscator.obfuscate(bootstrapContent, obfuscationOptions);
    const obfuscatedCode = obfuscationResult.getObfuscatedCode();
    console.log(`  混淆完成 (大小: ${Buffer.byteLength(obfuscatedCode, 'utf8')} bytes)`);

    // 将混淆后的代码写回 _bootstrap.js
    fs.writeFileSync(bootstrapPath, obfuscatedCode);
    console.log(`  混淆后的引导程序已写回: ${path.relative(ROOT_DIR, bootstrapPath)}`);

    console.log('引导程序混淆完成。');
  } catch (error) {
    console.error('\n混淆引导程序失败:', error);
    throw error;
  }
}

/**
 * 主构建函数
 */
async function build() {
  console.log('======== 开始构建 ======== ');
  const startTime = Date.now();

  try {
    // 1. 清理 dist
    cleanDist();

    // 2. 执行 NestJS 构建
    runNestBuild();

    // 3. 混淆 main.js
    obfuscateMainJs();

    // 4. 复制静态文件
    await copyStaticFiles();

    // 5. 加密 main.js (混淆后的)
    encryptMainJs();

    // 6. 处理并复制引导程序模板
    copyBootstrapTemplate();

    // 7. 混淆 bootstrap 模块 (在生成签名之前)
    obfuscateBootstrap();

    // 8. 生成签名 (必须在混淆bootstrap之后)
    generateSignature();

    // 9. 复制启动器模板
    copyLauncherTemplate();

    // 10. 混淆 launcher.js
    obfuscateLauncherJs();

    // 11. 混淆安全模块
    obfuscateSecurityModules();

    const duration = (Date.now() - startTime) / 1000;
    console.log(`\n======== 构建成功 (${duration.toFixed(1)}s) ========`);

  } catch (error) {
    console.error('\n======== 构建失败 ======== ');
    process.exit(1);
  }
}

// 执行构建
build();
