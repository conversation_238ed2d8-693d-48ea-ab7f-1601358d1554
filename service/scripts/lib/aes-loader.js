'use strict';

/**
 * AES加密/解密模块
 * 负责加密主模块和在运行时解密执行
 */
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const vm = require('vm');

// 运行时监测变量
let lastExecutionCheck = Date.now();
let securityChecks = 0;

/**
 * 使用AES-256-CBC加密数据
 * @param {Buffer|string} data 要加密的数据
 * @param {string} key Base64编码的AES密钥
 * @return {Buffer} 加密后的数据
 */
function encryptData(data, key) {
  try {
    // 转换密钥格式
    const keyBuffer = Buffer.from(key, 'base64');

    // 生成随机IV
    const iv = crypto.randomBytes(16);

    // 创建加密器
    const cipher = crypto.createCipheriv('aes-256-cbc', keyBuffer, iv);

    // 加密数据
    let encrypted;
    if (Buffer.isBuffer(data)) {
      encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
    } else {
      encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
    }

    // 将IV和加密后的数据组合
    return Buffer.concat([iv, encrypted]);
  } catch (error) {
    console.error('[加密错误]', error);
    throw error;
  }
}

/**
 * 解密AES-256-CBC加密的数据
 * @param {Buffer} encryptedData 加密的数据
 * @param {string} key Base64编码的AES密钥
 * @return {Buffer} 解密后的数据
 */
function decryptData(encryptedData, key) {
  try {
    // 检查参数
    if (!Buffer.isBuffer(encryptedData)) {
      throw new Error('加密数据必须是Buffer类型');
    }

    // 从加密数据中提取IV (前16字节)
    const iv = encryptedData.slice(0, 16);
    const encrypted = encryptedData.slice(16);

    // 转换密钥
    const keyBuffer = Buffer.from(key, 'base64');

    // 创建解密器
    const decipher = crypto.createDecipheriv('aes-256-cbc', keyBuffer, iv);

    // 解密数据
    return Buffer.concat([decipher.update(encrypted), decipher.final()]);
  } catch (error) {
    console.error('[解密错误]', error);
    throw error;
  }
}

/**
 * 运行时安全检查
 */
function performRuntimeSecurityCheck() {
  const currentTime = Date.now();
  const elapsed = currentTime - lastExecutionCheck;

  // 如果上次检查到现在的时间异常长，可能有调试器暂停了执行
  if (elapsed > 5000 && securityChecks > 0) {
    console.error('[安全警告] 检测到可能的执行中断，程序将终止');
    process.exit(3);
  }

  // 避免在首次运行时触发警告
  lastExecutionCheck = currentTime;
  securityChecks++;

  // 检查关键API是否被篡改
  const originalRequire = module.constructor.prototype.require;
  const originalReadFile = fs.readFileSync;

  if (module.constructor.prototype.require !== originalRequire) {
    console.error('[安全警告] 检测到require函数被篡改，程序将终止');
    process.exit(3);
  }

  if (fs.readFileSync !== originalReadFile) {
    console.error('[安全警告] 检测到fs.readFileSync函数被篡改，程序将终止');
    process.exit(3);
  }

  // 随机执行一些无用操作，以检测时间异常
  const start = Date.now();
  let sum = 0;
  for (let i = 0; i < 10000; i++) {
    sum += Math.sqrt(i);
  }
  const executionTime = Date.now() - start;

  // 通常这个操作应该只需几毫秒，如果超过100ms，可能存在调试器
  if (executionTime > 100) {
    console.warn('[安全警告] 检测到执行时间异常，可能存在调试器');
    // 这里不直接终止，只是警告
  }

  return sum > 0; // 防止被优化掉
}

/**
 * 从加密文件中加载并执行主模块
 */
function loadEncryptedModule() {
  try {
    // 安全检查
    performRuntimeSecurityCheck();

    const APP_DIR = path.dirname(__dirname);
    const MAIN_ENC_PATH = path.join(APP_DIR, 'main.js.enc');

    // 从launcher中导入buildFixedDecryptionKey函数
    // 假设launcher已经把这个函数暴露到全局，或者通过某种方式传递给了loader
    const buildDecryptionKey = global.buildFixedDecryptionKey ||
      require(path.join(APP_DIR, 'launcher')).buildFixedDecryptionKey;

    if (!buildDecryptionKey || typeof buildDecryptionKey !== 'function') {
      throw new Error('无法获取解密密钥生成器');
    }

    // 生成解密密钥
    const decryptionKey = buildDecryptionKey();

    // 读取加密的主模块
    const encryptedData = fs.readFileSync(MAIN_ENC_PATH);

    // 解密主模块
    const decryptedData = decryptData(encryptedData, decryptionKey);

    // 为解密后的代码创建模块
    const mainModule = {
      exports: {},
      filename: path.join(APP_DIR, 'main.js'),
      id: 'main',
      loaded: false,
      paths: module.paths
    };

    // 创建模块包装器
    const moduleWrapper = `(function(exports, require, module, __filename, __dirname) {
      ${decryptedData.toString('utf8')}
    });`;

    // 编译并执行模块
    const script = vm.createScript(moduleWrapper, {
      filename: mainModule.filename,
      lineOffset: 0,
      displayErrors: true
    });

    // 获取编译后的函数
    const compiledWrapper = script.runInThisContext();

    // 添加定期安全检查
    setInterval(performRuntimeSecurityCheck, 5000);

    // 执行模块
    compiledWrapper.call(
      mainModule.exports,
      mainModule.exports,
      require,
      mainModule,
      mainModule.filename,
      path.dirname(mainModule.filename)
    );

    mainModule.loaded = true;

    return mainModule.exports;
  } catch (error) {
    console.error('[致命错误] 无法加载主模块:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，加载加密模块
if (require.main === module) {
  loadEncryptedModule();
}

module.exports = {
  encryptData,
  decryptData,
  loadEncryptedModule
};
