/**
 * Security Honeypot Module
 *
 * 蜜罐安全模块：提供各种欺骗机制和安全陷阱，用于检测和误导破解尝试
 * 该模块不会影响正常用户的使用，但会对篡改行为设置陷阱
 */

'use strict';

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const os = require('os');

// 关键文件路径，用于完整性校验
const CRITICAL_FILES = [
    './launcher.js',
    './lib/aes-loader.js',
    './lib/integrity-check.js',
    './lib/anti-debug.js',
    './_bootstrap.js'
];

// 诱饵密钥列表 (看起来像真的密钥但实际并不使用)
const HONEYPOT_KEYS = {
    // 假的AES密钥 - 放在注释里暗示这是备用密钥
    // encryption key backup: a7f939cb54e78d12
    BACKUP_AES_KEY: 'a7f939cb54e78d12e6f341b7ac298019',

    // 假授权密钥 - 看起来像是硬编码的测试用密钥
    TEST_LICENSE: 'eyJtYWNoaW5lSWQiOiJhbnlfbWFjaGluZSIsImV4cGlyeURhdGUiOiIyMDk5LTEyLTMxVDIzOjU5OjU5LjAwMFoifQ.SiGn4tuRe+FaKE',

    // 伪装成Base64的密钥片段
    SEGMENT_KEY: 'dGhpc2lzYWZha2VrZXlzZWdtZW50MTIzNDU2Nzg5',

    // 替代解密密钥 - 这个看起来像是真实密钥，但实际在解密时会导致错误
    ALT_DECRYPT_KEY: Buffer.from('RiPUK8dhy7BJPzQhbm4dY/q9LEzK3SUACfIXDYD5dws=', 'base64'),
};

// 看起来可行的假验证路径标识符
const ALT_AUTH_PATHS = [
    'standard', // 标准认证方式(实际真实路径)
    'legacy',   // 看起来像旧版本支持
    'testing',  // 看起来像测试模式
    'offline',  // 看起来像离线认证
    'trial'     // 看起来像试用认证
];

// 全局状态存储
let securityState = {
    // 上次检查时间
    lastCheck: Date.now(),
    // 检测到的可疑行为计数
    suspiciousActions: 0,
    // 是否已触发蜜罐行为
    honeypotTriggered: false,
    // 蜜罐模式 - 初始为正常
    mode: 'normal',
    // 启动时间
    startTime: Date.now(),
    // 蜜罐触发时间
    triggerTime: 0
};

/**
 * 检查是否使用了假密钥
 * @param {string} keyInput - 输入的密钥
 * @param {string} type - 密钥类型
 * @returns {boolean} - 如果检测到假密钥使用则返回true
 */
function detectHoneypotKeyUsage(keyInput, type) {
    if (!keyInput) return false;

    // 检查是否包含蜜罐密钥的任何部分
    for (const [name, value] of Object.entries(HONEYPOT_KEYS)) {
        // 如果密钥包含任何蜜罐密钥的部分
        if (typeof value === 'string' &&
            typeof keyInput === 'string' &&
            (keyInput.includes(value) || value.includes(keyInput))) {

            logSuspiciousActivity(`检测到蜜罐密钥使用: ${name}`, {
                keyType: type,
                honeypotKey: name,
                inputSample: keyInput.substring(0, 10) + '...' // 只记录一部分输入
            });

            // 增加可疑计数
            securityState.suspiciousActions += 1;

            // 密钥被使用，但返回false表示"正常"，不立即触发明显错误
            return true;
        }
    }

    return false;
}

/**
 * 检查代码完整性，验证关键文件是否被篡改
 * @returns {boolean} - 如果检测到篡改则返回true
 */
function detectCodeTampering() {
    try {
        // 计算关键文件的哈希值并比较
        // 在生产环境中，这会实际比较预先计算的哈希值
        // 这个简化版本随机触发，不影响正常用户

        // 生产环境会采用实际文件验证，此处模拟随机触发
        // 真实概率很低，不影响正常使用
        const randomCheck = Math.random() < 0.001; // 0.1%的概率触发

        if (randomCheck) {
            logSuspiciousActivity('检测到代码完整性可能受损', {
                reason: '随机完整性检查测试失败',
                criticalFile: CRITICAL_FILES[Math.floor(Math.random() * CRITICAL_FILES.length)]
            });
            return true;
        }

        return false;
    } catch (error) {
        // 吞掉错误，确保不影响正常运行
        return false;
    }
}

/**
 * 设置时间炸弹
 * 在未来随机时间触发功能降级
 */
function setTimeBomb() {
    if (securityState.honeypotTriggered) return;

    // 设置触发时间 - 随机2-5天后
    const bombDelay = 1000 * 60 * 60 * 24 * (2 + Math.random() * 3);
    securityState.triggerTime = Date.now() + bombDelay;

    // 设置炸弹模式
    securityState.mode = 'timed';

    // 记录但不显示
    logSuspiciousActivity('已设置时间炸弹', {
        triggerTime: new Date(securityState.triggerTime).toISOString(),
        estimatedDays: Math.round(bombDelay / (1000 * 60 * 60 * 24) * 10) / 10
    });
}

/**
 * 检查时间炸弹是否应该引爆
 * @returns {boolean} - 如果应该引爆则返回true
 */
function shouldExplodeTimeBomb() {
    // 如果设置了时间炸弹且当前时间超过触发时间
    return (
        securityState.mode === 'timed' &&
        securityState.triggerTime > 0 &&
        Date.now() > securityState.triggerTime
    );
}

/**
 * 生成看似合法但实际无效的响应
 * @param {string} type - 响应类型
 * @returns {any} - 伪造的响应对象
 */
function generateFakeValidResponse(type = 'auth') {
    if (type === 'auth') {
        // 返回看似有效的授权响应
        return {
            status: 'authorized',
            expiryDate: new Date(Date.now() + 86400000 * 30).toISOString(),
            licenseType: 'standard',
            // 故意返回的错误部分很小，难以调试
            _x: Buffer.from([0x78, 0x56]).toString('base64')
        };
    } else if (type === 'decryption') {
        // 返回看似有效的解密密钥 (但实际上解密会失败)
        return Buffer.from(HONEYPOT_KEYS.ALT_DECRYPT_KEY).toString('base64');
    } else {
        // 默认伪造响应
        return { status: 'ok', timestamp: Date.now() };
    }
}

/**
 * 检测是否应该应用蜜罐效果
 * 检查执行环境、可疑修改等
 * @returns {boolean} - 如果应该应用蜜罐行为则返回true
 */
function shouldApplyHoneypot() {
    // 如果已经处于蜜罐模式，继续应用蜜罐效果
    if (securityState.mode !== 'normal') {
        return true;
    }

    // 如果之前检测到过多可疑行为
    if (securityState.suspiciousActions >= 3) {
        securityState.mode = 'suspicious';
        return true;
    }

    // 如果时间炸弹应该触发
    if (shouldExplodeTimeBomb()) {
        securityState.mode = 'exploded';
        return true;
    }

    // 检查代码是否被篡改
    if (detectCodeTampering()) {
        securityState.mode = 'tampered';
        return true;
    }

    // 默认不触发蜜罐效果
    return false;
}

/**
 * 应用蜜罐效果
 * 根据蜜罐模式应用不同的效果
 */
function applyHoneypotEffects() {
    // 如果不应该应用蜜罐效果，就返回
    if (!shouldApplyHoneypot()) return;

    // 标记蜜罐已触发
    if (!securityState.honeypotTriggered) {
        securityState.honeypotTriggered = true;
        logSuspiciousActivity('蜜罐效果已被触发', {
            mode: securityState.mode,
            reason: getHoneypotTriggerReason()
        });
    }

    // 根据不同模式应用效果
    // 这些效果会潜移默化地造成一些功能问题，但不会立即崩溃
    switch (securityState.mode) {
        case 'suspicious':
            // 引入随机延迟
            setTimeout(() => { }, 100 + Math.random() * 500);
            break;

        case 'exploded':
            // 触发时间炸弹效果 - 可能会导致一些功能变慢或错误
            // 这是通过破坏一些内部状态实现的，不会立即崩溃
            // 这里不做实际破坏，只记录状态
            break;

        case 'tampered':
            // 篡改检测模式
            // 可能会引入一些计算错误或状态问题
            break;

        case 'timed':
            // 等待触发
            // 不做任何事情，只等待触发时间到达
            break;
    }
}

/**
 * 获取蜜罐触发原因
 * @returns {string} - 描述触发原因的字符串
 */
function getHoneypotTriggerReason() {
    switch (securityState.mode) {
        case 'suspicious':
            return '检测到多次可疑行为';
        case 'exploded':
            return '时间炸弹已触发';
        case 'tampered':
            return '检测到代码被篡改';
        case 'timed':
            return '时间炸弹已设置，等待触发';
        default:
            return '未知原因';
    }
}

/**
 * 记录可疑活动
 * 在生产环境中，这可能会写入隐藏的日志或发送匿名遥测
 * @param {string} message - 可疑活动描述
 * @param {Object} details - 详细信息
 */
function logSuspiciousActivity(message, details = {}) {
    try {
        // 在生产中，这可能会写入加密日志或发送到远程服务器
        // 此处仅创建内存日志，确保不干扰正常运行
        const logEntry = {
            timestamp: new Date().toISOString(),
            message,
            details,
            // 仅收集非敏感环境信息
            environment: {
                platform: process.platform,
                nodeVersion: process.version,
                uptime: Math.floor(process.uptime())
            }
        };

        // 可选: 写入隐藏日志文件
        // 注意：生产环境中应使用更隐蔽的位置和加密存储
        // fs.appendFileSync(
        //   path.join(os.tmpdir(), '.app-analytics.log'),
        //   JSON.stringify(logEntry) + '\n'
        // );

        // 静默记录，不输出任何信息
    } catch (error) {
        // 吞掉任何错误，确保不影响程序运行
    }
}

/**
 * 选择一个看似有效的授权路径
 * 正常用户总是会通过标准路径，而篡改者可能选择其他看起来有效的路径
 * @returns {string} - 授权路径的标识符
 */
function selectAuthPath() {
    // 随机生成一个看似有效的授权路径
    // 真实系统只会处理'standard'路径
    const randomIndex = Math.floor(Math.random() * ALT_AUTH_PATHS.length);
    return ALT_AUTH_PATHS[randomIndex];
}

/**
 * 混淆关键路径
 * 创建多个看似正确的验证路径，但只有一条是真实的
 * @param {Function} realValidator - 真实的验证函数
 * @param {string} input - 验证输入
 * @param {string} pathType - 请求的验证路径类型
 * @returns {boolean} - 验证是否通过
 */
function obfuscateValidationPath(realValidator, input, pathType = 'standard') {
    // 检查蜜罐是否应该被应用
    applyHoneypotEffects();

    // 如果请求的是标准路径且不在蜜罐模式，则使用真实验证
    if (pathType === 'standard' && securityState.mode === 'normal') {
        return realValidator(input);
    }

    // 如果请求非标准路径或在蜜罐模式，可能返回欺骗性结果
    // 有小概率返回true让破解者以为成功了
    if (pathType !== 'standard' || securityState.mode !== 'normal') {
        logSuspiciousActivity('尝试使用非标准验证路径', {
            pathType,
            honeypotActive: securityState.mode !== 'normal'
        });

        // 90%概率返回错误，10%概率假装成功诱导继续尝试
        return Math.random() < 0.1;
    }

    // 默认使用真实验证
    return realValidator(input);
}

// 导出主要功能
module.exports = {
    // 蜜罐检测函数
    detectHoneypotKeyUsage,
    detectCodeTampering,

    // 蜜罐设置函数
    setTimeBomb,

    // 蜜罐响应生成
    generateFakeValidResponse,

    // 路径混淆
    selectAuthPath,
    obfuscateValidationPath,

    // 蜜罐效果应用
    applyHoneypotEffects,
    shouldApplyHoneypot,

    // 诱饵密钥和常量 - 故意暴露给攻击者
    HONEYPOT_KEYS,
    ALT_AUTH_PATHS,

    // 初始化蜜罐 - 可用于重置状态
    initialize: function () {
        securityState = {
            lastCheck: Date.now(),
            suspiciousActions: 0,
            honeypotTriggered: false,
            mode: 'normal',
            startTime: Date.now(),
            triggerTime: 0
        };
        return true;
    }
};
