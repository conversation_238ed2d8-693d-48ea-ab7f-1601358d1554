'use strict';

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

/**
 * 代码完整性检查系统
 * 提供运行时文件完整性验证、内存代码完整性检查等功能
 */
class IntegrityChecker {
  constructor(options = {}) {
    this.checksumStore = new Map();
    this.interval = options.interval || 30000; // 默认每30秒检查一次
    this.criticalFiles = options.criticalFiles || [];
    this.criticalFunctions = options.criticalFunctions || [];
    this.onViolation = options.onViolation || this.defaultViolationHandler;
    this.checksumTimers = [];
    this.lastExecutionTime = {};
    this.runtimeChecks = true;
  }

  /**
   * 计算文件的哈希值
   */
  calculateFileHash(filePath, algorithm = 'sha256') {
    try {
      const fileContent = fs.readFileSync(filePath);
      return crypto.createHash(algorithm).update(fileContent).digest('hex');
    } catch (error) {
      console.error(`[IntegrityChecker] 无法计算文件哈希: ${filePath}`, error);
      return null;
    }
  }

  /**
   * 记录关键文件的初始哈希值
   */
  registerFiles(files) {
    if (!Array.isArray(files)) {
      files = [files];
    }

    files.forEach(file => {
      const hash = this.calculateFileHash(file);
      if (hash) {
        this.checksumStore.set(file, hash);
        this.criticalFiles.push(file);
      }
    });

    return this;
  }

  /**
   * 记录关键函数的哈希值
   */
  registerFunction(name, fn) {
    if (typeof fn !== 'function') {
      throw new Error('必须提供一个有效的函数');
    }

    // 获取函数的字符串表示并计算哈希
    const fnString = fn.toString();
    const hash = crypto.createHash('sha256').update(fnString).digest('hex');

    this.checksumStore.set(name, {
      hash,
      original: fn,
      // 创建一个包装函数以便进行运行时检查
      wrapper: function (...args) {
        // 在每次调用时验证函数完整性
        const currentHash = crypto.createHash('sha256').update(fn.toString()).digest('hex');
        if (hash !== currentHash) {
          this.onViolation('function_tampered', { name, expected: hash, actual: currentHash });
        }
        return fn.apply(this, args);
      }
    });

    this.criticalFunctions.push(name);

    return this.checksumStore.get(name).wrapper;
  }

  /**
   * 验证文件的完整性
   */
  verifyFile(filePath) {
    if (!this.checksumStore.has(filePath)) {
      return false;
    }

    const originalHash = this.checksumStore.get(filePath);
    const currentHash = this.calculateFileHash(filePath);

    if (originalHash !== currentHash) {
      this.onViolation('file_tampered', { path: filePath, expected: originalHash, actual: currentHash });
      return false;
    }

    return true;
  }

  /**
   * 验证函数的完整性
   */
  verifyFunction(name) {
    if (!this.checksumStore.has(name)) {
      return false;
    }

    const { hash, original } = this.checksumStore.get(name);
    const currentFnString = original.toString();
    const currentHash = crypto.createHash('sha256').update(currentFnString).digest('hex');

    if (hash !== currentHash) {
      this.onViolation('function_tampered', { name, expected: hash, actual: currentHash });
      return false;
    }

    return true;
  }

  /**
   * 执行所有注册文件的完整性检查
   */
  verifyAllFiles() {
    let allValid = true;

    for (const file of this.criticalFiles) {
      if (!this.verifyFile(file)) {
        allValid = false;
      }
    }

    return allValid;
  }

  /**
   * 执行所有注册函数的完整性检查
   */
  verifyAllFunctions() {
    let allValid = true;

    for (const fnName of this.criticalFunctions) {
      if (!this.verifyFunction(fnName)) {
        allValid = false;
      }
    }

    return allValid;
  }

  /**
   * 默认的违规处理程序
   */
  defaultViolationHandler(type, details) {
    console.error(`[IntegrityViolation] 检测到完整性违规: ${type}`, details);

    // 默认行为：终止进程
    process.exit(1);
  }

  /**
   * 开始定期检查
   */
  startMonitoring() {
    // 文件完整性检查定时器
    const fileCheckerTimer = setInterval(() => {
      if (!this.runtimeChecks) return;
      this.verifyAllFiles();
    }, this.interval);

    // 函数完整性检查定时器
    const functionCheckerTimer = setInterval(() => {
      if (!this.runtimeChecks) return;
      this.verifyAllFunctions();
    }, this.interval * 1.3); // 错开时间，避免同时执行

    this.checksumTimers.push(fileCheckerTimer, functionCheckerTimer);

    // 添加随机延迟的检查，使攻击者难以预测
    this.addRandomChecks();

    return this;
  }

  /**
   * 添加随机时间的完整性检查
   */
  addRandomChecks() {
    const scheduleRandomCheck = () => {
      const delay = Math.floor(Math.random() * this.interval * 0.8) + this.interval * 0.2;

      setTimeout(() => {
        if (!this.runtimeChecks) return;

        // 随机选择执行文件检查或函数检查
        if (Math.random() > 0.5) {
          this.verifyAllFiles();
        } else {
          this.verifyAllFunctions();
        }

        // 重新安排下一次随机检查
        scheduleRandomCheck();
      }, delay);
    };

    scheduleRandomCheck();
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    this.checksumTimers.forEach(timer => clearInterval(timer));
    this.checksumTimers = [];
    this.runtimeChecks = false;
  }

  /**
   * 执行时间分析 - 检测调试器
   */
  timeAnalysis(fn, name, expectedMaxTime) {
    return (...args) => {
      const start = Date.now();
      const result = fn(...args);
      const executionTime = Date.now() - start;

      // 保存这个函数的平均执行时间
      if (!this.lastExecutionTime[name]) {
        this.lastExecutionTime[name] = executionTime;
      } else {
        // 平滑平均值，给予新值30%的权重
        this.lastExecutionTime[name] = this.lastExecutionTime[name] * 0.7 + executionTime * 0.3;
      }

      // 如果执行时间异常（超过预期或历史平均值的5倍），可能存在调试器
      const threshold = expectedMaxTime || (this.lastExecutionTime[name] * 5);
      if (executionTime > threshold) {
        this.onViolation('timing_anomaly', {
          function: name,
          executionTime,
          averageTime: this.lastExecutionTime[name],
          threshold
        });
      }

      return result;
    };
  }
}

// 导出整个类和一个工厂函数以方便使用
module.exports = {
  IntegrityChecker,
  createChecker: (options) => new IntegrityChecker(options)
};