'use strict';

const v8 = require('v8');
const os = require('os');
const vm = require('vm');
const crypto = require('crypto');
const child_process = require('child_process');

/**
 * 反调试与动态混淆系统
 * 提供多层次的反调试检测机制和动态代码转换
 */
class AntiDebugger {
    constructor(options = {}) {
        this.isActive = true;
        this.options = {
            checkInterval: options.checkInterval || 2000,
            performanceThreshold: options.performanceThreshold || 2.5,
            memoryCheckEnabled: options.memoryCheckEnabled !== false,
            systemCheckEnabled: options.systemCheckEnabled !== false,
            childProcessCheckEnabled: options.childProcessCheckEnabled !== false,
            onDetection: options.onDetection || this.defaultDetectionHandler,
            debugStringDetection: options.debugStringDetection !== false,
            dynamicCodeEnabled: options.dynamicCodeEnabled !== false,
            codeCheckInterval: options.codeCheckInterval || 10000
        };

        this.detectionCounter = 0;
        this.lastCheckTime = Date.now();
        this.checkTimers = [];
        this.codeTransformations = new Map();

        // 存储要混淆的函数
        this.protectedFunctions = new Map();

        // 初始化一些随机的干扰变量
        this._initNoiseVariables();
    }

    /**
     * 初始化随机干扰变量
     * 这些变量没有实际用途，只是为了干扰静态分析
     */
    _initNoiseVariables() {
        this._debugID = crypto.randomBytes(16).toString('hex');
        this._detectionSeq = [];
        for (let i = 0; i < 6; i++) {
            this._detectionSeq.push(crypto.randomBytes(4).readUInt32BE(0));
        }
        this._transformQueue = [];
        this._lastTransformIdx = 0;
    }

    /**
     * 默认的调试检测处理程序
     */
    defaultDetectionHandler(type, details) {
        console.error(`[安全警告] 检测到可能的调试尝试: ${type}`);

        this.detectionCounter++;

        // 如果检测到多次调试尝试，终止进程
        if (this.detectionCounter >= 3) {
            console.error('[安全警告] 检测到持续的调试尝试，终止进程');
            process.exit(1);
        }

        return this.detectionCounter;
    }

    /**
     * 检测是否存在调试器
     */
    isDebuggerAttached() {
        return process._debugProcess || process._debugEnd || process._debugPause;
    }

    /**
     * 执行时间分析检测
     */
    performTimeAnalysis() {
        if (!this.isActive) return false;

        const startTime = Date.now();

        // 执行一些简单但耗时可预测的操作
        let total = 0;
        for (let i = 0; i < 50000; i++) {
            total += Math.sin(i * 0.01) * Math.cos(i * 0.01);
        }

        const duration = Date.now() - startTime;
        const expectedTime = 5; // 预期在普通运行环境中大约需要5ms

        // 如果执行时间超过预期的数倍，可能存在调试器
        if (duration > expectedTime * this.options.performanceThreshold) {
            this.options.onDetection('time_analysis', {
                duration,
                expected: expectedTime,
                threshold: expectedTime * this.options.performanceThreshold,
                result: total // 防止优化
            });
            return true;
        }

        return false;
    }

    /**
     * 检测进程参数是否包含调试相关标志
     */
    checkProcessArguments() {
        if (!this.isActive) return false;

        const debugFlags = [
            '--inspect', '--inspect-brk', '--debug', '--debug-brk',
            '--inspect-port', '--debug-port'
        ];

        for (const arg of process.execArgv.concat(process.argv)) {
            for (const flag of debugFlags) {
                if (arg.includes(flag)) {
                    this.options.onDetection('debug_flags', { flag, arg });
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检测Node.js环境变量是否包含调试相关设置
     */
    checkEnvironmentVariables() {
        if (!this.isActive) return false;

        const debugEnvVars = [
            'NODE_OPTIONS', 'NODE_INSPECTOR', 'NODE_DEBUG', 'DEBUG', 'NODE_ENV'
        ];

        for (const envVar of debugEnvVars) {
            const value = process.env[envVar];
            if (value && (
                value.includes('inspect') ||
                value.includes('debug') ||
                value.includes('development')
            )) {
                this.options.onDetection('debug_env', { variable: envVar, value });
                return true;
            }
        }

        return false;
    }

    /**
     * 检测内存使用情况异常
     */
    checkMemoryUsage() {
        if (!this.isActive || !this.options.memoryCheckEnabled) return false;

        const memoryUsage = process.memoryUsage();

        // 如果RSS（常驻集大小）异常大，可能存在调试器或分析工具
        if (memoryUsage.rss > 1024 * 1024 * 300) { // 超过300MB
            this.options.onDetection('high_memory', {
                rss: memoryUsage.rss,
                heapTotal: memoryUsage.heapTotal,
                heapUsed: memoryUsage.heapUsed
            });
            return true;
        }

        return false;
    }

    /**
     * 检测系统负载异常
     */
    checkSystemLoad() {
        if (!this.isActive || !this.options.systemCheckEnabled) return false;

        // 获取系统CPU负载
        const loadAvg = os.loadavg();
        // 获取CPU数量以计算平均负载
        const cpus = os.cpus().length;

        // 如果单个CPU的负载过高，可能有分析工具在运行
        if (loadAvg[0] / cpus > 0.8) { // 80%负载
            this.options.onDetection('high_cpu_load', {
                loadAvg: loadAvg[0],
                cpuCount: cpus,
                perCpuLoad: loadAvg[0] / cpus
            });
            return true;
        }

        return false;
    }

    /**
     * 使用子进程检测调试器
     */
    checkUsingChildProcess() {
        if (!this.isActive || !this.options.childProcessCheckEnabled) return false;

        try {
            // 获取当前进程的PID
            const pid = process.pid;

            // 根据操作系统确定检查命令
            let command, args;
            if (process.platform === 'win32') {
                command = 'tasklist';
                args = ['/FI', `PID eq ${pid}`, '/v'];
            } else {
                command = 'ps';
                args = ['-p', pid, '-o', 'command'];
            }

            // 执行命令并获取输出
            const output = child_process.spawnSync(command, args, {
                encoding: 'utf8'
            }).stdout;

            // 检查输出是否包含调试相关的字符串
            if (output && (
                output.includes('inspect') ||
                output.includes('debug') ||
                output.includes('--inspect-brk') ||
                output.includes('chrome-devtools')
            )) {
                this.options.onDetection('debug_process', { output });
                return true;
            }
        } catch (err) {
            // 忽略错误
        }

        return false;
    }

    /**
     * 检测可能的调试字符串，防止函数被重命名
     */
    checkDebugStrings() {
        if (!this.isActive || !this.options.debugStringDetection) return false;

        // 转换Error.stackTraceLimit让它变得更难分析
        const originalLimit = Error.stackTraceLimit;
        Error.stackTraceLimit = 50;

        try {
            // 生成错误以获取堆栈跟踪
            const error = new Error('StackCheck');
            const stack = error.stack || '';

            // 检查堆栈中是否存在调试相关字符串
            const suspiciousStrings = [
                'chrome-devtools://', 'devtools://', 'debugger',
                'DebuggerStatement', 'debug eval', 'function.debugger',
                'REPL', 'Debug.', 'ndb', 'node-inspector'
            ];

            for (const str of suspiciousStrings) {
                if (stack.includes(str)) {
                    this.options.onDetection('debug_string', {
                        found: str,
                        stackSample: stack.substring(0, 200) // 只显示部分堆栈
                    });
                    return true;
                }
            }
        } finally {
            // 恢复原始设置
            Error.stackTraceLimit = originalLimit;
        }

        return false;
    }

    /**
     * 执行所有检查
     */
    runAllChecks() {
        if (!this.isActive) return false;

        // 添加随机延迟，避免可预测性
        const delay = Math.floor(Math.random() * 500);

        setTimeout(() => {
            // 基本检查
            this.isDebuggerAttached() && this.options.onDetection('debugger_attached');
            this.checkProcessArguments();
            this.checkEnvironmentVariables();

            // 随机选择其他检查运行
            if (Math.random() > 0.3) this.performTimeAnalysis();
            if (Math.random() > 0.3) this.checkMemoryUsage();
            if (Math.random() > 0.5) this.checkSystemLoad();
            if (Math.random() > 0.6) this.checkUsingChildProcess();
            if (Math.random() > 0.4) this.checkDebugStrings();

            // 检查两次检查之间的时间间隔
            const currentTime = Date.now();
            const timeSinceLastCheck = currentTime - this.lastCheckTime;

            // 如果间隔异常长，可能有人暂停了调试器
            if (timeSinceLastCheck > this.options.checkInterval * 3) {
                this.options.onDetection('check_delay', {
                    delay: timeSinceLastCheck,
                    expected: this.options.checkInterval
                });
            }

            this.lastCheckTime = currentTime;
        }, delay);

        return true;
    }

    /**
     * 启动定期检查
     */
    startMonitoring() {
        if (!this.isActive) return this;

        // 立即运行一次检查
        this.runAllChecks();

        // 定期检查计时器
        const checkTimer = setInterval(() => {
            this.runAllChecks();
        }, this.options.checkInterval);

        // 如果启用了代码混淆，启动定期代码变换
        if (this.options.dynamicCodeEnabled) {
            const codeTimer = setInterval(() => {
                this.transformAllProtectedFunctions();
            }, this.options.codeCheckInterval);

            this.checkTimers.push(codeTimer);
        }

        this.checkTimers.push(checkTimer);

        return this;
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        this.checkTimers.forEach(timer => clearInterval(timer));
        this.checkTimers = [];
        this.isActive = false;

        return this;
    }

    /**
     * 保护函数并创建动态变形版本
     * @param {string} name 函数名称
     * @param {Function} fn 要保护的函数
     */
    protectFunction(name, fn) {
        if (typeof fn !== 'function') {
            throw new Error('必须提供一个有效的函数');
        }

        // 存储原始函数
        this.protectedFunctions.set(name, {
            original: fn,
            transformed: null,
            transformations: 0
        });

        // 创建初始变形
        this._transformFunction(name);

        // 返回包装器函数
        return (...args) => {
            // 使用当前的变形版本
            const protectedFn = this.protectedFunctions.get(name);
            return (protectedFn.transformed || protectedFn.original).apply(this, args);
        };
    }

    /**
     * 变换单个受保护的函数
     * @param {string} name 函数名称
     */
    _transformFunction(name) {
        if (!this.options.dynamicCodeEnabled || !this.protectedFunctions.has(name)) {
            return false;
        }

        const fnData = this.protectedFunctions.get(name);
        const originalFn = fnData.original;
        const fnString = originalFn.toString();

        try {
            // 选择随机变换类型
            const transformType = Math.floor(Math.random() * 4);
            let transformedCode;

            switch (transformType) {
                case 0: // 添加无用变量和操作
                    transformedCode = this._addNoiseToFunction(fnString);
                    break;
                case 1: // 函数拆分和重新组合
                    transformedCode = this._splitAndReassembleFunction(fnString);
                    break;
                case 2: // 控制流扁平化
                    transformedCode = this._flattenControlFlow(fnString);
                    break;
                case 3: // 字符串和常量加密
                    transformedCode = this._encryptConstants(fnString);
                    break;
                default:
                    transformedCode = fnString;
            }

            // 将变换后的代码编译为新函数
            const transformedFn = this._compileTransformedFunction(transformedCode, name);

            if (transformedFn) {
                fnData.transformed = transformedFn;
                fnData.transformations++;
                this.protectedFunctions.set(name, fnData);
                return true;
            }
        } catch (err) {
            // 如果变换失败，静默回退到原始函数
            console.error(`[变换错误] 函数 ${name} 变换失败:`, err);
            fnData.transformed = originalFn;
            this.protectedFunctions.set(name, fnData);
        }

        return false;
    }

    /**
     * 变换所有受保护的函数
     */
    transformAllProtectedFunctions() {
        if (!this.options.dynamicCodeEnabled) return false;

        for (const name of this.protectedFunctions.keys()) {
            this._transformFunction(name);
        }

        return true;
    }

    /**
     * 向函数添加噪音代码
     */
    _addNoiseToFunction(fnString) {
        // 函数主体部分的开始和结束位置
        const bodyStart = fnString.indexOf('{') + 1;
        const bodyEnd = fnString.lastIndexOf('}');

        if (bodyStart > 0 && bodyEnd > bodyStart) {
            const header = fnString.substring(0, bodyStart);
            const body = fnString.substring(bodyStart, bodyEnd);
            const footer = fnString.substring(bodyEnd);

            // 创建随机变量名
            const varName = '_v' + Math.floor(Math.random() * 10000);

            // 添加无用的变量和操作
            const noise = `
        const ${varName} = ${Math.random() * 100};
        try {
          if (${varName} < 0) {
            console.log("不会执行到这里");
          }
        } catch(e) {}
      `;

            return header + noise + body + footer;
        }

        return fnString;
    }

    /**
     * 将函数拆分后重新组合
     */
    _splitAndReassembleFunction(fnString) {
        // 识别函数名和参数部分
        const funcMatch = fnString.match(/function\s*([^(]*)\(([^)]*)\)/);
        if (!funcMatch) return fnString; // 无法识别函数结构

        const params = funcMatch[2].split(',').map(p => p.trim());

        // 函数主体
        const bodyStart = fnString.indexOf('{') + 1;
        const bodyEnd = fnString.lastIndexOf('}');
        const body = fnString.substring(bodyStart, bodyEnd);

        // 创建辅助函数名
        const helperName = '_h' + Math.floor(Math.random() * 10000);

        // 创建主函数和辅助函数
        return `
      function ${helperName}(${params.join(', ')}) {
        ${body}
      }

      function ${funcMatch[1] || ''}(${params.join(', ')}) {
        return ${helperName}.call(this, ${params.join(', ')});
      }
    `;
    }

    /**
     * 扁平化控制流
     */
    _flattenControlFlow(fnString) {
        // 简化版控制流扁平化 - 只适用于简单函数
        // 真实场景需要使用AST解析和转换

        // 识别函数名和参数部分
        const funcMatch = fnString.match(/function\s*([^(]*)\(([^)]*)\)/);
        if (!funcMatch) return fnString; // 无法识别函数结构

        const funcName = funcMatch[1] || '';
        const params = funcMatch[2];

        // 函数主体
        const bodyStart = fnString.indexOf('{') + 1;
        const bodyEnd = fnString.lastIndexOf('}');
        const body = fnString.substring(bodyStart, bodyEnd);

        // 创建随机变量名
        const stateVar = '_s' + Math.floor(Math.random() * 10000);
        const resultVar = '_r' + Math.floor(Math.random() * 10000);

        // 创建包含switch语句的扁平化代码(简化版)
        return `
      function ${funcName}(${params}) {
        let ${stateVar} = 0;
        let ${resultVar};

        while (${stateVar} !== -1) {
          switch (${stateVar}) {
            case 0:
              ${stateVar} = 1;
              break;
            case 1:
              ${body.replace(/return\s+([^;]+);/g, `${resultVar} = $1; ${stateVar} = -1; break;`)}
              ${stateVar} = -1;
              break;
            default:
              ${stateVar} = -1;
          }
        }

        return ${resultVar};
      }
    `;
    }

    /**
     * 加密函数中的常量
     */
    _encryptConstants(fnString) {
        // 简单的字符串常量加密
        return fnString.replace(/'([^']+)'/g, (match, str) => {
            // 将字符串转换为base64
            const encoded = Buffer.from(str).toString('base64');
            return `Buffer.from('${encoded}', 'base64').toString()`;
        }).replace(/"([^"]+)"/g, (match, str) => {
            // 将字符串转换为base64
            const encoded = Buffer.from(str).toString('base64');
            return `Buffer.from("${encoded}", 'base64').toString()`;
        });
    }

    /**
     * 将变换后的代码编译为函数
     */
    _compileTransformedFunction(code, name) {
        try {
            // 使用 vm 模块在隔离上下文中编译代码
            const context = vm.createContext({
                Buffer,
                console,
                setTimeout,
                clearTimeout,
                setInterval,
                clearInterval,
                process
            });

            // 添加一个唯一的函数名
            const uniqueFnName = `_fn_${Date.now()}_${Math.floor(Math.random() * 1000000)}`;

            // 在上下文中执行代码，并返回编译后的函数
            vm.runInContext(`${uniqueFnName} = ${code}`, context);

            // 从上下文中获取函数
            return context[uniqueFnName];
        } catch (err) {
            console.error(`[编译错误] 无法编译变换后的函数 ${name}:`, err);
            return null;
        }
    }
}

// 导出类和工厂函数
module.exports = {
    AntiDebugger,
    createAntiDebugger: (options) => new AntiDebugger(options)
};
