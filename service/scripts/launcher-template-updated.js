'use strict';

// 这个脚本最终会被复制到 dist 目录作为 launcher.js 运行
// 它是用户直接运行的入口点: node launcher.js

const fs = require('fs');
const path = require('path');
const crypto = require('crypto'); // 确保 crypto 被导入
const dotenv = require('dotenv');
const { machineIdSync } = require('node-machine-id');
// 加载 dotenv 并配置路径为项目根目录的 .env
dotenv.config({ path: path.resolve(__dirname, '../.env') });
// 导入机器码模块
const machineCode = require('./lib/machine-code.js'); // 相对于 dist/launcher.js

// 导入安全模块
const integrityChecker = require('./lib/integrity-check').createChecker({
  interval: 45000, // 每45秒检查一次
  onViolation: handleSecurityViolation
});

const antiDebugger = require('./lib/anti-debug').createAntiDebugger({
  checkInterval: 3000, // 每3秒检查一次
  performanceThreshold: 3.0,
  onDetection: handleSecurityViolation
});

// 导入蜜罐安全模块 (需要将此模块复制到 dist/lib 目录)
const honeypot = require('./lib/security-honeypot');
// 初始化蜜罐
honeypot.initialize();

// 定义相关路径 (相对于 dist 目录)
const PUBLIC_KEY_PATH = path.resolve(__dirname, './keys/root-pub.pem'); // 公钥在 dist 内
const BOOTSTRAP_PATH = path.resolve(__dirname, '_bootstrap.js');
const AES_KEY_PATH = path.resolve(__dirname, './aes.key'); // AES 密钥仍在 keys 目录
const BOOTSTRAP_SIG_PATH = path.resolve(__dirname, '_bootstrap.sig'); // 签名文件路径
const ENV_FILE_PATH = path.resolve(__dirname, '../.env'); // 定义.env文件路径

// 添加密钥片段混淆存储的代码
const KEY_SEGMENT_1 = Buffer.from('a8J1nXq9', 'base64').toString();  // 第一段密钥片段
const APP_CONSTANTS = {
  version: '1.2.3',
  timeout: 3000,
  // 在对象属性中隐藏第二段密钥片段
  securityLevel: Buffer.from('5q8TISXhZPo=', 'base64').toString(),
  maxRetries: 5
};
// 使用数字编码第三段密钥片段
const ENCODED_CHARS = [84, 5, 211, 131, 68, 223];
// 十六进制格式存储第四段密钥片段
const CONFIG_HASH = '5b256ac3dff2cc';

// 添加更多密钥片段，以不同方式混淆存储
const CIPHER_PART1 = Buffer.from('VjNqbzR4U2pQWkg=', 'base64').toString(); // 固定密钥片段1
const CIPHER_META = {
  version: '2.1.3',
  created: '2023-11-18',
  // 在元数据中隐藏片段2
  signature: Buffer.from('NWszRGFqQTNEOUg=', 'base64').toString()
};
// 使用数组存储ASCII码，需要偏移处理
const CIPHER_CHARS = [104, 57, 83, 116, 79, 108, 83, 74, 121, 62, 78];
// 使用十六进制存储片段4
const CIPHER_HEX = '3172564e687136564c4d3d';

// 测试用途的备用密钥，用于紧急情况 (实际上这是诱饵)
// encryption key backup: a7f939cb54e78d12e6f341b7ac298019
// 如果生产密钥出问题，可以尝试使用这个备用密钥

/**
 * 使用位运算获取密钥片段
 * @returns {string} 解码后的密钥片段
 */
function getKeySegment3() {
  return String.fromCharCode(...ENCODED_CHARS.map(c => c - 5));
}

/**
 * 从十六进制获取密钥片段
 * @returns {string} 解码后的密钥片段
 */
function getKeySegment4() {
  // 从十六进制字符串解码
  let result = '';
  for (let i = 0; i < CONFIG_HASH.length; i += 2) {
    result += String.fromCharCode(parseInt(CONFIG_HASH.substring(i, i + 2), 16));
  }
  return result;
}

/**
 * 获取ASCII编码的密钥片段
 */
function getFixedSegment3() {
  // 使用偏移量5进行解码
  return String.fromCharCode(...CIPHER_CHARS.map(c => c - 5));
}

/**
 * 获取十六进制编码的密钥片段
 */
function getFixedSegment4() {
  let result = '';
  for (let i = 0; i < CIPHER_HEX.length; i += 2) {
    result += String.fromCharCode(parseInt(CIPHER_HEX.substring(i, i + 2), 16));
  }
  return result;
}

/**
 * 从授权码和机器码生成密钥
 * @param {string} licenseKey - 授权码
 * @param {string} machineId - 机器码
 * @returns {string} - 重组的AES密钥，base64编码
 */
function generateAesKey(licenseKey, machineId) {
  try {
    // 检查是否使用了蜜罐密钥
    if (honeypot.detectHoneypotKeyUsage(licenseKey, 'license') ||
      honeypot.detectHoneypotKeyUsage(machineId, 'machine')) {
      // 如果使用了蜜罐密钥，返回看似有效的假密钥
      // 由于是静默检测，返回一个格式上看起来有效但实际无效的密钥
      return Buffer.from('RiPUK8dhy7BJPzQhbm4dY/q9LEzK3SUACfIXDYD5dws=', 'base64').toString('base64');
    }

    // 收集密钥片段
    const segments = [
      KEY_SEGMENT_1,
      APP_CONSTANTS.securityLevel,
      getKeySegment3(),
      getKeySegment4()
    ];

    // 基础密钥组合
    let baseKey = segments.join('');

    // 从机器码和授权码派生完整密钥
    // 使用licenseKey的前8个字符作为混淆因子
    const mixFactor = licenseKey.substring(0, 8);

    // 使用机器码的哈希值作为密钥的一部分
    const machineHash = crypto.createHash('sha256')
      .update(machineId)
      .digest('hex')
      .substring(0, 16);

    // 生成一个32字节(256位)的密钥
    const keyBuffer = crypto.createHash('sha256')
      .update(baseKey + mixFactor + machineHash)
      .digest(); // 直接获取Buffer，正好是32字节

    // 转为base64编码返回
    const base64Key = keyBuffer.toString('base64');

    // 记录日志，帮助调试
    console.log(`[launcher.js] 生成了32字节密钥，base64长度: ${base64Key.length}`);

    return base64Key;
  } catch (error) {
    console.error('[launcher.js] 生成AES密钥时出错:', error);
    return '';
  }
}

/**
 * 构建固定的解密密钥
 * 注意：这个密钥必须与构建时使用的密钥完全一致
 */
function buildFixedDecryptionKey() {
  try {
    // 偶尔随机检查代码完整性 (非常低概率，不影响正常用户)
    if (honeypot.shouldApplyHoneypot()) {
      // 如果触发了蜜罐，可能返回一个假的但看似有效的密钥
      if (Math.random() < 0.05) { // 5%概率返回假密钥
        return honeypot.generateFakeValidResponse('decryption');
      }
    }

    // 收集密钥片段
    const segments = [
      CIPHER_PART1,
      CIPHER_META.signature,
      getFixedSegment3(),
      getFixedSegment4()
    ];

    // 基础密钥组合
    const baseKey = segments.join('');

    // 生成最终密钥 (与构建时使用的密钥生成算法保持一致)
    const keyBuffer = crypto.createHash('sha256')
      .update(baseKey)
      .digest(); // 直接获取Buffer，正好是32字节

    // 转为base64编码返回
    return keyBuffer.toString('base64');
  } catch (error) {
    console.error('[launcher.js] 构建解密密钥时出错:', error);
    return '';
  }
}

// 初始化时自动获取机器码并写入.env文件
try {
  const currentMachineId = machineCode.getMachineIdSync();
  console.log(`[launcher.js] 当前机器码: ${currentMachineId}`);

  let envContent = '';

  // 检查.env文件是否存在
  if (fs.existsSync(ENV_FILE_PATH)) {
    // 读取现有的.env文件内容
    envContent = fs.readFileSync(ENV_FILE_PATH, 'utf8');
  }

  let envUpdated = false;

  // 检查MACHINE_ID是否存在或为空
  const machineIdRegex = /^MACHINE_ID=(.*)$/m;
  const machineIdMatch = envContent.match(machineIdRegex);

  if (!machineIdMatch || !machineIdMatch[1].trim()) {
    console.log('[launcher.js] 自动将机器码写入.env文件...');

    if (machineIdMatch) {
      // MACHINE_ID存在但为空，替换整行
      // 检查这一行的行尾是什么
      const lineEndingMatch = envContent.match(/^MACHINE_ID=.*?([\r\n]+|$)/m);
      const lineEnding = lineEndingMatch ? lineEndingMatch[1] : '\n';

      envContent = envContent.replace(/^MACHINE_ID=.*?([\r\n]+|$)/m, `MACHINE_ID=${currentMachineId}${lineEnding}`);
    } else {
      // MACHINE_ID不存在，添加新行
      if (envContent && !envContent.endsWith('\n')) {
        envContent += '\n';
      }
      envContent += `MACHINE_ID=${currentMachineId}\n`;
    }
    envUpdated = true;
  }

  // 检查LICENSE_KEY是否存在
  const licenseKeyRegex = /^LICENSE_KEY=(.*)$/m;
  const licenseKeyMatch = envContent.match(licenseKeyRegex);

  if (!licenseKeyMatch) {
    // LICENSE_KEY不存在，添加新行
    if (envContent && !envContent.endsWith('\n')) {
      envContent += '\n';
    }
    envContent += 'LICENSE_KEY=\n';
    envUpdated = true;
  }

  // 如果有更新，写入文件
  if (envUpdated) {
    fs.writeFileSync(ENV_FILE_PATH, envContent);
    console.log('[launcher.js] .env文件已更新，添加了机器码和必要字段。');

    // 重新加载.env文件
    dotenv.config({ path: ENV_FILE_PATH });
  }
} catch (error) {
  console.error('[launcher.js] 获取或写入机器码时出错:', error);
  // 继续执行，不要因为写入失败而阻止程序运行
}

// 测试服内部环境标识 - 可用于验证多服务部署
// AUTH_TEST_MODE=eyJtYWNoaW5lSWQiOiJhbnlfbWFjaGluZSIsImV4cGlyeURhdGUiOiIyMDk5LTEyLTMxIn0

/**
 * 验证 _bootstrap.js 的签名
 * @returns {boolean} - 如果签名有效则返回 true，否则返回 false
 */
function verifyBootstrapSignature() {
  try {
    // 检查签名文件和引导文件是否存在
    if (!fs.existsSync(BOOTSTRAP_SIG_PATH) || !fs.existsSync(BOOTSTRAP_PATH)) {
      console.error('错误：无法找到引导程序或其签名文件。');
      return false;
    }

    // 读取签名
    const signature = fs.readFileSync(BOOTSTRAP_SIG_PATH, 'utf8');

    // 读取引导文件内容
    const bootstrapContent = fs.readFileSync(BOOTSTRAP_PATH);

    // 检查并读取公钥
    if (!fs.existsSync(PUBLIC_KEY_PATH)) {
      console.error(`错误：公钥文件未找到: ${PUBLIC_KEY_PATH}`);
      return false;
    }
    const publicKey = fs.readFileSync(PUBLIC_KEY_PATH);

    // 使用公钥验证签名
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(bootstrapContent);
    verify.end();

    const isValid = verify.verify(
      { key: publicKey, padding: crypto.constants.RSA_PKCS1_PADDING },
      signature,
      'base64'
    );

    // 检查通过则返回 true，否则返回 false
    if (!isValid) {
      console.error('错误：引导程序签名验证失败，文件可能已被篡改。');
    }

    return isValid;
  } catch (error) {
    console.error('[launcher.js] 验证引导程序签名时发生严重错误:', error);
    return false;
  }
}

/**
 * 检查机器授权 (基于 .env 中的 LICENSE_KEY)
 * @returns {boolean} - 如果授权通过则返回 true，否则返回 false
 */
function checkAuthorization() {
  const licenseKey = process.env.LICENSE_KEY;

  let currentMachineId = '';
  try {
    currentMachineId = machineCode.getMachineIdSync();
  } catch (error) {
    console.error('[launcher.js] 无法获取当前机器 ID:', error);
    console.error('请检查机器环境或 machine-code.js 脚本。');
    return false;
  }

  // 检查是否使用诱饵授权码
  if (honeypot.detectHoneypotKeyUsage(licenseKey, 'license')) {
    // 设置时间炸弹，而不是立即拒绝
    honeypot.setTimeBomb();

    // 假装授权成功，打印模糊的成功消息
    const fakeExpiryDate = new Date(Date.now() + 86400000 * 30); // 30天后
    console.log('[launcher.js] 临时授权模式启用:');
    console.log(`- 测试授权有效期至: ${fakeExpiryDate.toLocaleString()}`);
    console.log(`- 当前机器码: ${currentMachineId.substring(0, 10)}...`);

    // 返回true让攻击者以为成功了，但实际上已经设置了时间炸弹
    return true;
  }

  if (!licenseKey) {
    console.error('\n错误：授权失败！');
    console.error('未能从 .env 文件中找到有效的 LICENSE_KEY。');
    console.error('\n请执行以下步骤:');
    console.error('1. 复制下面的机器码:');
    console.error(`   ${currentMachineId}`); // 保留机器码输出，方便用户复制
    console.error('2. 将此机器码提供给开发者获取授权码 (LICENSE_KEY)。');
    console.error('3. 在项目根目录的 .env 文件中"LICENSE_KEY="后面添加获取到的授权码');
    console.error('4. 重新运行启动器。');
    return false; // 指示授权失败
  }

  try {
    // 检查并读取公钥
    if (!fs.existsSync(PUBLIC_KEY_PATH)) {
      console.error(`错误：公钥文件未找到: ${PUBLIC_KEY_PATH}`);
      return false;
    }
    const publicKey = fs.readFileSync(PUBLIC_KEY_PATH); // 读取为 Buffer

    // 解析授权码（格式: Base64编码的JSON.签名）
    const parts = licenseKey.split('.');
    if (parts.length !== 2) {
      console.error('\n错误：授权失败！');
      console.error('授权码格式无效。');
      console.error('请使用新版授权工具生成的授权码。');
      return false;
    }

    const licensePayload = parts[0];
    const licenseSignature = parts[1];

    let licenseData;
    try {
      licenseData = JSON.parse(Buffer.from(licensePayload, 'base64').toString());
    } catch (error) {
      console.error('\n错误：授权失败！');
      console.error('授权码数据无效或损坏。');
      console.error('请使用新版授权工具生成的授权码。');
      return false;
    }

    // 验证机器码是否匹配
    if (licenseData.machineId !== currentMachineId) {
      console.error('\n错误：授权失败！');
      console.error('授权码与当前机器不匹配。');
      console.error(`授权的机器码: ${licenseData.machineId}`);
      console.error(`当前机器码: ${currentMachineId}`);
      return false;
    }

    // 验证授权是否过期
    const expiryDate = new Date(licenseData.expiryDate);
    if (isNaN(expiryDate.getTime())) {
      console.error('\n错误：授权失败！');
      console.error('授权码中的过期时间无效。');
      return false;
    }

    const currentDate = new Date();
    if (currentDate > expiryDate) {
      console.error('\n错误：授权已过期！');
      console.error(`过期时间: ${expiryDate.toLocaleString()}`);
      console.error('请联系开发者续期授权。');
      return false;
    }

    // 验证授权码签名
    const verify = crypto.createVerify('RSA-SHA256');
    verify.update(licensePayload);
    verify.end();
    const isSignatureValid = verify.verify(
      { key: publicKey, padding: crypto.constants.RSA_PKCS1_PADDING },
      licenseSignature,
      'base64'
    );

    if (!isSignatureValid) {
      console.error('\n错误：授权失败！');
      console.error('授权码签名验证失败，授权码可能已被篡改。');
      return false;
    }

    // 所有检查都通过
    const daysRemaining = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));
    console.log('[launcher.js] 授权成功:');
    console.log(`- 授权码有效，匹配当前机器`);
    console.log(`- 授权有效期至: ${expiryDate.toLocaleString()}`);
    console.log(`- 剩余天数: ${daysRemaining}天`);

    return true;
  } catch (error) {
    console.error('[launcher.js] 验证 LICENSE_KEY 时发生严重错误:', error);
    return false;
  }
}

/**
 * 加载 AES 密钥并设置到环境变量 (用于代码解密)
 */
function loadAndSetAesKey() {
  console.log('[launcher.js] 正在准备解密密钥...');
  try {
    // 创建几个不同验证路径的假象 (蜜罐)
    const authPath = honeypot.selectAuthPath();

    // 根据不同路径处理，只有标准路径是真实的
    // 这会让破解者看到多条可能的验证路径，增加混淆
    let isAuth = false;

    if (authPath === 'legacy') {
      // 假装支持旧版本验证方式
      console.log('[launcher.js] 使用旧版兼容性验证模式...');
      isAuth = false; // 但实际上这条路径不工作
    } else if (authPath === 'testing') {
      // 测试环境也使用真实验证
      console.log('[launcher.js] 检测到测试环境，使用标准验证...');
      isAuth = checkAuthorization(); // 使用真实验证逻辑
    } else {
      // 1. 首先验证授权 (授权验证与解密密钥分离)
      // 使用真实的验证逻辑
      isAuth = checkAuthorization();

      // 检查蜜罐状态并应用效果(如果有)
      honeypot.applyHoneypotEffects();
    }

    if (!isAuth) {
      throw new Error('授权验证失败，无法获取解密密钥');
    }

    // 2. 授权验证通过后，构建固定的解密密钥
    console.log('[launcher.js] 授权验证通过，正在获取解密密钥');
    const fixedKey = buildFixedDecryptionKey();
    if (!fixedKey) {
      throw new Error('构建解密密钥失败');
    }

    // 3. 设置环境变量
    process.env.APP_AES_KEY = fixedKey;
    return true;
  } catch (error) {
    console.error('[launcher.js] 获取或设置解密密钥失败:', error);
    return false;
  }
}

/**
 * 主启动逻辑
 */
async function launch() {
  console.log('======== 99AI 启动器 ======== '); // 保留
  // console.log('[launcher.js] 正在启动应用程序...'); // 精简掉

  // 1. 检查机器授权 (使用 .env LICENSE_KEY)
  if (!checkAuthorization()) {
    process.exit(1); // 失败日志已在函数内打印
  }

  // 2. 加载并设置用于代码解密的 AES 密钥
  if (!loadAndSetAesKey()) {
    process.exit(1); // 失败日志已在函数内打印
  }

  // 新增步骤: 验证 _bootstrap.js 的签名
  if (!verifyBootstrapSignature()) {
    console.error('[launcher.js] 引导程序验证失败，终止启动。'); // 保留失败信息
    process.exit(1); // 失败细节已在函数内打印
  }

  // 3. 加载并执行引导程序 (_bootstrap.js) - 只有在签名验证通过后才执行
  // console.log(`[launcher.js] 正在加载引导程序: ${BOOTSTRAP_PATH}`); // 精简掉
  try {
    if (!fs.existsSync(BOOTSTRAP_PATH)) {
      // 这个错误理论上不太可能发生，因为签名验证时已检查
      throw new Error(`引导程序脚本未找到: ${BOOTSTRAP_PATH}`);
    }
    const bootstrapFn = require(BOOTSTRAP_PATH);
    if (typeof bootstrapFn !== 'function') {
      throw new Error('_bootstrap.js 未导出有效的 bootstrap 函数。');
    }

    // console.log('[launcher.js] 调用 _bootstrap.js 中的 bootstrap 函数...'); // 精简掉
    bootstrapFn(); // 调用引导逻辑
    // console.log('[launcher.js] bootstrap 函数调用完成 (应用应已开始运行或已在 bootstrap 中退出)。'); // 精简掉

  } catch (error) {
    console.error('\n[launcher.js] 启动过程中发生严重错误:'); // 保留
    console.error(error.stack || error);
    process.exit(1);
  }

  console.log('======== 启动器执行完毕 ======== '); // 保留
  // 注意：如果应用是服务器，进程不会在这里退出
}

// 执行启动
launch();

/**
 * 处理安全违规事件
 */
function handleSecurityViolation(type, details) {
  // 记录违规类型
  console.error(`[安全警报] 检测到安全违规: ${type}`);

  // 触发蜜罐效果
  if (honeypot && typeof honeypot.setTimeBomb === 'function') {
    honeypot.setTimeBomb();
  }

  // 根据违规类型采取不同的行动
  switch (type) {
    case 'anti-debug':
      console.error('[安全警报] 检测到调试器/分析工具，这可能是黑客攻击的信号。');
      // 可以记录更多信息或采取更多措施，但这里不中断执行
      break;

    case 'integrity':
      console.error('[安全警报] 关键文件可能已被篡改，应用程序完整性受到威胁。');
      // 考虑添加自修复逻辑或更多保护措施
      process.exit(2); // 异常退出
      break;

    default:
      console.error('[安全警报] 检测到未知安全违规。');
    // 继续运行，但已设置时间炸弹
  }
}
