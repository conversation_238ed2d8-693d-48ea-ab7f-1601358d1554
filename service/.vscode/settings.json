{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "json", "jsonc", "json5", "yaml", "yml", "markdown"], "cSpell.words": ["aiweb", "ant<PERSON>", "axios", "Baichuan", "bumpp", "Chatbox", "chatglm", "chatgpt", "chatlog", "chenz<PERSON>yu", "chevereto", "cogvideox", "commitlint", "crami", "cref", "dall", "dalle", "<PERSON><PERSON><PERSON>", "deepsearch", "deepseek", "docker<PERSON>b", "Do<PERSON><PERSON>", "duckduck<PERSON>", "<PERSON><PERSON>", "EMAILCODE", "Epay", "<PERSON><PERSON><PERSON>", "errmsg", "esno", "<PERSON><PERSON>", "getticket", "GPTAPI", "gpts", "headimgurl", "highlightjs", "hljs", "hun<PERSON>", "<PERSON><PERSON>", "iconify", "ISDEV", "Jsapi", "katex", "ka<PERSON><PERSON><PERSON><PERSON>", "langchain", "<PERSON><PERSON>", "linkify", "logprobs", "longcontext", "<PERSON><PERSON><PERSON>", "luma", "mapi", "Markmap", "mdhljs", "mediumtext", "micromessenger", "mila", "Mindmap", "modelcontextprotocol", "MODELSMAPLIST", "MODELTYPELIST", "modelvalue", "<PERSON><PERSON><PERSON>", "newconfig", "niji", "Nmessage", "nodata", "OPENAI", "PICREADER", "pinia", "Popconfirm", "PPTCREATE", "projectaddress", "qwen", "rushstack", "sdxl", "<PERSON><PERSON>", "seedream", "<PERSON><PERSON>", "sref", "suno", "tailwindcss", "<PERSON><PERSON>", "traptitech", "tsup", "Typecheck", "typeorm", "unionid", "unplugin", "usercenter", "vastxie", "VITE", "vueuse", "wechat"], "vue.codeActions.enabled": false}