import { Logger } from '@nestjs/common';
import axios from 'axios';
/**
 * 将URL转换为Base64
 * @param url - 需要转换的URL
 * @returns 转换后的Base64字符串
 */
export async function convertUrlToBase64(url: string): Promise<string> {
  try {
    // 添加超时控制和响应大小限制
    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      timeout: 10000, // 10秒超时
      maxContentLength: 50 * 1024 * 1024, // 50MB 限制
    });

    const buffer = Buffer.from(response.data, 'binary');
    const contentType = response.headers['content-type'] || 'application/octet-stream';
    const base64Data = `data:${contentType};base64,${buffer.toString('base64')}`;

    Logger.debug(
      `URL转Base64成功: ${url.substring(0, 50)}..., 大小: ${buffer.length}字节`,
      'ConvertUrlToBase64',
    );
    return base64Data;
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      Logger.error(`URL转换超时: ${url}`, 'ConvertUrlToBase64');
    } else if (error.response?.status) {
      Logger.error(`URL转换失败 (${error.response.status}): ${url}`, 'ConvertUrlToBase64');
    } else {
      Logger.error(`URL转换失败: ${url}, 错误: ${error.message}`, 'ConvertUrlToBase64');
    }
    return url; // 失败时返回原始URL
  }
}
