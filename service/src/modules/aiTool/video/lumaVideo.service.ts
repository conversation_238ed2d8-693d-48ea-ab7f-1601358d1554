import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosResponse } from 'axios';
import { ChatLogService } from '../../chatLog/chatLog.service';
import { GlobalConfigService } from '../../globalConfig/globalConfig.service';
import { UploadService } from '../../upload/upload.service';
import { OpenAIChatService } from '../chat/chat.service';
// 引入其他需要的模块或服务

@Injectable()
export class LumaVideoService {
  constructor(
    private readonly chatLogService: ChatLogService,
    private readonly globalConfigService: GlobalConfigService,
    private readonly uploadService: UploadService,
    private readonly openAIChatService: OpenAIChatService,
  ) {}

  async lumaVideo(inputs) {
    const {
      apiKey,
      proxyUrl,
      imageUrl,
      prompt,
      timeout,
      assistantLogId,
      extraParam,
      action,
      taskId,
    } = inputs;

    // 内部处理翻译逻辑
    const finalPrompt = await this.translatePrompt(prompt);

    Logger.debug(
      `开始处理视频生成请求，参数：${JSON.stringify({
        prompt: finalPrompt,
        action,
        taskId,
        timeout,
      })}`,
      'LumaVideoService',
    );

    const result: any = {
      content: '',
      videoUrl: '',
      taskId: '',
      taskData: '',
      status: 2,
    };

    if (!apiKey || !proxyUrl) {
      Logger.error('缺少必要参数: apiKey 或 proxyUrl', 'LumaVideoService');
      result.status = 5;
      result.content = '配置参数错误';
      await this.chatLogService.updateChatLog(assistantLogId, {
        content: '配置参数错误',
        status: 5,
      });
      return result;
    }

    /* 提交绘画任务 */
    let response: AxiosResponse<any, any> | null = null;
    let url = '';
    let payloadJson = {};
    const headers = { Authorization: `Bearer ${apiKey}` };

    if (action === 'extend') {
      url = `${proxyUrl}/luma/generations/${taskId}/extend`;
    } else {
      url = `${proxyUrl}/luma/generations`;
    }

    // const aspectRatio = extraParam.size || '16:9';
    payloadJson = {
      user_prompt: finalPrompt,
      // aspect_ratio: aspectRatio,
      expand_prompt: true,
    };

    if (imageUrl) {
      if (action === 'extend') {
        payloadJson['image_url'] = imageUrl;
      } else {
        // 检查是否包含多个链接（用逗号分隔）
        const urls = imageUrl.split(',').map(url => url.trim());
        if (urls.length > 1) {
          // 如果有多个链接，第一个作为 image_url，第二个作为 image_end_url
          payloadJson['image_url'] = urls[0];
          payloadJson['image_end_url'] = urls[1];
        } else {
          // 如果只有一个链接，只设置 image_url
          payloadJson['image_url'] = imageUrl;
        }
      }
    }

    Logger.log(
      `正在准备发送请求到 ${url}，payload: ${JSON.stringify(
        payloadJson,
      )}, headers: ${JSON.stringify(headers)}`,
      'LumaService',
    );

    try {
      response = await axios.post(url, payloadJson, {
        headers,
        timeout: 30000,
      });
      Logger.debug(`任务提交成功，响应数据: ${JSON.stringify(response.data)}`, 'LumaVideoService');
    } catch (error) {
      Logger.error(`任务提交失败: ${error.message}`, 'LumaVideoService');
      if (axios.isAxiosError(error)) {
        if (error.response) {
          Logger.error(`API响应错误: ${JSON.stringify(error.response.data)}`, 'LumaVideoService');
        } else if (error.request) {
          Logger.error('未收到API响应', 'LumaVideoService');
        }
      }
      result.status = 5;
      result.content = '任务提交失败';
      await this.chatLogService.updateChatLog(assistantLogId, {
        content: '任务提交失败',
        status: 5,
      });
      return result;
    }

    if (!response?.data?.id) {
      Logger.error('未能获取任务ID', 'LumaVideoService');
      result.status = 5;
      result.content = '未能获取任务ID';
      await this.chatLogService.updateChatLog(assistantLogId, {
        content: '未能获取任务ID',
        status: 5,
      });
      return result;
    }

    result.taskId = response?.data?.id;
    Logger.log(`任务提交成功, 任务ID: ${response?.data?.id}`, 'LumaService');

    try {
      await this.pollLumaVideoResult({
        proxyUrl,
        apiKey,
        taskId: response.data.id,
        timeout,
        prompt: finalPrompt,
        onSuccess: async data => {
          try {
            await this.chatLogService.updateChatLog(assistantLogId, {
              videoUrl: data?.videoUrl,
              content: data?.content || finalPrompt,
              progress: '100%',
              status: 3,
              taskId: data?.taskId,
              taskData: data?.taskData,
            });
            Logger.log('视频任务已完成', 'LumaService');
          } catch (error) {
            Logger.error(`更新日志失败: ${error.message}`, 'LumaService');
          }
        },
        onGenerating: async data => {
          try {
            await this.chatLogService.updateChatLog(assistantLogId, {
              videoUrl: data?.videoUrl,
              content: data?.content || '视频生成中...',
              progress: data?.progress,
              status: data.status,
            });
            Logger.log('视频生成中...', 'LumaService');
          } catch (error) {
            Logger.error(`更新日志失败: ${error.message}`, 'LumaService');
          }
        },
        onFailure: async data => {
          try {
            await this.chatLogService.updateChatLog(assistantLogId, {
              content: '视频生成失败',
              status: data.status,
            });
            Logger.log('生成失败', 'Lum aService');
          } catch (error) {
            Logger.error(`更新日志失败: ${error.message}`, 'LumaService');
          }
        },
      });
    } catch (error) {
      Logger.error('查询生成结果时发生错误:', error.message, 'LumaService');
      throw new Error('查询生成结果时发生错误');
    }
    return result;
  }

  async pollLumaVideoResult(inputs) {
    const { proxyUrl, apiKey, taskId, timeout, onSuccess, onFailure, onGenerating, prompt } =
      inputs;

    const result: any = {
      videoUrl: '',
      drawId: '',
      taskData: '',
      status: 2,
      progress: 0,
      content: '',
    };

    const headers = { Authorization: `Bearer ${apiKey}` };
    const url = `${proxyUrl}/luma/generations/${taskId}`;
    const startTime = Date.now();
    const totalDuration = 300000;
    const POLL_INTERVAL = 5000; // 每5秒查一次
    let retryCount = 0; // 当前重试次数
    let progressInterval = null; // 用于存储进度更新的计时器

    try {
      while (Date.now() - startTime < timeout) {
        await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL));

        try {
          Logger.debug(`正在查询任务状态，taskId: ${taskId}`, 'LumaVideoService');
          const res = await axios.get(url, {
            headers,
            timeout: 10000, // 添加查询超时时间
          });

          // 清除之前的计时器（如果存在）
          if (progressInterval) {
            clearInterval(progressInterval);
          }

          // 创建新的计时器
          progressInterval = setInterval(() => {
            const elapsed = Date.now() - startTime;
            let percentage = Math.floor((elapsed / totalDuration) * 100);
            if (percentage >= 99) percentage = 99; // 最多显示99%
            result.content = `视频生成中 （${percentage}%）`;
            onGenerating(result);
          }, 1000); // 每秒更新一次进度

          const responses = res.data;

          if (!responses) {
            Logger.warn('收到空响应', 'LumaVideoService');
            continue;
          }

          Logger.debug(`任务状态: ${responses.state}`, 'LumaVideoService');

          if (responses.state === 'completed') {
            clearInterval(progressInterval);
            result.taskId = responses.id;
            result.taskData = JSON.stringify(responses);
            result.videoUrl = responses.video.url;
            Logger.debug(`视频生成完成: ${responses.video.url}`, 'LumaService');

            try {
              // const localStorageStatus =
              //   await this.globalConfigService.getConfigs([
              //     'localStorageStatus',
              //   ]);
              // if (Number(localStorageStatus)) {
              // 使用 Date 对象获取当前日期并格式化为 YYYYMM/DD

              const now = new Date();
              const year = now.getFullYear();
              const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以+1
              const day = String(now.getDate()).padStart(2, '0');
              const currentDate = `${year}${month}/${day}`;
              Logger.debug(`开始上传文件到本地存储`, 'LumaVideoService');
              result.videoUrl = await this.uploadService.uploadFileFromUrl({
                url: responses.video.download_url,
                dir: `video/luma/${currentDate}`,
              });
              Logger.debug(`文件上传成功: ${result.videoUrl}`, 'LumaVideoService');
              // }
            } catch (error) {
              Logger.error(`文件上传失败: ${error.message}`, 'LumaVideoService');
              // 即使上传失败也返回原始URL
              result.videoUrl = responses.video.url;
            }

            result.content = `AI 生成视频 "${prompt}"`;

            onSuccess(result);
            return result;
          } else if (responses.state === 'failed') {
            Logger.error(`任务失败: ${JSON.stringify(responses)}`, 'LumaVideoService');
            result.status = 5;
            result.content = '视频生成失败';
            clearInterval(progressInterval);
            onFailure(result);
            return result;
          } else if (responses.state === 'processing') {
            // 处理中状态
            const progress = responses.progress || 0;
            result.progress = progress;
            result.content = `视频生成中 (${progress}%)`;
            onGenerating(result);
          }
        } catch (error) {
          retryCount++;
          if (retryCount > 3) {
            Logger.error(`轮询重试次数过多，终止查询: ${error.message}`, 'LumaVideoService');
            result.status = 5;
            result.content = '查询任务状态失败';
            clearInterval(progressInterval);
            onFailure(result);
            return result;
          }
          Logger.warn(`轮询失败，准备第${retryCount}次重试: ${error.message}`, 'LumaVideoService');
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
          // 继续重试时也要通知前端
          result.content = `重试中... (${retryCount}/3)`;
          onGenerating(result);
        }
      }

      // 超时处理
      Logger.error('轮询超时，请稍后再试！', 'LumaVideoService');
      result.status = 4;
      result.content = '查询超时，请稍后再试';
      clearInterval(progressInterval);
      onFailure(result);
      return result;
    } catch (error) {
      Logger.error(`轮询过程中发生错误: ${error}`, 'LumaVideoService');
      result.status = 5;
      result.content = '视频生成过程中发生错误';
      clearInterval(progressInterval);
      onFailure(result);
      return result;
    }
  }

  /**
   * 自动翻译 Luma 视频提示词
   * @param prompt 原始提示词
   * @returns 翻译后的提示词
   */
  async translatePrompt(prompt: string): Promise<string> {
    const { isMjTranslate } = await this.globalConfigService.getConfigs(['isMjTranslate']);

    // 如果不需要翻译，直接返回原始提示词
    if (isMjTranslate !== '1') {
      return prompt;
    }

    try {
      const translatedPrompt = await this.openAIChatService.chatFree(
        prompt,
        "Translate any given phrase from any language into English. For instance, when I input '{可爱的熊猫}', you should output '{cute panda}', with no period at the end.",
      );

      Logger.debug(`翻译后的提示词: ${translatedPrompt}`, 'LumaVideoService');
      return translatedPrompt || prompt; // 如果翻译返回空值，则使用原始提示词
    } catch (error) {
      Logger.error(`Luma 提示词翻译失败: ${error.message}`, 'LumaVideoService');
      // 翻译失败时返回原始提示词
      return prompt;
    }
  }
}
