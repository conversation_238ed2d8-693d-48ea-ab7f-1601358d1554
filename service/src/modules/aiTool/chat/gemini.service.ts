import { getTokenCount, removeThinkTags } from '@/common/utils';
import { GoogleGenAI, createPartFromUri, createUserContent } from '@google/genai';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import { HttpsProxyAgent } from 'https-proxy-agent';
import { ChatLogService } from '../../chatLog/chatLog.service';

// Node.js < 18 环境中需要全局安装fetch
// 在最新版本的@google/genai库中将自动使用node-fetch
import 'cross-fetch/polyfill';

// 固定的Gemini配置
const GEMINI_API_KEY = 'AIzaSyBpXBIhkYAwqCKNX5ZD2JdZOxjk3cB4vuA'; // 更新为新的有效API密钥
const GEMINI_DEFAULT_MODEL = 'gemini-1.5-pro-latest'; // 更新为稳定版本模型

@Injectable()
export class GeminiService {
  constructor(private readonly chatLogService: ChatLogService) {}

  /**
   * 创建Gemini API客户端，支持代理配置
   * @param apiKey API密钥
   * @param proxyUrl 代理地址，格式为http://host:port
   * @returns GoogleGenAI实例
   */
  private createGeminiClient(apiKey: string, proxyUrl?: string) {
    // 基础配置
    const options: any = {
      apiKey,
    };

    // 如果提供了代理URL且是有效的代理地址（非API服务地址）
    if (proxyUrl && this.isValidProxyUrl(proxyUrl)) {
      Logger.debug(`使用代理: ${proxyUrl}`, 'GeminiService');
      try {
        const agent = new HttpsProxyAgent(proxyUrl);

        // 设置Google Gemini客户端的fetch选项
        options.fetch = (url: string, init: any) => {
          Logger.debug(`Gemini请求URL: ${url}`, 'GeminiService');
          Logger.debug(`使用API密钥: ${apiKey.substring(0, 8)}...`, 'GeminiService');

          return global.fetch(url, {
            ...init,
            // @ts-ignore - agent属性在Node.js中是有效的，但TypeScript定义可能不包含
            agent,
            // 增加超时设置
            signal: AbortSignal.timeout(60000), // 60秒超时
          });
        };
      } catch (error) {
        Logger.error(`创建代理代理失败: ${error.message}`, 'GeminiService');
      }
    } else {
      // 没有代理或代理无效，设置直连超时
      Logger.debug(`直接连接Gemini API`, 'GeminiService');
      options.fetch = (url: string, init: any) => {
        Logger.debug(`Gemini请求URL: ${url}`, 'GeminiService');

        return global.fetch(url, {
          ...init,
          // 增加超时设置
          signal: AbortSignal.timeout(60000), // 60秒超时
        });
      };
    }

    return new GoogleGenAI(options);
  }

  /**
   * 验证代理URL是否有效
   * @param proxyUrl 代理URL
   * @returns 是否是有效的代理URL
   */
  private isValidProxyUrl(proxyUrl: string): boolean {
    // 检查是否是一个标准格式的代理URL
    try {
      // API服务URL通常不包含端口号，而代理URL通常包含
      const isProxyPattern =
        /^(http|https):\/\/[\w.-]+(:\d+)?$/.test(proxyUrl) && proxyUrl.includes(':');

      // 排除一些明显是API服务的URL
      const isApiService =
        proxyUrl.includes('api.') ||
        proxyUrl.includes('.cloud') ||
        proxyUrl.includes('.ai') ||
        proxyUrl.includes('/v1/') ||
        !proxyUrl.includes(':');

      const isValid = isProxyPattern && !isApiService;

      if (!isValid) {
        Logger.warn(`提供的代理URL "${proxyUrl}" 不是有效的代理格式，将使用直连`, 'GeminiService');
      }

      return isValid;
    } catch (e) {
      Logger.warn(`代理URL验证失败: ${e.message}`, 'GeminiService');
      return false;
    }
  }

  /**
   * 处理PDF文件并使用Gemini生成内容
   * @param pdfUrl PDF文件的URL
   * @param prompt 提示词
   * @param options 选项
   * @returns 生成的内容
   */
  async processPdfWithGemini(pdfUrl: string, prompt: string, options: any = {}) {
    const {
      chatId,
      apiKey = GEMINI_API_KEY,
      model = GEMINI_DEFAULT_MODEL,
      temperature = 0.7,
      timeout = 300000,
      proxyUrl,
      onProgress,
      onFailure,
    } = options;

    try {
      Logger.debug(`开始处理PDF文件: ${pdfUrl}`, 'GeminiService');
      Logger.debug(`使用模型: ${model}, 温度: ${temperature}`, 'GeminiService');

      // 初始化Gemini客户端
      const ai = this.createGeminiClient(apiKey, proxyUrl);

      // 获取PDF文件内容
      const pdfResponse = await axios.get(pdfUrl, {
        responseType: 'arraybuffer',
        // 如果PDF请求也需要代理
        ...(proxyUrl && this.isValidProxyUrl(proxyUrl)
          ? {
              proxy: {
                host: proxyUrl.split('://')[1].split(':')[0],
                port: parseInt(proxyUrl.split(':')[2]),
              },
            }
          : {}),
      });
      const pdfBuffer = Buffer.from(pdfResponse.data);
      const base64PdfData = pdfBuffer.toString('base64');

      // 构建请求内容
      const contents = [
        { text: prompt || '请总结这个文档的内容' },
        {
          inlineData: {
            mimeType: 'application/pdf',
            data: base64PdfData,
          },
        },
      ];

      // 生成内容
      const generateParams = {
        model,
        contents,
        temperature,
      } as any;

      const streamResponse = await ai.models.generateContentStream(generateParams);

      let full_content = '';

      // 处理流式响应
      for await (const chunk of streamResponse) {
        const chunkText = chunk.text || '';
        full_content += chunkText;

        // 回调进度更新
        if (onProgress && typeof onProgress === 'function') {
          onProgress({
            text: chunkText,
            full_content: full_content,
          });
        }
      }

      return {
        full_content,
        full_reasoning_content: '',
        imageUrl: null,
        videoUrl: null,
        taskId: null,
        tool_calls: null,
      };
    } catch (error) {
      Logger.error(`处理PDF文件失败: ${error.message}`, 'GeminiService');

      if (onFailure && typeof onFailure === 'function') {
        onFailure({
          errMsg: `处理PDF文件失败: ${error.message}`,
        });
      }

      return {
        errMsg: `处理PDF文件失败: ${error.message}`,
        status: 4,
      };
    }
  }

  /**
   * 增强图片处理，支持URL直接输入
   * @param imageUrl 图片URL
   * @param prompt 提示词
   * @param options 选项
   * @returns 生成的内容
   */
  async processImageWithGemini(imageUrl: string, prompt: string, options: any = {}) {
    const {
      chatId,
      apiKey = GEMINI_API_KEY,
      model = GEMINI_DEFAULT_MODEL,
      temperature = 0.7,
      timeout = 300000,
      proxyUrl,
      onProgress,
      onFailure,
    } = options;

    try {
      Logger.debug(`开始处理图片: ${imageUrl}`, 'GeminiService');
      Logger.debug(`使用模型: ${model}, 温度: ${temperature}`, 'GeminiService');

      // 初始化Gemini客户端
      const ai = this.createGeminiClient(apiKey, proxyUrl);

      // 获取图片数据
      const imageData = await this.getImageDataFromUrl(imageUrl, proxyUrl);

      // 构建请求内容
      const contents = [
        {
          inlineData: {
            mimeType: imageData.mimeType,
            data: imageData.data,
          },
        },
        { text: prompt || '描述这张图片' },
      ];

      // 生成内容
      const generateParams = {
        model,
        contents,
        temperature,
      } as any;

      const streamResponse = await ai.models.generateContentStream(generateParams);

      let full_content = '';

      // 处理流式响应
      for await (const chunk of streamResponse) {
        const chunkText = chunk.text || '';
        full_content += chunkText;

        // 回调进度更新
        if (onProgress && typeof onProgress === 'function') {
          onProgress({
            text: chunkText,
            full_content: full_content,
          });
        }
      }

      return {
        full_content,
        full_reasoning_content: '',
        imageUrl: null,
        videoUrl: null,
        taskId: null,
        tool_calls: null,
      };
    } catch (error) {
      Logger.error(`处理图片失败: ${error.message}`, 'GeminiService');

      if (onFailure && typeof onFailure === 'function') {
        onFailure({
          errMsg: `处理图片失败: ${error.message}`,
        });
      }

      return {
        errMsg: `处理图片失败: ${error.message}`,
        status: 4,
      };
    }
  }

  /**
   * 构建消息历史
   * 类似于chat.service.ts中的buildMessageFromParentMessageId方法
   * @param options 配置选项
   * @returns 格式化后的消息历史
   */
  async buildMessageFromParentMessageId(options: any) {
    const startTime = Date.now();

    let {
      systemMessage = '',
      maxRounds = 12,
      maxModelTokens = 64000,
      isFileUpload = 0,
      isImageUpload = 0,
      isConvertToBase64,
      groupId,
    } = options;

    const messages = [];
    // 查询历史对话列表
    if (groupId) {
      try {
        // 使用chatLogService获取历史记录
        const history = await this.chatLogService.chatHistory(groupId, maxRounds);

        // 按时间顺序排序历史记录
        const sortedHistory = [...history].sort((a: any, b: any) => {
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });

        // 整理消息
        let userMessages = [];
        let assistantMessages = [];

        // 分类所有消息
        for (const record of sortedHistory) {
          try {
            let content;

            // 初始化内容为原始内容或空字符串
            content = record.content || '';
            let hasSpecialFormat = false;

            // 1. 处理文件内容
            if (isFileUpload === 1 && record.fileUrl) {
              try {
                // 尝试解析JSON格式的fileUrl
                const filesInfo = JSON.parse(record.fileUrl);
                if (Array.isArray(filesInfo)) {
                  // 提取所有文件的URL并拼接
                  const fileUrls = filesInfo.map((file: any) => file.url).join('\n');
                  content = fileUrls + '\n' + content;
                } else {
                  // 如果不是数组格式，按原来方式处理
                  content = record.fileUrl + '\n' + content;
                }
              } catch (error) {
                // 如果解析失败，说明可能是旧格式，直接拼接
                content = record.fileUrl + '\n' + content;
                Logger.debug(`解析fileUrl失败，使用原始格式: ${error.message}`, 'GeminiService');
              }
            }

            // 2. 处理图片内容
            const recordWithExt = record as any;
            if (isImageUpload === 2 && record.imageUrl) {
              // Gemini的图片处理方式不同于OpenAI，这里只记录图片URL，后续处理
              hasSpecialFormat = true;
              // 对于Gemini，我们暂时以字符串方式存储，在实际发送消息时再处理
              // 不使用OpenAI的数组格式
              // 单独记录图片URL，以便后续处理
              recordWithExt.geminiImageUrls = record.imageUrl.split(',').map(url => url.trim());
            }
            // 3. 逆向格式图片，直接添加到内容前面
            else if (isImageUpload === 1 && record.imageUrl) {
              content = record.imageUrl + '\n' + content;
            }

            // 处理assistant消息，移除think标签
            if (record.role === 'assistant') {
              content = removeThinkTags(content);

              // 跳过空的assistant消息
              if (typeof content === 'string' && !content.trim()) {
                continue;
              }

              assistantMessages.push({
                id: recordWithExt.id,
                role: 'assistant',
                content: content,
                createdAt: recordWithExt.createdAt,
                geminiImageUrls: recordWithExt.geminiImageUrls,
              });
            } else if (record.role === 'user') {
              userMessages.push({
                id: recordWithExt.id,
                role: 'user',
                content: content,
                createdAt: recordWithExt.createdAt,
                geminiImageUrls: recordWithExt.geminiImageUrls,
              });
            }
          } catch (error) {
            Logger.debug(
              `处理历史记录ID=${(record as any).id}失败: ${error.message}`,
              'GeminiService',
            );
          }
        }

        // 构建OpenAI格式的消息序列，随后在geminiChat中会转换为Gemini格式
        // 系统消息在Gemini中会特殊处理
        if (systemMessage) {
          messages.push({ role: 'system', content: systemMessage });
        }

        // 获取所有用户和助手消息，并按时间排序
        userMessages.sort(
          (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );
        assistantMessages.sort(
          (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
        );

        // 计算配对数量（取较小的数量）
        const pairCount = Math.min(userMessages.length, assistantMessages.length);

        // 按user-assistant对添加消息
        for (let i = 0; i < pairCount; i++) {
          messages.push({
            role: 'user',
            content: userMessages[i].content,
            geminiImageUrls: userMessages[i].geminiImageUrls,
          });
          messages.push({
            role: 'assistant',
            content: assistantMessages[i].content,
          });
        }

        // 如果用户消息比助手消息多，添加最后一条用户消息
        if (userMessages.length > pairCount) {
          messages.push({
            role: 'user',
            content: userMessages[userMessages.length - 1].content,
            geminiImageUrls: userMessages[userMessages.length - 1].geminiImageUrls,
          });
        }
      } catch (error) {
        Logger.error(`获取聊天历史记录失败: ${error.message}`, 'GeminiService');
      }
    }

    // 计算并限制token数量
    let totalTokens = await getTokenCount(messages);

    // 动态计算token限制 (Gemini可能有不同的token限制，可能需要调整)
    const tokenLimit = maxModelTokens < 8000 ? 4000 : maxModelTokens - 4000;

    // 如果超出token限制，进行裁剪
    if (totalTokens > tokenLimit) {
      Logger.debug(`消息超出token限制(${totalTokens} > ${tokenLimit})，开始裁剪`, 'GeminiService');

      // 裁剪算法
      while (totalTokens > tokenLimit && messages.length > 2) {
        // 检查是否只剩下系统消息和当前用户消息
        if (
          messages.length === 2 &&
          ((messages[0].role === 'system' && messages[1].role === 'user') ||
            (messages[0].role === 'user' && messages[1].role === 'user'))
        ) {
          break;
        }

        // 保留系统消息和最后一条用户消息
        const systemIndex = messages.findIndex(m => m.role === 'system');
        const lastUserIndex = messages.length - 1; // 最后一条始终是当前用户消息

        // 从前往后删除非系统消息
        if (messages.length > 2) {
          // 跳过系统消息
          const startIndex = systemIndex === 0 ? 1 : 0;

          // 删除最早的user-assistant对或单条消息
          if (startIndex < lastUserIndex) {
            if (
              messages[startIndex].role === 'user' &&
              startIndex + 1 < lastUserIndex &&
              messages[startIndex + 1].role === 'assistant'
            ) {
              // 删除一对消息
              messages.splice(startIndex, 2);
            } else {
              // 删除单条消息
              messages.splice(startIndex, 1);
            }
          }
        }

        // 重新计算token
        const newTotalTokens = await getTokenCount(messages);
        if (newTotalTokens >= totalTokens) {
          // 如果token没有减少，停止裁剪
          Logger.debug('Token裁剪无效，停止裁剪过程', 'GeminiService');
          break;
        }

        // 更新token计数
        totalTokens = newTotalTokens;
      }
    }

    // 修正消息顺序，确保交替出现
    if (messages.length > 1) {
      const fixedMessages = [];
      // 保留系统消息
      if (messages[0].role === 'system') {
        fixedMessages.push(messages[0]);
        messages.shift();
      }

      // 按照user-assistant交替顺序重新构建
      const userMessages = messages
        .filter(msg => msg.role === 'user')
        .sort(
          (a, b) => new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime(),
        );

      const assistantMessages = messages
        .filter(msg => msg.role === 'assistant')
        .sort(
          (a, b) => new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime(),
        );

      // 使用较少的消息数量
      const pairCount = Math.min(userMessages.length, assistantMessages.length);

      // 构建交替的消息对
      for (let i = 0; i < pairCount; i++) {
        fixedMessages.push(userMessages[i]);
        fixedMessages.push(assistantMessages[i]);
      }

      // 如果还有剩余的user消息，添加最后一条
      if (userMessages.length > pairCount) {
        fixedMessages.push(userMessages[userMessages.length - 1]);
      }

      // 替换原消息数组
      messages.length = 0;
      messages.push(...fixedMessages);
    }

    Logger.debug(
      `构建Gemini消息历史完成: ${Math.floor(
        messages.length / 2,
      )} 组对话, ${totalTokens} tokens, 耗时: ${Date.now() - startTime}ms`,
      'GeminiService',
    );

    return {
      messagesHistory: messages,
      round: messages.length,
    };
  }

  /**
   * 将OpenAI格式的消息历史转换为Gemini格式
   * @param messagesHistory OpenAI格式的消息历史
   * @returns Gemini格式的消息历史
   */
  formatOpenAIToGeminiMessages(messagesHistory: any[]) {
    const history = [];

    // 跳过系统消息，因为Gemini不支持系统消息
    let startIndex = messagesHistory[0]?.role === 'system' ? 1 : 0;

    // 逐对处理消息
    for (let i = startIndex; i < messagesHistory.length - 1; i += 2) {
      const userMessage = messagesHistory[i];
      const assistantMessage = messagesHistory[i + 1];

      // 确保消息对齐
      if (userMessage?.role === 'user' && assistantMessage?.role === 'assistant') {
        // 处理用户消息
        const userParts = [];
        if (typeof userMessage.content === 'string') {
          userParts.push({ text: userMessage.content });
        } else if (Array.isArray(userMessage.content)) {
          // 处理包含图片的复合消息
          for (const part of userMessage.content) {
            if (part.type === 'text') {
              userParts.push({ text: part.text });
            } else if (part.type === 'image_url') {
              userParts.push({ inlineData: { data: part.image_url.url, mimeType: 'image/jpeg' } });
            }
          }
        }

        // 处理特殊的geminiImageUrls (在buildMessageFromParentMessageId中添加的)
        if (userMessage.geminiImageUrls && userMessage.geminiImageUrls.length > 0) {
          // 稍后在geminiChat中处理这些图片URL
        }

        // 添加用户消息
        history.push({
          role: 'user',
          parts: userParts,
        });

        // 添加助手消息
        history.push({
          role: 'model',
          parts: [{ text: assistantMessage.content }],
        });
      }
    }

    // 处理最后一条用户消息（如果有）
    if (messagesHistory.length > 0 && messagesHistory[messagesHistory.length - 1].role === 'user') {
      const lastUserMessage = messagesHistory[messagesHistory.length - 1];
      const userParts = [];

      if (typeof lastUserMessage.content === 'string') {
        userParts.push({ text: lastUserMessage.content });
      } else if (Array.isArray(lastUserMessage.content)) {
        // 处理包含图片的复合消息
        for (const part of lastUserMessage.content) {
          if (part.type === 'text') {
            userParts.push({ text: part.text });
          } else if (part.type === 'image_url') {
            userParts.push({ inlineData: { data: part.image_url.url, mimeType: 'image/jpeg' } });
          }
        }
      }

      // 处理特殊的geminiImageUrls
      if (lastUserMessage.geminiImageUrls && lastUserMessage.geminiImageUrls.length > 0) {
        // 在geminiChat中处理
      }

      // 不加入 history，作为当前用户最新消息
      return {
        history,
        currentMessage: {
          role: 'user',
          parts: userParts,
          geminiImageUrls: lastUserMessage.geminiImageUrls,
        },
      };
    }

    return { history, currentMessage: null };
  }

  /**
   * 将OpenAI格式的系统消息转为Gemini用户消息
   * @param systemMessage 系统消息内容
   * @returns 格式化后的用户消息
   */
  formatSystemMessageToGemini(systemMessage: string) {
    return {
      role: 'user',
      parts: [{ text: `You are an AI assistant. ${systemMessage}` }],
    };
  }

  /**
   * 从URL获取图片的base64数据
   * @param url 图片URL
   * @param proxyUrl 可选的代理URL
   * @returns Promise<{data: string, mimeType: string}>
   */
  async getImageDataFromUrl(url: string, proxyUrl?: string) {
    try {
      // 配置代理
      const axiosConfig: any = {
        responseType: 'arraybuffer',
        timeout: 30000, // 30秒超时
      };

      // 如果提供了代理URL，设置代理
      if (proxyUrl && this.isValidProxyUrl(proxyUrl)) {
        const proxyParts = proxyUrl.split('://')[1].split(':');
        axiosConfig.proxy = {
          host: proxyParts[0],
          port: parseInt(proxyParts[1]),
        };
      }

      const response = await axios.get(url, axiosConfig);
      const buffer = Buffer.from(response.data, 'binary');
      const base64Data = buffer.toString('base64');
      const mimeType = response.headers['content-type'] || 'image/jpeg';

      return { data: base64Data, mimeType };
    } catch (error) {
      Logger.error(`获取图片数据失败: ${error.message}`, 'GeminiService');
      throw error;
    }
  }

  /**
   * Gemini聊天主函数
   * @param messagesHistory OpenAI格式的消息历史
   * @param options 聊天选项
   * @returns 聊天响应
   */
  async geminiChat(messagesHistory: any[], options: any) {
    const {
      chatId,
      apiKey = GEMINI_API_KEY,
      model = GEMINI_DEFAULT_MODEL,
      modelName,
      temperature = 0.7,
      prompt,
      imageUrl,
      isFileUpload,
      timeout = 300000,
      proxyUrl,
      usingDeepThinking,
      usingMcpTool,
      fileVectorSearch,
      imageDescription,
      isMcpTool,
      onProgress,
      onFailure,
      abortController,
    } = options;

    try {
      Logger.debug(`Gemini聊天开始，消息历史长度: ${messagesHistory.length}`, 'GeminiService');
      Logger.debug(`使用模型: ${model}, 温度: ${temperature}`, 'GeminiService');

      // 检查代理参数
      if (proxyUrl) {
        const isValidProxy = this.isValidProxyUrl(proxyUrl);
        if (!isValidProxy) {
          Logger.warn(`提供的代理URL无效: ${proxyUrl}，将使用直接连接`, 'GeminiService');
        }
      }

      // 获取系统消息
      const systemMessage = messagesHistory.find(msg => msg.role === 'system')?.content || '';

      // 初始化Gemini客户端
      const ai = this.createGeminiClient(apiKey, proxyUrl);

      // 转换消息格式
      const { history, currentMessage } = this.formatOpenAIToGeminiMessages(messagesHistory);

      // 创建聊天实例
      const chat = ai.chats.create({
        model,
        history: systemMessage
          ? [this.formatSystemMessageToGemini(systemMessage), ...history]
          : history,
        generationConfig: {
          temperature,
        },
      } as any);

      // 处理图片和文件
      let finalMessage = prompt;
      let messageParts = [finalMessage];

      // 如果有图片URL，添加图片
      if (imageUrl) {
        try {
          const imageData = await this.getImageDataFromUrl(imageUrl, proxyUrl);
          messageParts.push(
            createPartFromUri(
              `data:${imageData.mimeType};base64,${imageData.data}`,
              imageData.mimeType,
            ),
          );
        } catch (error) {
          Logger.error(`处理图片失败: ${error.message}`, 'GeminiService');
        }
      }

      // 处理上下文中的图片URLs (如果存在)
      if (currentMessage?.geminiImageUrls && currentMessage.geminiImageUrls.length > 0) {
        try {
          for (const imgUrl of currentMessage.geminiImageUrls) {
            const imageData = await this.getImageDataFromUrl(imgUrl, proxyUrl);
            messageParts.push(
              createPartFromUri(
                `data:${imageData.mimeType};base64,${imageData.data}`,
                imageData.mimeType,
              ),
            );
          }
        } catch (error) {
          Logger.error(`处理上下文图片失败: ${error.message}`, 'GeminiService');
        }
      }

      // 准备流式响应
      const response = await chat.sendMessageStream({
        message:
          Array.isArray(messageParts) && messageParts.length > 1
            ? createUserContent(messageParts)
            : finalMessage,
      });

      let full_content = '';
      let full_reasoning_content = '';

      // 处理流式响应
      for await (const chunk of response) {
        if (abortController?.signal?.aborted) {
          break;
        }

        const chunkText = chunk.text || '';
        full_content += chunkText;

        // 回调进度更新
        if (onProgress && typeof onProgress === 'function') {
          onProgress({
            text: chunkText,
            full_content: full_content,
          });
        }
      }

      return {
        full_content,
        full_reasoning_content,
        imageUrl: null,
        videoUrl: null,
        taskId: null,
        tool_calls: null,
      };
    } catch (error) {
      Logger.error(`Gemini聊天出错: ${error.message}`, 'GeminiService');
      // 记录更详细的错误信息
      if (error.stack) {
        Logger.error(`错误堆栈: ${error.stack}`, 'GeminiService');
      }

      if (onFailure && typeof onFailure === 'function') {
        onFailure({
          errMsg: `聊天失败: ${error.message}`,
        });
      }

      return {
        errMsg: `聊天失败: ${error.message}`,
        status: 4,
      };
    }
  }

  /**
   * 使用代码执行工具的聊天功能
   * @param prompt 提示语
   * @param options 选项
   * @returns 执行结果
   */
  async geminiWithCodeExecution(prompt: string, options: any = {}) {
    const {
      chatId,
      apiKey = GEMINI_API_KEY,
      model = GEMINI_DEFAULT_MODEL,
      temperature = 0.7,
      timeout = 300000,
      proxyUrl,
      onProgress,
      onFailure,
    } = options;

    try {
      Logger.debug(`开始使用代码执行: ${prompt}`, 'GeminiService');
      Logger.debug(`使用模型: ${model}, 温度: ${temperature}`, 'GeminiService');

      // 初始化Gemini客户端
      const ai = this.createGeminiClient(apiKey, proxyUrl);

      // 创建聊天实例，启用代码执行工具
      const chat = ai.chats.create({
        model,
        generationConfig: {
          temperature,
        },
      } as any);

      // 添加代码执行工具
      const chatWithTool = chat as any;
      if (chatWithTool.tools) {
        chatWithTool.tools = [{ codeExecution: {} }];
      }

      // 发送消息
      const streamResponse = await chat.sendMessageStream({
        message: prompt,
      });

      let full_content = '';
      let tool_calls = null;

      // 处理流式响应
      for await (const chunk of streamResponse) {
        const chunkText = chunk.text || '';
        full_content += chunkText;

        // 如果有代码执行结果
        const response = chunk as any;
        if (response.parts && response.parts.length > 0) {
          for (const part of response.parts) {
            if (part.functionCall || part.functionResponse) {
              if (!tool_calls) tool_calls = [];
              tool_calls.push(part);
            }
          }
        }

        // 回调进度更新
        if (onProgress && typeof onProgress === 'function') {
          onProgress({
            text: chunkText,
            full_content: full_content,
            tool_calls: tool_calls,
          });
        }
      }

      return {
        full_content,
        full_reasoning_content: '',
        imageUrl: null,
        videoUrl: null,
        taskId: null,
        tool_calls: tool_calls,
      };
    } catch (error) {
      Logger.error(`代码执行失败: ${error.message}`, 'GeminiService');

      if (onFailure && typeof onFailure === 'function') {
        onFailure({
          errMsg: `代码执行失败: ${error.message}`,
        });
      }

      return {
        errMsg: `代码执行失败: ${error.message}`,
        status: 4,
      };
    }
  }

  /**
   * 使用Google搜索检索功能的聊天
   * @param prompt 提示语
   * @param options 选项
   * @returns 执行结果
   */
  async geminiWithGoogleSearch(prompt: string, options: any = {}) {
    const {
      chatId,
      apiKey = GEMINI_API_KEY,
      model = GEMINI_DEFAULT_MODEL,
      temperature = 0.7,
      dynamicThreshold = 0.3, // 动态检索阈值
      timeout = 300000,
      proxyUrl,
      onProgress,
      onFailure,
    } = options;

    try {
      Logger.debug(`开始使用Google搜索: ${prompt}`, 'GeminiService');
      Logger.debug(`使用模型: ${model}, 温度: ${temperature}`, 'GeminiService');

      // 初始化Gemini客户端
      const ai = this.createGeminiClient(apiKey, proxyUrl);

      // 准备搜索工具配置
      const searchTools =
        model.includes('2.0') || model.includes('2.5')
          ? // Gemini 2.0/2.5使用工具形式的搜索
            [{ googleSearchRetrieval: {} }]
          : // Gemini 1.5使用动态检索配置
            [
              {
                googleSearchRetrieval: {
                  dynamicRetrievalConfig: {
                    mode: 'MODE_DYNAMIC',
                    dynamicThreshold: dynamicThreshold,
                  },
                },
              },
            ];

      try {
        // 创建内容生成请求
        const generateParams = {
          model,
          contents: prompt,
          temperature: temperature,
        } as any;

        // 添加工具参数
        generateParams.tools = searchTools;

        const streamResponse = await ai.models.generateContentStream(generateParams);

        let full_content = '';
        let search_results = [];
        let grounding_metadata = null;

        // 处理流式响应
        for await (const chunk of streamResponse) {
          const chunkText = chunk.text || '';
          full_content += chunkText;

          // 检查是否有接地元数据
          const response = chunk as any;
          if (response.groundingMetadata) {
            grounding_metadata = response.groundingMetadata;

            // 提取搜索结果
            if (grounding_metadata.groundingChunks) {
              search_results = grounding_metadata.groundingChunks.map((chunk, index) => ({
                resultIndex: index + 1,
                title: chunk.web?.title || '',
                link: chunk.web?.uri || '',
                content: chunk.web?.snippet || '',
              }));
            }
          }

          // 回调进度更新
          if (onProgress && typeof onProgress === 'function') {
            onProgress({
              text: chunkText,
              full_content: full_content,
              search_results: search_results.length > 0 ? search_results : null,
            });
          }
        }

        return {
          full_content,
          full_reasoning_content: '',
          imageUrl: null,
          videoUrl: null,
          taskId: null,
          tool_calls: null,
          search_results: search_results.length > 0 ? search_results : null,
          networkSearchResult:
            search_results.length > 0 ? JSON.stringify(search_results, null, 2) : null,
          grounding_metadata,
        };
      } catch (genError) {
        Logger.error(`Gemini API请求错误: ${genError.message}`, 'GeminiService');
        throw genError;
      }
    } catch (error) {
      Logger.error(`Google搜索检索失败: ${error.message}`, 'GeminiService');

      if (onFailure && typeof onFailure === 'function') {
        onFailure({
          errMsg: `Google搜索检索失败: ${error.message}`,
        });
      }

      return {
        errMsg: `Google搜索检索失败: ${error.message}`,
        status: 4,
      };
    }
  }
}
