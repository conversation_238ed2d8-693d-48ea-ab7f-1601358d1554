import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import * as fs from 'fs';
import * as os from 'os';
import * as path from 'path';
import * as stream from 'stream';
import { promisify } from 'util';
import { generateDoubaoSignature } from '../../../common/utils/doubaoSignature';
import { ChatLogService } from '../../chatLog/chatLog.service';
import { GlobalConfigService } from '../../globalConfig/globalConfig.service';
import { UploadService } from '../../upload/upload.service';
import { OpenAIChatService } from '../chat/chat.service';

const pipeline = promisify(stream.pipeline);

// 豆包Function Calling工具定义 - 三个专门的工具

// 1. 蒙版处理工具 - 重点格式化参数，几乎不润色提示词
const doubaoInpaintingTool = {
  type: 'function' as const,
  function: {
    name: 'process_doubao_inpainting',
    description: '处理涂抹编辑（Inpainting）请求，当检测到蒙版时使用，重点是格式化参数',
    parameters: {
      type: 'object',
      properties: {
        original_prompt: {
          type: 'string',
          description: '保持用户原始提示词，仅做最小必要的格式调整，不进行大幅润色',
        },
        inpainting_params: {
          type: 'object',
          properties: {
            scale: {
              type: 'number',
              description: '引导强度，范围1-30，涂抹编辑推荐5-8',
            },
            steps: {
              type: 'number',
              description: '推理步数，范围1-50，涂抹编辑推荐25-35',
            },
            seed: {
              type: 'number',
              description: '随机种子，-1为随机',
            },
            add_logo: {
              type: 'boolean',
              description: '是否添加水印，涂抹编辑通常不建议',
            },
            logo_position: {
              type: 'number',
              description: '水印位置：0-右下角，1-左下角，2-左上角，3-右上角',
            },
            logo_language: {
              type: 'number',
              description: '水印语言：0-中文，1-英文',
            },
            logo_opacity: {
              type: 'number',
              description: '水印透明度，0-1之间',
            },
            logo_text_content: {
              type: 'string',
              description: '自定义水印文本内容',
            },
          },
          description: '涂抹编辑专用参数，针对局部重绘优化',
        },
        completion_reply: {
          type: 'string',
          description: '简洁的完成回复，告知用户涂抹编辑已完成',
        },
      },
      required: ['original_prompt', 'completion_reply'],
    },
  },
};

// 2. 图生图工具 - 既要格式化参数，也要根据上下文生成准确提示词
const doubaoControlNetTool = {
  type: 'function' as const,
  function: {
    name: 'process_doubao_controlnet',
    description: '处理图生图（ControlNet）请求，当有图像但无蒙版时使用，需要优化提示词和参数',
    parameters: {
      type: 'object',
      properties: {
        enhanced_prompt: {
          type: 'string',
          description:
            '按照【艺术风格】+【主体描述】+【文字排版】模板优化的提示词，参考图像内容和用户需求',
        },
        controlnet_config: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['canny', 'depth', 'pose'],
              description:
                'ControlNet类型：canny(轮廓边缘)、depth(景深)、pose(人物姿态)，根据参考图像特点选择',
            },
            strength: {
              type: 'number',
              description: 'ControlNet强度，范围(0.0, 1.0]，建议0.6-0.8',
            },
          },
          description: '图生图ControlNet配置，根据参考图像特征智能选择',
        },
        controlnet_params: {
          type: 'object',
          properties: {
            width: {
              type: 'number',
              description: '图像宽度，范围256-768，根据用户意图和参考图像智能推荐',
            },
            height: {
              type: 'number',
              description: '图像高度，范围256-768，根据用户意图和参考图像智能推荐',
            },
            size_reasoning: {
              type: 'string',
              description: '选择此尺寸的分析理由，基于用户需求和参考图像',
            },
            scale: {
              type: 'number',
              description: '引导强度，范围1-30，图生图推荐3-6',
            },
            ddim_steps: {
              type: 'number',
              description: '推理步数，范围1-50，图生图推荐16-25',
            },
            seed: {
              type: 'number',
              description: '随机种子，-1为随机',
            },
            use_rephraser: {
              type: 'boolean',
              description: '是否使用提示词重写，图生图推荐开启',
            },
            add_logo: {
              type: 'boolean',
              description: '是否添加水印',
            },
            logo_position: {
              type: 'number',
              description: '水印位置：0-右下角，1-左下角，2-左上角，3-右上角',
            },
            logo_language: {
              type: 'number',
              description: '水印语言：0-中文，1-英文',
            },
            logo_text_content: {
              type: 'string',
              description: '自定义水印文本内容',
            },
          },
          description: '图生图专用参数，针对ControlNet优化',
        },
        completion_reply: {
          type: 'string',
          description: '生成的回复文本，告知用户图生图处理已完成，可提及参考图像的使用',
        },
      },
      required: ['enhanced_prompt', 'controlnet_config', 'completion_reply'],
    },
  },
};

// 3. 智能判断工具 - 分析历史消息判断是纯文生图还是基于历史图像的图生图
const doubaoIntentAnalysisTool = {
  type: 'function' as const,
  function: {
    name: 'analyze_doubao_intent',
    description: '当没有传图片时使用，分析历史消息判断用户意图：纯文生图 vs 基于历史图像的图生图',
    parameters: {
      type: 'object',
      properties: {
        intent_type: {
          type: 'string',
          enum: ['pure_text_to_image', 'history_based_image_to_image'],
          description:
            'pure_text_to_image(纯文生图) 或 history_based_image_to_image(基于历史图像的图生图)',
        },
        enhanced_prompt: {
          type: 'string',
          description:
            '按照【艺术风格】+【主体描述】+【文字排版】模板优化的提示词，结合用户需求和历史上下文',
        },
        reference_images: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              url: {
                type: 'string',
                description: '历史图像的URL',
              },
              relevance_score: {
                type: 'number',
                description: '图像与当前请求的相关性分数(0-10)',
              },
              message_index: {
                type: 'number',
                description: '图像在历史消息中的索引',
              },
              usage_reason: {
                type: 'string',
                description: '选择此图像的原因说明',
              },
            },
          },
          description: '当intent_type为history_based_image_to_image时，提取的相关历史图像',
        },
        controlnet_config: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['canny', 'depth', 'pose'],
              description: 'ControlNet类型，仅在使用历史图像时需要',
            },
            strength: {
              type: 'number',
              description: 'ControlNet强度，范围(0.0, 1.0]，建议0.6',
            },
          },
          description: '当使用历史图像时的ControlNet配置',
        },
        generation_params: {
          type: 'object',
          properties: {
            width: {
              type: 'number',
              description: '图像宽度，范围256-768，根据用户意图和内容类型智能推荐',
            },
            height: {
              type: 'number',
              description: '图像高度，范围256-768，根据用户意图和内容类型智能推荐',
            },
            size_reasoning: {
              type: 'string',
              description: '选择此尺寸的分析理由，基于用户需求和图像内容类型',
            },
            scale: {
              type: 'number',
              description: '引导强度，文生图推荐3.5，图生图推荐3-6',
            },
            ddim_steps: {
              type: 'number',
              description: '推理步数，文生图推荐25，图生图推荐16-25',
            },
            use_sr: {
              type: 'boolean',
              description: '是否使用超分辨率',
            },
            use_pre_llm: {
              type: 'boolean',
              description: '是否使用预处理LLM优化提示词',
            },
            use_rephraser: {
              type: 'boolean',
              description: '是否使用提示词重写，仅图生图时有效',
            },
            add_logo: {
              type: 'boolean',
              description: '是否添加水印',
            },
            logo_position: {
              type: 'number',
              description: '水印位置：0-右下角，1-左下角，2-左上角，3-右上角',
            },
            logo_language: {
              type: 'number',
              description: '水印语言：0-中文，1-英文',
            },
            logo_opacity: {
              type: 'number',
              description: '水印透明度，0-1之间',
            },
            logo_text_content: {
              type: 'string',
              description: '自定义水印文本内容',
            },
          },
          description: '智能推荐的生成参数，根据意图类型和图像内容优化',
        },
        completion_reply: {
          type: 'string',
          description: '生成的回复文本，告知用户图像生成已完成，根据意图类型调整语气',
        },
      },
      required: ['intent_type', 'enhanced_prompt', 'completion_reply'],
    },
  },
};

/**
 * 豆包图像生成服务
 * 支持文生图、图生图、涂抹编辑等功能
 *
 * 特性：
 * - 完整的HMAC-SHA256签名认证
 * - 智能Function Calling提示词优化
 * - 中文优化和文化元素理解
 * - 完善的错误处理和用户反馈
 * - 高性能并发处理
 * - 智能参数推荐和验证
 */
@Injectable()
export class DoubaoImageService {
  // 添加缓存和性能优化相关属性
  private readonly parameterCache = new Map<string, any>();
  private readonly requestQueue = new Map<string, Promise<any>>();
  private readonly maxCacheSize = 100;
  private readonly maxConcurrentRequests = 5;
  private currentRequests = 0;

  constructor(
    private readonly uploadService: UploadService,
    private readonly globalConfigService: GlobalConfigService,
    private readonly chatLogService: ChatLogService,
    private readonly openAIChatService: OpenAIChatService,
  ) {}

  // 创建临时文件
  private async createTempFile(buffer: Buffer, extension: string = 'png'): Promise<string> {
    const tempDir = os.tmpdir();
    const fileName = `doubao-image-${Date.now()}-${Math.floor(Math.random() * 10000)}.${extension}`;
    const filePath = path.join(tempDir, fileName);

    // 使用 buffer转stream 方式写入
    const bufferStream = new stream.PassThrough();
    bufferStream.end(buffer);
    await pipeline(bufferStream, fs.createWriteStream(filePath));
    return filePath;
  }

  // 清理临时文件
  private async cleanupTempFiles(files: string[]) {
    for (const file of files) {
      try {
        await fs.promises.unlink(file);
      } catch (error) {
        Logger.error(`清理临时文件失败: ${error}`, 'DoubaoImageService');
      }
    }
  }

  /**
   * 豆包图像生成主入口（优化版）
   * @param inputs 输入参数
   * @param buildMessageFromParentMessageId 构建消息历史的函数
   * @returns 生成结果
   */
  async doubaoImageGenerate(inputs, buildMessageFromParentMessageId) {
    Logger.log('开始AI图像生成任务', 'DoubaoImageService');

    // 性能监控
    const startTime = Date.now();
    const requestId = `doubao-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // 并发控制
    if (this.currentRequests >= this.maxConcurrentRequests) {
      Logger.warn(
        `服务并发请求数达到上限(${this.maxConcurrentRequests})，请求排队中...`,
        'DoubaoImageService',
      );
    }

    // 临时文件列表，用于后续清理
    const tempFiles: string[] = [];
    const result: any = { content: '', imageUrl: '', status: 2, requestId };

    try {
      this.currentRequests++;
      Logger.debug(
        `请求开始 [${requestId}]，当前并发数: ${this.currentRequests}`,
        'DoubaoImageService',
      );

      // 输入参数验证和优化
      const parsedData = await this.parseAndValidateDoubaoInputs(inputs);

      // 检查参数缓存
      const cacheKey = this.generateCacheKey(parsedData);
      let cachedParams = null;
      if (this.parameterCache.has(cacheKey)) {
        Logger.debug(`使用缓存的参数配置`, 'DoubaoImageService');
        cachedParams = this.parameterCache.get(cacheKey);
      }

      // 获取历史消息
      const { messagesHistory } = await buildMessageFromParentMessageId(
        {
          groupId: inputs.groupId,
          systemMessage: '',
          maxModelTokens: 8000,
          maxRounds: 5,
          isImageUpload: 2,
          isConvertToBase64: 0,
        },
        this.chatLogService,
      );

      // 分析用户意图和优化提示词（带进度反馈）
      result.status = 2;
      result.content = '正在分析您的需求并优化提示词...';

      const {
        drawPrompt,
        completionReply,
        isEditOperation,
        currentImageUrl,
        maskUrl,
        doubaoParams,
      } = await this.analyzeIntentAndEnhancePrompt(parsedData, messagesHistory, inputs.prompt);

      // 缓存优化后的参数
      if (doubaoParams && Object.keys(doubaoParams).length > 0) {
        this.updateParameterCache(cacheKey, doubaoParams);
      }

      Logger.log(`生成模式: ${isEditOperation ? '图生图/编辑' : '文生图'}`, 'DoubaoImageService');

      // 更新进度
      result.content = '正在调用AI生成图像...';

      if (isEditOperation) {
        // 根据操作类型选择不同的处理方式
        const operationType = doubaoParams.operation_type;

        if (operationType === 'controlnet_v2') {
          // 图生图（ControlNet v2.0）
          Logger.log('使用ControlNet v2.0进行图生图', 'DoubaoImageService');
          await this.generateDoubaoControlNet(
            inputs,
            drawPrompt,
            completionReply,
            doubaoParams,
            currentImageUrl,
            result,
            tempFiles,
            requestId,
          );
        } else if (operationType === 'inpainting') {
          // 涂抹编辑
          Logger.log('使用Inpainting进行涂抹编辑', 'DoubaoImageService');
          await this.generateDoubaoInpainting(
            inputs,
            drawPrompt,
            completionReply,
            doubaoParams,
            currentImageUrl,
            maskUrl,
            result,
            tempFiles,
            requestId,
          );
        } else {
          // 默认使用文生图
          Logger.log('操作类型未明确，使用文生图模式', 'DoubaoImageService');
          await this.generateDoubaoTextToImage(
            inputs,
            drawPrompt,
            completionReply,
            doubaoParams,
            result,
            tempFiles,
            requestId,
          );
        }
      } else {
        // 文生图
        await this.generateDoubaoTextToImage(
          inputs,
          drawPrompt,
          completionReply,
          doubaoParams,
          result,
          tempFiles,
          requestId,
        );
      }

      // 性能统计
      const duration = Date.now() - startTime;
      Logger.log(`请求完成，耗时: ${duration}ms`, 'DoubaoImageService');

      return result;
    } catch (error) {
      await this.handleDoubaoError(error, result, tempFiles, inputs.onFailure, requestId);
      return result;
    } finally {
      this.currentRequests--;
      Logger.debug(`请求结束，当前并发数: ${this.currentRequests}`, 'DoubaoImageService');
    }
  }

  // 将宽高比转换为具体像素尺寸
  private convertAspectRatioToSize(aspectRatio: string): { width: number; height: number } {
    if (!aspectRatio) return { width: 512, height: 512 };

    // 如果已经是具体数值，直接返回默认值
    if (typeof aspectRatio === 'number' || aspectRatio.includes('x')) {
      return { width: 512, height: 512 };
    }

    // 豆包支持的宽高比到像素尺寸映射（范围256-768）
    const aspectRatioMap = {
      auto: { width: 512, height: 512 }, // 正方形
      '4:3': { width: 640, height: 480 }, // 横图 4:3
      '3:4': { width: 480, height: 640 }, // 竖图 3:4
      '16:9': { width: 720, height: 405 }, // 横图 16:9
      '9:16': { width: 405, height: 720 }, // 竖图 9:16
    };

    const convertedSize = aspectRatioMap[aspectRatio] || { width: 512, height: 512 };
    Logger.log(
      `转换宽高比 ${aspectRatio} -> ${convertedSize.width}x${convertedSize.height} (豆包)`,
      'DoubaoImageService',
    );
    return convertedSize;
  }

  // 解析和验证豆包输入参数（优化版）
  private async parseAndValidateDoubaoInputs(inputs) {
    const { apiKey, model, proxyUrl, prompt, extraParam, imageUrl: originalImageUrl } = inputs;

    // 参数验证
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
      throw new Error('提示词不能为空');
    }

    if (prompt.length > 2000) {
      Logger.warn('提示词过长，将自动截取前2000个字符', 'DoubaoImageService');
      inputs.prompt = prompt.substring(0, 2000);
    }

    // 🔍 添加详细调试信息
    Logger.debug(
      `=== DoubaoImageService parseAndValidateDoubaoInputs 调试信息 ===`,
      'DoubaoImageService',
    );
    Logger.debug(`原始imageUrl类型: ${typeof originalImageUrl}`, 'DoubaoImageService');
    Logger.debug(`原始imageUrl内容: ${JSON.stringify(originalImageUrl)}`, 'DoubaoImageService');

    // 初始化变量
    let currentImageUrl = originalImageUrl;
    let maskUrl = null;
    let hasImageData = false;
    let parsedImageUrl = null;

    // 解析imageUrl数据(如果存在)
    if (currentImageUrl) {
      try {
        Logger.debug(`开始解析豆包图片URL数据...`, 'DoubaoImageService');

        if (
          typeof currentImageUrl === 'string' &&
          (currentImageUrl.startsWith('{') || currentImageUrl.startsWith('['))
        ) {
          Logger.debug(`检测到JSON格式的imageUrl，开始解析...`, 'DoubaoImageService');
          parsedImageUrl = JSON.parse(currentImageUrl);
          Logger.debug(`JSON解析成功: ${JSON.stringify(parsedImageUrl)}`, 'DoubaoImageService');

          if (parsedImageUrl) {
            if (Array.isArray(parsedImageUrl)) {
              Logger.debug(
                `imageUrl是数组格式，包含${parsedImageUrl.length}个元素`,
                'DoubaoImageService',
              );
              hasImageData = parsedImageUrl.length > 0;
              // 对于数组，保持原始数组结构用于后续处理
              currentImageUrl = parsedImageUrl;
            } else if (parsedImageUrl.imageUrls && Array.isArray(parsedImageUrl.imageUrls)) {
              Logger.debug(
                `imageUrl包含imageUrls字段，包含${parsedImageUrl.imageUrls.length}个图片`,
                'DoubaoImageService',
              );

              // 🔧 改进：处理所有图片，不只是找第一个
              const allImages = parsedImageUrl.imageUrls.filter(
                img => img.type === 'image' || !img.type,
              );
              const masks = parsedImageUrl.imageUrls.filter(img => img.type === 'mask');

              Logger.debug(
                `找到${allImages.length}张图片，${masks.length}个蒙版`,
                'DoubaoImageService',
              );

              if (allImages.length > 0) {
                // 如果有多张图片，保存为数组；单张图片直接保存URL
                currentImageUrl = allImages.length === 1 ? allImages[0].url : allImages;
                hasImageData = true;
                Logger.debug(
                  `设置图片数据: ${JSON.stringify(currentImageUrl)}`,
                  'DoubaoImageService',
                );
              }

              if (masks.length > 0) {
                maskUrl = masks[0].url; // 取第一个蒙版
                Logger.debug(`设置蒙版URL: ${maskUrl}`, 'DoubaoImageService');
              }
            } else if (parsedImageUrl.url) {
              Logger.debug(`imageUrl包含单个url字段`, 'DoubaoImageService');
              currentImageUrl = parsedImageUrl.url;
              hasImageData = true;
            }
          }
        } else if (
          typeof currentImageUrl === 'string' &&
          (currentImageUrl.startsWith('http') || currentImageUrl.startsWith('/file/'))
        ) {
          Logger.debug(`检测到直接URL格式的imageUrl`, 'DoubaoImageService');
          hasImageData = true;
        } else if (Array.isArray(currentImageUrl)) {
          Logger.debug(
            `imageUrl已经是数组格式，包含${currentImageUrl.length}个元素`,
            'DoubaoImageService',
          );
          hasImageData = currentImageUrl.length > 0;
        } else {
          Logger.debug(`imageUrl格式未识别: ${typeof currentImageUrl}`, 'DoubaoImageService');
        }

        Logger.debug(
          `解析完成 - hasImageData: ${hasImageData}, currentImageUrl类型: ${typeof currentImageUrl}`,
          'DoubaoImageService',
        );
      } catch (error) {
        Logger.error(`解析imageUrl JSON失败: ${error}`, 'DoubaoImageService');
        Logger.debug(`错误详情: ${error.stack}`, 'DoubaoImageService');
        hasImageData = typeof currentImageUrl === 'string' && currentImageUrl.length > 0;
      }
    }

    // 从extraParam获取mask数据(如果有)
    if (extraParam && extraParam.mask && !maskUrl) {
      maskUrl = extraParam.mask;
      Logger.debug(`从extraParam获取到mask数据`, 'DoubaoImageService');
    }

    // 参数范围验证和自动修正（按照豆包官方文档）
    if (extraParam) {
      // width: [256, 768]
      if (extraParam.width && (extraParam.width < 256 || extraParam.width > 768)) {
        Logger.warn(`width参数超出范围(${extraParam.width})，自动调整为512`, 'DoubaoImageService');
        extraParam.width = 512;
      }
      // height: [256, 768]
      if (extraParam.height && (extraParam.height < 256 || extraParam.height > 768)) {
        Logger.warn(
          `height参数超出范围(${extraParam.height})，自动调整为512`,
          'DoubaoImageService',
        );
        extraParam.height = 512;
      }
      // scale: [1, 10]
      if (extraParam.scale && (extraParam.scale < 1 || extraParam.scale > 10)) {
        Logger.warn(`scale参数超出范围(${extraParam.scale})，自动调整为3.5`, 'DoubaoImageService');
        extraParam.scale = 3.5;
      }
      // ddim_steps: [1, 200]，推荐 [1, 50]
      if (extraParam.ddim_steps && (extraParam.ddim_steps < 1 || extraParam.ddim_steps > 200)) {
        Logger.warn(
          `ddim_steps参数超出范围(${extraParam.ddim_steps})，自动调整为25`,
          'DoubaoImageService',
        );
        extraParam.ddim_steps = 25;
      }
      // 如果ddim_steps超过推荐范围，给出警告
      if (extraParam.ddim_steps && extraParam.ddim_steps > 50) {
        Logger.warn(
          `ddim_steps(${extraParam.ddim_steps})超过推荐范围[1,50]，可能导致延迟增加`,
          'DoubaoImageService',
        );
      }
    }

    return {
      apiKey,
      model,
      proxyUrl,
      prompt: inputs.prompt, // 使用可能被截取的提示词
      extraParam,
      currentImageUrl,
      maskUrl,
      hasImageData,
    };
  }

  // 生成缓存键
  private generateCacheKey(parsedData): string {
    const keyData = {
      prompt: parsedData.prompt,
      hasImageData: parsedData.hasImageData,
      extraParam: parsedData.extraParam,
    };
    return Buffer.from(JSON.stringify(keyData)).toString('base64').substring(0, 32);
  }

  // 更新参数缓存
  private updateParameterCache(key: string, params: any) {
    if (this.parameterCache.size >= this.maxCacheSize) {
      // 删除最旧的缓存项
      const firstKey = this.parameterCache.keys().next().value;
      this.parameterCache.delete(firstKey);
    }
    this.parameterCache.set(key, params);
    Logger.debug(`参数缓存已更新，当前缓存数: ${this.parameterCache.size}`, 'DoubaoImageService');
  }

  // 分析用户意图和优化提示词（重构为三个专门的工具）
  private async analyzeIntentAndEnhancePrompt(parsedData, messagesHistory, originalPrompt) {
    let { currentImageUrl, maskUrl, hasImageData } = parsedData;
    let isEditOperation = hasImageData;
    let drawPrompt = originalPrompt;
    let completionReply = '';
    let doubaoParams: any = {};

    // 获取Function Calling配置
    const {
      toolCallUrl,
      toolCallKey,
      toolCallModel,
      isDalleChat,
      openaiBaseKey,
      openaiBaseUrl,
      openaiBaseModel,
    } = await this.globalConfigService.getConfigs([
      'toolCallUrl',
      'toolCallKey',
      'toolCallModel',
      'openaiBaseKey',
      'openaiBaseUrl',
      'openaiBaseModel',
      'isDalleChat',
    ]);

    // 创建OpenAI客户端用于Function Calling
    const { correctApiBaseUrl } = await import('@/common/utils/correctApiBaseUrl');
    const OpenAI = (await import('openai')).default;

    const openai = new OpenAI({
      apiKey: toolCallKey || openaiBaseKey,
      baseURL: await correctApiBaseUrl(toolCallUrl || openaiBaseUrl),
      timeout: parsedData.timeout,
    });

    if (hasImageData) {
      // 已有图像数据 - 先检测是否有蒙版，决定使用哪个工具
      Logger.log('已有图像数据，分析图像类型选择对应工具', 'DoubaoImageService');

      // 检测是否包含蒙版
      let hasMask = false;
      if (Array.isArray(currentImageUrl)) {
        hasMask = currentImageUrl.some(img => img.type === 'mask');
      } else if (typeof currentImageUrl === 'string' && currentImageUrl.includes('"type":"mask"')) {
        hasMask = true;
      }

      if (hasMask) {
        // 使用蒙版处理工具
        Logger.log('检测到蒙版，使用涂抹编辑工具', 'DoubaoImageService');
        const result = await this.processInpaintingWithTool(
          openai,
          toolCallModel || openaiBaseModel,
          originalPrompt,
          currentImageUrl,
          messagesHistory,
        );
        drawPrompt = result.drawPrompt;
        completionReply = result.completionReply;
        doubaoParams = { ...result.doubaoParams, operation_type: 'inpainting' };
      } else {
        // 使用图生图工具
        Logger.log('未检测到蒙版，使用图生图工具', 'DoubaoImageService');
        const result = await this.processControlNetWithTool(
          openai,
          toolCallModel || openaiBaseModel,
          originalPrompt,
          currentImageUrl,
          messagesHistory,
        );
        drawPrompt = result.drawPrompt;
        completionReply = result.completionReply;
        doubaoParams = { ...result.doubaoParams, operation_type: 'controlnet_v2' };
      }
    } else {
      // 无图像数据 - 使用智能判断工具
      Logger.log('无图像数据，使用智能判断工具分析用户意图', 'DoubaoImageService');
      const result = await this.processIntentAnalysisWithTool(
        openai,
        toolCallModel || openaiBaseModel,
        originalPrompt,
        messagesHistory,
      );

      drawPrompt = result.drawPrompt;
      completionReply = result.completionReply;
      doubaoParams = result.doubaoParams;

      // 根据分析结果设置图像数据和操作类型
      if (
        result.intentType === 'history_based_image_to_image' &&
        result.referenceImages &&
        result.referenceImages.length > 0
      ) {
        isEditOperation = true;
        hasImageData = true;
        currentImageUrl = result.referenceImages;
        doubaoParams.operation_type = 'controlnet_v2';
        doubaoParams.controlnet_config = result.controlnetConfig;
      } else {
        doubaoParams.operation_type = 'text_to_image';
      }
    }

    // 最终确认操作类型和备用逻辑
    if (hasImageData) {
      isEditOperation = true;

      // 备用逻辑：如果Function Calling没有正确设置operation_type，我们自己分析
      if (!doubaoParams.operation_type) {
        Logger.log('Function Calling未设置操作类型，启用备用分析逻辑', 'DoubaoImageService');

        // 分析图像数据中是否包含蒙版
        let hasMask = false;
        if (Array.isArray(currentImageUrl)) {
          hasMask = currentImageUrl.some(img => img.type === 'mask');
        } else if (
          typeof currentImageUrl === 'string' &&
          currentImageUrl.includes('"type":"mask"')
        ) {
          hasMask = true;
        }

        if (hasMask) {
          doubaoParams.operation_type = 'inpainting';
          Logger.log('检测到蒙版数据，自动设置为涂抹编辑模式', 'DoubaoImageService');
        } else {
          doubaoParams.operation_type = 'controlnet_v2';
          Logger.log('未检测到蒙版，自动设置为图生图模式', 'DoubaoImageService');
        }
      }
    }

    Logger.log(
      `确定操作类型: ${isEditOperation ? '编辑图像' : '生成新图像'}, 具体模式: ${
        doubaoParams.operation_type || '文生图'
      }`,
      'DoubaoImageService',
    );

    return {
      drawPrompt,
      completionReply,
      isEditOperation,
      currentImageUrl,
      maskUrl,
      hasImageData,
      doubaoParams,
    };
  }

  // 蒙版处理工具 - 重点格式化参数，几乎不润色提示词
  private async processInpaintingWithTool(
    openai,
    model,
    originalPrompt,
    imageData,
    messagesHistory,
  ) {
    const systemMessage =
      '你是涂抹编辑专家。当用户提供了蒙版数据时，你的任务是：\n' +
      '1. 保持用户原始提示词，仅做最小必要的格式调整\n' +
      '2. 针对涂抹编辑优化参数配置\n' +
      '3. 生成简洁的完成回复\n\n' +
      '涂抹编辑特点：\n' +
      '- 局部重绘，保持原图其他区域不变\n' +
      '- 提示词应该描述要替换的内容，而不是整个画面\n' +
      '- 参数推荐：scale 5-8，steps 25-35\n' +
      '- 通常不建议添加水印\n\n' +
      '请保持用户提示词的原意，只做必要的格式整理。';

    const userMessageContent = `
用户提示词: "${originalPrompt}"
图像数据: ${JSON.stringify(imageData)}
历史消息: ${JSON.stringify(messagesHistory)}

请为涂抹编辑优化参数并生成回复。
`;

    try {
      const response = await openai.chat.completions.create({
        model: model,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessageContent },
        ],
        tools: [doubaoInpaintingTool],
        tool_choice: 'auto',
      });

      const toolCalls = response.choices[0]?.message?.tool_calls;
      if (toolCalls && toolCalls.length > 0) {
        const functionArgs = JSON.parse(toolCalls[0].function.arguments);

        return {
          drawPrompt: functionArgs.original_prompt || originalPrompt,
          completionReply: functionArgs.completion_reply || '涂抹编辑已完成',
          doubaoParams: functionArgs.inpainting_params || {},
        };
      }
    } catch (error) {
      Logger.error(`涂抹编辑工具调用失败: ${error}`, 'DoubaoImageService');
    }

    return {
      drawPrompt: originalPrompt,
      completionReply: '涂抹编辑已完成',
      doubaoParams: {},
    };
  }

  // 图生图工具 - 既要格式化参数，也要根据上下文生成准确提示词
  private async processControlNetWithTool(
    openai,
    model,
    originalPrompt,
    imageData,
    messagesHistory,
  ) {
    const systemMessage =
      '你是图生图专家。当用户提供了参考图像（无蒙版）时，你的任务是：\n' +
      '1. 按照【艺术风格】+【主体描述】+【文字排版】模板优化提示词\n' +
      '2. 根据参考图像特征选择合适的ControlNet类型\n' +
      '3. 优化图生图参数配置\n' +
      '4. 智能分析用户意图和参考图像，推荐最佳图片尺寸\n' +
      '5. 生成专业的完成回复\n\n' +
      'ControlNet类型选择：\n' +
      '- canny：适合保持轮廓边缘，线条清晰的图像\n' +
      '- depth：适合保持景深层次，有明显前后关系的图像\n' +
      '- pose：适合人物姿态，保持人体动作结构\n\n' +
      '图片尺寸推荐规则：\n' +
      '- 优先考虑保持与参考图像相同或相近的宽高比\n' +
      '- 分析用户意图中是否明确提到了尺寸要求（如"正方形"、"竖图"、"横图"等）\n' +
      '- 根据图像内容类型推荐：人像通常适合竖图(如384x640)，风景适合横图(如640x384)，产品展示适合正方形(如512x512)\n' +
      '- 复杂场景需要更大尺寸(如640x640或768x768)，简单场景可以使用较小尺寸(如384x384)\n' +
      '- 提供size_reasoning字段，解释选择此尺寸的理由\n\n' +
      '参数推荐：\n' +
      '- scale: 3-6（图生图推荐范围）\n' +
      '- ddim_steps: 16-25（图生图推荐范围）\n' +
      '- strength: 0.6-0.8（ControlNet强度）\n' +
      '- use_rephraser: true（推荐开启提示词重写）\n\n' +
      '请结合参考图像内容和用户需求，生成优化的提示词。';

    const userMessageContent = `
用户提示词: "${originalPrompt}"
参考图像: ${JSON.stringify(imageData)}
历史消息: ${JSON.stringify(messagesHistory)}

请分析参考图像特征，优化提示词并配置ControlNet参数。
同时，请根据用户意图和参考图像特征，智能推荐最佳的图片尺寸。
`;

    try {
      const response = await openai.chat.completions.create({
        model: model,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessageContent },
        ],
        tools: [doubaoControlNetTool],
        tool_choice: 'auto',
      });

      const toolCalls = response.choices[0]?.message?.tool_calls;
      if (toolCalls && toolCalls.length > 0) {
        const functionArgs = JSON.parse(toolCalls[0].function.arguments);

        // 记录尺寸推荐理由
        if (functionArgs.controlnet_params?.size_reasoning) {
          Logger.log(
            `图生图智能尺寸推荐: ${functionArgs.controlnet_params.width}x${functionArgs.controlnet_params.height}, 理由: ${functionArgs.controlnet_params.size_reasoning}`,
            'DoubaoImageService',
          );
        }

        return {
          drawPrompt: functionArgs.enhanced_prompt || originalPrompt,
          completionReply: functionArgs.completion_reply || '图生图处理已完成',
          doubaoParams: {
            ...functionArgs.controlnet_params,
            controlnet_config: functionArgs.controlnet_config,
          },
        };
      }
    } catch (error) {
      Logger.error(`图生图工具调用失败: ${error}`, 'DoubaoImageService');
    }

    return {
      drawPrompt: originalPrompt,
      completionReply: '图生图处理已完成',
      doubaoParams: {},
    };
  }

  // 智能判断工具 - 分析历史消息判断是纯文生图还是基于历史图像的图生图
  private async processIntentAnalysisWithTool(openai, model, originalPrompt, messagesHistory) {
    const systemMessage =
      '你是意图分析专家。当用户没有直接提供图像时，你需要：\n' +
      '1. 分析用户提示词和历史消息\n' +
      '2. 判断是纯文生图还是想基于历史图像进行图生图\n' +
      '3. 如果是基于历史图像，提取最相关的图像\n' +
      '4. 按照【艺术风格】+【主体描述】+【文字排版】模板优化提示词\n' +
      '5. 推荐合适的生成参数\n' +
      '6. 智能分析用户意图和图像内容类型，推荐最佳图片尺寸\n\n' +
      '判断规则：\n' +
      '- 如果用户明确提到"基于上一张图"、"参考刚才的图"等，选择history_based_image_to_image\n' +
      '- 如果用户提到"修改"、"调整"、"改变"现有图像，选择history_based_image_to_image\n' +
      '- 如果用户要求全新创作，选择pure_text_to_image\n\n' +
      '提示词模板：【艺术风格】+【主体描述】+【文字排版】\n' +
      '- 艺术风格：写实风、插画风、中国水墨风、油画风等\n' +
      '- 主体描述：人物动作、物品形态、场景布局等\n' +
      '- 文字排版：文字内容用双引号""，描述位置、大小、颜色\n\n' +
      '图片尺寸推荐规则：\n' +
      '- 分析用户意图中是否明确提到了尺寸要求（如"正方形"、"竖图"、"横图"等）\n' +
      '- 根据图像内容类型推荐：人像通常适合竖图(如384x640)，风景适合横图(如640x384)，产品展示适合正方形(如512x512)\n' +
      '- 复杂场景需要更大尺寸(如640x640或768x768)，简单场景可以使用较小尺寸(如384x384)\n' +
      '- 提供size_reasoning字段，解释选择此尺寸的理由\n\n' +
      '参数推荐：\n' +
      '- 文生图：width/height 根据内容智能推荐，scale 3.5，ddim_steps 25\n' +
      '- 图生图：scale 3-6，ddim_steps 16-25，strength 0.6-0.8';

    const userMessageContent = `
用户提示词: "${originalPrompt}"
历史消息: ${JSON.stringify(messagesHistory)}

请分析用户意图，判断是纯文生图还是基于历史图像的图生图，并优化提示词。
同时，请根据用户意图和图像内容类型，智能推荐最佳的图片尺寸。
`;

    try {
      const response = await openai.chat.completions.create({
        model: model,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessageContent },
        ],
        tools: [doubaoIntentAnalysisTool],
        tool_choice: 'auto',
      });

      const toolCalls = response.choices[0]?.message?.tool_calls;
      if (toolCalls && toolCalls.length > 0) {
        const functionArgs = JSON.parse(toolCalls[0].function.arguments);

        // 记录尺寸推荐理由
        if (functionArgs.generation_params?.size_reasoning) {
          Logger.log(
            `智能尺寸推荐: ${functionArgs.generation_params.width}x${functionArgs.generation_params.height}, 理由: ${functionArgs.generation_params.size_reasoning}`,
            'DoubaoImageService',
          );
        }

        return {
          drawPrompt: functionArgs.enhanced_prompt || originalPrompt,
          completionReply: functionArgs.completion_reply || '图像生成已完成',
          doubaoParams: functionArgs.generation_params || {},
          intentType: functionArgs.intent_type,
          referenceImages: functionArgs.reference_images || [],
          controlnetConfig: functionArgs.controlnet_config,
        };
      }
    } catch (error) {
      Logger.error(`意图分析工具调用失败: ${error}`, 'DoubaoImageService');
    }

    return {
      drawPrompt: originalPrompt,
      completionReply: '图像生成已完成',
      doubaoParams: {},
      intentType: 'pure_text_to_image',
      referenceImages: [],
      controlnetConfig: null,
    };
  }

  // 生成豆包文生图
  private async generateDoubaoTextToImage(
    inputs,
    drawPrompt,
    completionReply,
    doubaoParams,
    result,
    tempFiles,
    requestId,
  ) {
    const { apiKey, model, proxyUrl, extraParam, timeout, onSuccess } = inputs;

    Logger.log(`执行文生图操作 [${requestId}]`, 'DoubaoImageService');

    // 解析配置
    const doubaoConfig = this.parseDoubaoConfig(apiKey, proxyUrl);
    if (!doubaoConfig.accessKeyId || !doubaoConfig.secretAccessKey) {
      throw new Error('配置不完整，请检查apiKey格式（应为accessKeyId|secretAccessKey）');
    }

    // 合并参数：extraParam > doubaoParams > 默认值
    const finalParams = this.mergeDoubaoParams(extraParam, doubaoParams);

    Logger.log(`最终提示词: "${drawPrompt}"`, 'DoubaoImageService');
    Logger.debug(`生成参数: ${JSON.stringify(finalParams)}`, 'DoubaoImageService');

    // 构建请求参数（按照官方示例优化）
    const requestBody = {
      req_key: 'high_aes_general_v21_L', // 算法名称，按照官方示例
      prompt: drawPrompt, // 用于生成图像的提示词
      seed: finalParams.seed || -1, // 随机种子，默认-1
      scale: finalParams.scale || 3.5, // 影响文本描述的程度，默认3.5
      ddim_steps: finalParams.ddim_steps || 25, // 生成图像的步数，默认25
      width: finalParams.width || 512, // 生成图像的宽，默认512
      height: finalParams.height || 512, // 生成图像的高，默认512
      use_pre_llm: finalParams.use_pre_llm !== false, // 开启文本扩写，默认true
      use_sr: finalParams.use_sr !== false, // 文生图+AIGC超分，默认true
      return_url: true, // 输出是否返回图片链接
      logo_info: {
        add_logo: finalParams.add_logo || false,
        position: finalParams.logo_position || 0,
        language: finalParams.logo_language || 0,
        opacity: finalParams.logo_opacity || 0.3,
        logo_text_content: finalParams.logo_text_content || '',
      },
    };

    // 从baseUrl中提取host
    const hostFromUrl = new URL(doubaoConfig.baseUrl).hostname;
    Logger.debug(`请求Host: ${hostFromUrl}`, 'DoubaoImageService');

    // 生成签名（按照官方示例优化）
    const signature = generateDoubaoSignature(
      {
        accessKeyId: doubaoConfig.accessKeyId,
        secretAccessKey: doubaoConfig.secretAccessKey,
        region: doubaoConfig.region,
        service: doubaoConfig.service,
      },
      {
        method: 'POST',
        uri: '/',
        queryParams: {
          Action: 'HighAesGeneralV21L', // 按照官方示例格式
          Version: '2024-06-06', // 按照官方示例版本
        },
        headers: {
          'Content-Type': 'application/json',
        },
        payload: JSON.stringify(requestBody),
        host: hostFromUrl, // 通过host参数传递，确保签名使用正确的host
      },
    );

    Logger.debug(`签名头部: ${JSON.stringify(signature.headers)}`, 'DoubaoImageService');
    Logger.debug(`请求体: ${JSON.stringify(requestBody)}`, 'DoubaoImageService');

    // 更新进度
    result.content = '正在连接服务器...';

    // 发送请求（带重试机制，按照官方示例优化）
    const response = await this.makeDoubaoRequestWithRetry(
      `${doubaoConfig.baseUrl}?Action=HighAesGeneralV21L&Version=2024-06-06`,
      requestBody,
      signature.headers,
      timeout || 60000,
      requestId,
    );

    Logger.log(`API调用成功`, 'DoubaoImageService');

    // 记录响应状态（简化版）
    Logger.debug(`API响应状态: ${response.status}`, 'DoubaoImageService');

    this.logDoubaoApiResponse(response.data);

    // 更新进度
    result.content = '正在处理生成的图像...';

    // 处理响应并上传图片
    await this.processDoubaoResponseAndUpload(
      response.data,
      result,
      completionReply,
      onSuccess,
      requestId,
    );
  }

  // 带重试机制的豆包请求
  private async makeDoubaoRequestWithRetry(
    url: string,
    requestBody: any,
    headers: any,
    timeout: number,
    requestId: string,
    maxRetries: number = 2,
  ) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        Logger.debug(`豆包API请求尝试 ${attempt}/${maxRetries}`, 'DoubaoImageService');

        const response = await axios.post(url, requestBody, {
          headers,
          timeout,
        });

        return response;
      } catch (error) {
        lastError = error;
        Logger.warn(
          `豆包API请求失败，尝试 ${attempt}/${maxRetries}: ${error.message}`,
          'DoubaoImageService',
        );

        if (attempt < maxRetries) {
          // 指数退避重试
          const delay = Math.pow(2, attempt) * 1000;
          Logger.debug(`等待 ${delay}ms 后重试`, 'DoubaoImageService');
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  // 解析豆包配置
  private parseDoubaoConfig(apiKey: string, proxyUrl?: string) {
    if (!apiKey || typeof apiKey !== 'string') {
      throw new Error('apiKey不能为空');
    }

    // 解析apiKey：accessKeyId|secretAccessKey
    const keyParts = apiKey.split('|');
    if (keyParts.length !== 2) {
      throw new Error('apiKey格式错误，应为：accessKeyId|secretAccessKey');
    }

    const [accessKeyId, secretAccessKey] = keyParts;
    if (!accessKeyId.trim() || !secretAccessKey.trim()) {
      throw new Error('accessKeyId和secretAccessKey都不能为空');
    }

    // 确定baseUrl
    const baseUrl = proxyUrl || 'https://visual.volcengineapi.com';

    const config = {
      accessKeyId: accessKeyId.trim(),
      secretAccessKey: secretAccessKey.trim(),
      region: 'cn-north-1', // 固定值：华北1（北京）
      service: 'cv', // 固定值：计算机视觉服务
      baseUrl: baseUrl,
    };

    Logger.debug(`豆包配置解析完成`, 'DoubaoImageService');

    return config;
  }

  // 合并豆包参数（按照官方文档）
  private mergeDoubaoParams(extraParam, doubaoParams) {
    const defaultParams = {
      width: 512,
      height: 512,
      scale: 3.5,
      ddim_steps: 25,
      use_pre_llm: true,
      use_sr: true,
      seed: -1,
      add_logo: false,
      logo_position: 0,
      logo_language: 0,
      logo_opacity: 0.3,
      logo_text_content: '',
    };

    // 处理size参数转换
    let sizeParams: Partial<{ width: number; height: number }> = {};
    if (extraParam?.size) {
      // 如果extraParam中有size参数，转换为width/height
      sizeParams = this.convertAspectRatioToSize(extraParam.size);
      Logger.debug(
        `从extraParam.size转换尺寸: ${extraParam.size} -> ${sizeParams.width}x${sizeParams.height}`,
        'DoubaoImageService',
      );
    }

    // 优先级：extraParam > sizeParams > doubaoParams > defaultParams
    const finalParams = {
      ...defaultParams,
      ...doubaoParams,
      ...sizeParams,
      ...extraParam,
    };

    // 移除size参数，避免传递给API
    if (finalParams.size) {
      delete finalParams.size;
    }

    // 记录尺寸信息
    const sizeSource = extraParam?.width
      ? 'extraParam直接指定'
      : extraParam?.size
      ? `extraParam.size转换(${extraParam.size})`
      : doubaoParams?.width
      ? 'doubaoParams'
      : 'defaultParams';

    Logger.debug(
      `图像尺寸: ${finalParams.width}x${finalParams.height}, 来源: ${sizeSource}${
        doubaoParams?.size_reasoning ? ', 推荐理由: ' + doubaoParams.size_reasoning : ''
      }`,
      'DoubaoImageService',
    );

    return finalParams;
  }

  // 合并豆包ControlNet参数
  private mergeDoubaoControlNetParams(extraParam, doubaoParams) {
    const defaultParams = {
      seed: -1,
      scale: 3.0,
      ddim_steps: 16,
      use_rephraser: true,
      controlnet_type: 'depth',
      controlnet_strength: 0.6,
      add_logo: false,
      logo_position: 0,
      logo_language: 0,
      logo_text_content: '',
      width: 512,
      height: 512,
    };

    // 处理size参数转换
    let sizeParams: Partial<{ width: number; height: number }> = {};
    if (extraParam?.size) {
      // 如果extraParam中有size参数，转换为width/height
      sizeParams = this.convertAspectRatioToSize(extraParam.size);
      Logger.debug(
        `从extraParam.size转换ControlNet尺寸: ${extraParam.size} -> ${sizeParams.width}x${sizeParams.height}`,
        'DoubaoImageService',
      );
    }

    // 从doubaoParams中提取ControlNet配置
    const controlnetConfig = doubaoParams.controlnet_config || {};

    // 优先级：extraParam > sizeParams > doubaoParams > controlnetConfig > defaultParams
    const finalParams = {
      ...defaultParams,
      ...controlnetConfig,
      ...doubaoParams,
      ...sizeParams,
      ...extraParam,
      controlnet_type:
        controlnetConfig.type || extraParam?.controlnet_type || defaultParams.controlnet_type,
      controlnet_strength:
        controlnetConfig.strength ||
        extraParam?.controlnet_strength ||
        defaultParams.controlnet_strength,
    };

    // 移除size参数，避免传递给API
    if (finalParams.size) {
      delete finalParams.size;
    }

    // 记录尺寸信息
    const sizeSource = extraParam?.width
      ? 'extraParam直接指定'
      : extraParam?.size
      ? `extraParam.size转换(${extraParam.size})`
      : doubaoParams?.width
      ? 'doubaoParams'
      : 'defaultParams';

    Logger.debug(
      `ControlNet图像尺寸: ${finalParams.width}x${finalParams.height}, 来源: ${sizeSource}${
        doubaoParams?.size_reasoning ? ', 推荐理由: ' + doubaoParams.size_reasoning : ''
      }`,
      'DoubaoImageService',
    );

    return finalParams;
  }

  // 合并豆包Inpainting参数
  private mergeDoubaoInpaintingParams(extraParam, doubaoParams) {
    const defaultParams = {
      seed: -1,
      scale: 5,
      steps: 25,
      add_logo: false,
      logo_position: 0,
      logo_language: 0,
      logo_opacity: 0.3,
      logo_text_content: '',
      // 涂抹编辑通常保持原图尺寸，但如果需要可以在这里设置默认值
    };

    // 优先级：extraParam > doubaoParams > defaultParams
    const finalParams = {
      ...defaultParams,
      ...doubaoParams,
      ...extraParam,
    };

    // Inpainting通常不需要指定尺寸，因为会保持原图尺寸
    // 但如果有明确指定，记录一下
    if (extraParam?.width || doubaoParams?.width) {
      const sizeSource = extraParam?.width ? 'extraParam' : 'doubaoParams';
      Logger.debug(
        `涂抹编辑指定了尺寸: ${finalParams.width}x${finalParams.height}, 来源: ${sizeSource}`,
        'DoubaoImageService',
      );
    }

    return finalParams;
  }

  // 记录豆包API响应（精简版）
  private logDoubaoApiResponse(response, requestId?: string) {
    // API的状态信息在Result中
    const resultData = response?.Result || response?.result;
    const code = resultData?.code || resultData?.status || response?.code || response?.status_code;
    const message = resultData?.message || resultData?.status_message || response?.message;

    Logger.log(`API响应状态: ${code}`, 'DoubaoImageService');
    if (message && message !== 'Success') {
      Logger.log(`API响应消息: ${message}`, 'DoubaoImageService');
    }

    // API的数据在Result.data中
    const dataSource = resultData?.data || response?.data || response;

    if (dataSource) {
      const imageCount = dataSource.image_urls?.length || 0;
      const base64Count = dataSource.binary_data_base64?.length || 0;

      if (imageCount > 0) {
        Logger.log(`返回图片URL数量: ${imageCount}`, 'DoubaoImageService');
      }
      if (base64Count > 0) {
        Logger.log(`返回Base64数量: ${base64Count}`, 'DoubaoImageService');
      }

      if (dataSource.rephraser_result) {
        Logger.debug(`提示词重写结果: ${dataSource.rephraser_result}`, 'DoubaoImageService');
      }
    }
  }

  // 处理豆包API响应并上传图片（优化版）
  private async processDoubaoResponseAndUpload(
    response,
    result,
    completionReply,
    onSuccess,
    requestId?: string,
  ) {
    Logger.log(`处理API响应并上传生成的图片`, 'DoubaoImageService');

    // API响应结构分析：response.data.Result.data 包含实际数据
    const resultData = response?.Result || response?.result;
    const code = resultData?.code || resultData?.status || response?.code || response?.status_code;
    const message = resultData?.message || resultData?.status_message || response?.message;

    Logger.debug(`状态检查: code=${code}, message=${message}`, 'DoubaoImageService');

    // 如果有明确的错误码且不是成功状态，则抛出错误
    if (code !== undefined && code !== 10000 && code !== 200 && code !== 0) {
      throw new Error(`API错误: ${message || '未知错误'} (错误码: ${code})`);
    }

    // API的数据在 Result.data 中
    const dataSource = resultData?.data || response?.data || response;

    if (!dataSource) {
      throw new Error('API响应中没有数据');
    }

    Logger.debug(
      `数据源路径: ${
        resultData?.data ? 'Result.data' : response?.data ? 'response.data' : 'response直接'
      }`,
      'DoubaoImageService',
    );

    // 使用 Date 对象获取当前日期并格式化为 YYYYMM/DD
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const currentDate = `${year}${month}/${day}`;
    Logger.debug(`图片上传目录: ${currentDate}`, 'DoubaoImageService');

    let imageUrl = '';

    // 更新进度
    result.content = '正在上传图像到存储服务...';

    // 优先使用image_urls
    if (dataSource.image_urls && dataSource.image_urls.length > 0) {
      const firstImageUrl = dataSource.image_urls[0];
      Logger.log(`返回图片URL: ${firstImageUrl}`, 'DoubaoImageService');

      try {
        // 下载图片并上传到我们的存储
        Logger.debug(`下载图片并上传到本地存储`, 'DoubaoImageService');
        const imageResponse = await axios.get(firstImageUrl, {
          responseType: 'arraybuffer',
          timeout: 30000, // 30秒超时
        });
        const buffer = Buffer.from(imageResponse.data);

        const file = {
          buffer: buffer,
          mimetype: 'image/png',
        };

        imageUrl = await this.uploadService.uploadFile(file, `images/doubao/${currentDate}`);
        Logger.log(`图片上传成功`, 'DoubaoImageService');
      } catch (downloadError) {
        Logger.error(`下载图片失败，使用原始URL: ${downloadError}`, 'DoubaoImageService');
        imageUrl = firstImageUrl;
      }
    }
    // 备选：使用binary_data_base64
    else if (dataSource.binary_data_base64 && dataSource.binary_data_base64.length > 0) {
      const base64Data = dataSource.binary_data_base64[0];
      Logger.debug(`返回Base64数据，长度: ${base64Data.length}`, 'DoubaoImageService');

      const buffer = Buffer.from(base64Data, 'base64');
      Logger.debug(`Base64转换Buffer成功，大小: ${buffer.length}字节`, 'DoubaoImageService');

      const file = {
        buffer: buffer,
        mimetype: 'image/png',
      };

      try {
        imageUrl = await this.uploadService.uploadFile(file, `images/doubao/${currentDate}`);
        Logger.log(`Base64图片上传成功`, 'DoubaoImageService');
      } catch (uploadError) {
        Logger.error(`Base64图片上传失败: ${uploadError}`, 'DoubaoImageService');
        throw new Error(`上传图片失败: ${uploadError.message || JSON.stringify(uploadError)}`);
      }
    } else {
      Logger.error(
        `响应中没有找到图像数据，数据源字段: ${Object.keys(dataSource)}`,
        'DoubaoImageService',
      );
      throw new Error('API响应中没有有效的图像数据');
    }

    result.imageUrl = imageUrl;
    result.content = completionReply || 'AI图片已创建';
    result.status = 3;

    Logger.debug(`处理完成，准备调用onSuccess回调，状态: ${result.status}`, 'DoubaoImageService');
    onSuccess(result);
    Logger.debug(`onSuccess回调执行完成`, 'DoubaoImageService');
  }

  // 处理豆包错误（精简版）
  private async handleDoubaoError(error, result, tempFiles, onFailure, requestId: string) {
    result.status = 5;
    Logger.error(`图像生成失败，状态: ${result.status}`, 'DoubaoImageService');

    try {
      // 尝试清理临时文件
      if (tempFiles && tempFiles.length > 0) {
        Logger.debug(`准备清理${tempFiles.length}个临时文件`, 'DoubaoImageService');
        this.cleanupTempFiles(tempFiles).catch(cleanupError => {
          Logger.error(`清理临时文件失败: ${cleanupError}`, 'DoubaoImageService');
        });
      }
    } catch (cleanupError) {
      Logger.error(`尝试清理临时文件时出错: ${cleanupError}`, 'DoubaoImageService');
    }

    // 提取错误信息
    const status = error?.response?.status || 500;
    const message = error?.message || '';
    const responseData = error?.response?.data;

    Logger.error(`错误详情: status=${status}, message=${message}`, 'DoubaoImageService');

    // 处理特有的错误码
    if (responseData && responseData.code) {
      const errorCode = responseData.code;
      const errorMessage = responseData.message || '';

      switch (errorCode) {
        case 50411:
          result.content = '输入图片前审核未通过，请检查图片内容是否合规';
          break;
        case 50511:
          result.content = '输出图片后审核未通过，请尝试修改提示词';
          break;
        case 50412:
          result.content = '输入文本前审核未通过，请检查提示词是否包含敏感内容';
          break;
        case 50512:
          result.content = '输出文本后审核未通过，请尝试修改提示词';
          break;
        case 50413:
          result.content = '输入文本被安全策略拦截，请修改提示词内容';
          break;
        default:
          result.content = `生成失败: ${errorMessage || '未知错误'}`;
      }
    } else if (
      message.includes('配置不完整') ||
      message.includes('apiKey格式错误') ||
      message.includes('apiKey不能为空')
    ) {
      result.content = '服务配置错误，请检查apiKey格式（应为accessKeyId|secretAccessKey）';
    } else if (message.includes('提示词不能为空')) {
      result.content = '请输入有效的图像描述内容';
    } else if (status === 401 || status === 403) {
      result.content = '服务认证失败，请检查访问密钥配置';
    } else if (status === 429) {
      result.content = '服务请求过于频繁，请稍后再试';
    } else if (status >= 500) {
      result.content = '服务暂时不可用，请稍后再试';
    } else {
      result.content = message || '图像生成失败，请稍后试试吧！';
    }

    onFailure(result);
    Logger.debug(`onFailure回调执行完成`, 'DoubaoImageService');
  }

  // 豆包ControlNet v2.0图生图
  private async generateDoubaoControlNet(
    inputs,
    drawPrompt,
    completionReply,
    doubaoParams,
    currentImageUrl,
    result,
    tempFiles,
    requestId,
  ) {
    const { apiKey, proxyUrl, timeout, onSuccess } = inputs;

    Logger.log(`执行ControlNet图生图操作 [${requestId}]`, 'DoubaoImageService');

    // 解析配置
    const doubaoConfig = this.parseDoubaoConfig(apiKey, proxyUrl);
    if (!doubaoConfig.accessKeyId || !doubaoConfig.secretAccessKey) {
      throw new Error('配置不完整，请检查apiKey格式（应为accessKeyId|secretAccessKey）');
    }

    // 准备图像URL列表
    const imageUrls = await this.prepareImageUrlList(currentImageUrl, null);
    const imageUrlsOnly = imageUrls.filter(img => img.type === 'image').map(img => img.url);

    if (imageUrlsOnly.length === 0) {
      throw new Error('ControlNet需要至少一张参考图像');
    }

    Logger.debug(`ControlNet参考图像数量: ${imageUrlsOnly.length}`, 'DoubaoImageService');

    // 合并参数：extraParam > doubaoParams > 默认值
    const finalParams = this.mergeDoubaoControlNetParams(inputs.extraParam, doubaoParams);

    // 构建ControlNet请求参数
    const requestBody = {
      req_key: 'high_aes_scheduler_svr_controlnet_v2.0',
      image_urls: imageUrlsOnly,
      prompt: drawPrompt,
      seed: finalParams.seed || -1,
      scale: finalParams.scale || 3.0,
      ddim_steps: finalParams.ddim_steps || 16,
      use_rephraser: finalParams.use_rephraser !== false,
      return_url: true,
      controlnet_args: [
        {
          type: finalParams.controlnet_type || 'depth',
          binary_data_index: 0,
          strength: finalParams.controlnet_strength || 0.6,
        },
      ],
      logo_info: {
        add_logo: finalParams.add_logo || false,
        position: finalParams.logo_position || 0,
        language: finalParams.logo_language || 0,
        logo_text_content: finalParams.logo_text_content || '',
      },
    };

    Logger.log(`ControlNet最终提示词: "${drawPrompt}"`, 'DoubaoImageService');
    Logger.debug(`ControlNet参数: ${JSON.stringify(requestBody)}`, 'DoubaoImageService');

    // 生成签名
    const hostFromUrl = new URL(doubaoConfig.baseUrl).hostname;
    const signature = generateDoubaoSignature(
      {
        accessKeyId: doubaoConfig.accessKeyId,
        secretAccessKey: doubaoConfig.secretAccessKey,
        region: doubaoConfig.region,
        service: doubaoConfig.service,
      },
      {
        method: 'POST',
        uri: '/',
        queryParams: {
          Action: 'CVProcess',
          Version: '2022-08-31',
        },
        headers: {
          'Content-Type': 'application/json',
        },
        payload: JSON.stringify(requestBody),
        host: hostFromUrl,
      },
    );

    // 更新进度
    result.content = '正在使用ControlNet生成图像...';

    // 发送请求
    const response = await this.makeDoubaoRequestWithRetry(
      `${doubaoConfig.baseUrl}?Action=CVProcess&Version=2022-08-31`,
      requestBody,
      signature.headers,
      timeout || 60000,
      requestId,
    );

    Logger.log(`ControlNet API调用成功`, 'DoubaoImageService');
    this.logDoubaoApiResponse(response.data);

    // 更新进度
    result.content = '正在处理ControlNet生成的图像...';

    // 处理响应并上传图片
    await this.processDoubaoResponseAndUpload(
      response.data,
      result,
      completionReply,
      onSuccess,
      requestId,
    );
  }

  // 豆包涂抹编辑（Inpainting）
  private async generateDoubaoInpainting(
    inputs,
    drawPrompt,
    completionReply,
    doubaoParams,
    currentImageUrl,
    maskUrl,
    result,
    tempFiles,
    requestId,
  ) {
    const { apiKey, proxyUrl, timeout, onSuccess } = inputs;

    Logger.log(`执行涂抹编辑操作 [${requestId}]`, 'DoubaoImageService');

    // 解析配置
    const doubaoConfig = this.parseDoubaoConfig(apiKey, proxyUrl);
    if (!doubaoConfig.accessKeyId || !doubaoConfig.secretAccessKey) {
      throw new Error('配置不完整，请检查apiKey格式（应为accessKeyId|secretAccessKey）');
    }

    // 准备图像URL列表（包含原图和蒙版）
    const imageUrls = await this.prepareImageUrlList(currentImageUrl, maskUrl);
    const originalImage = imageUrls.find(img => img.type === 'image');
    const maskImage = imageUrls.find(img => img.type === 'mask');

    if (!originalImage) {
      throw new Error('Inpainting需要原始图像');
    }

    if (!maskImage) {
      throw new Error('Inpainting需要蒙版图像');
    }

    Logger.debug(
      `Inpainting图像: 原图=${originalImage.url}, 蒙版=${maskImage.url}`,
      'DoubaoImageService',
    );

    // 合并参数：extraParam > doubaoParams > 默认值
    const finalParams = this.mergeDoubaoInpaintingParams(inputs.extraParam, doubaoParams);

    // 构建Inpainting请求参数
    const requestBody = {
      req_key: 'i2i_inpainting_edit',
      image_urls: [originalImage.url, maskImage.url],
      custom_prompt: drawPrompt,
      seed: finalParams.seed || -1,
      scale: finalParams.scale || 5,
      steps: finalParams.steps || 25,
      return_url: true,
      logo_info: {
        add_logo: finalParams.add_logo || false,
        position: finalParams.logo_position || 0,
        language: finalParams.logo_language || 0,
        opacity: finalParams.logo_opacity || 0.3,
        logo_text_content: finalParams.logo_text_content || '',
      },
    };

    Logger.log(`涂抹编辑最终提示词: "${drawPrompt}"`, 'DoubaoImageService');
    Logger.debug(`涂抹编辑参数: ${JSON.stringify(requestBody)}`, 'DoubaoImageService');

    // 生成签名
    const hostFromUrl = new URL(doubaoConfig.baseUrl).hostname;
    const signature = generateDoubaoSignature(
      {
        accessKeyId: doubaoConfig.accessKeyId,
        secretAccessKey: doubaoConfig.secretAccessKey,
        region: doubaoConfig.region,
        service: doubaoConfig.service,
      },
      {
        method: 'POST',
        uri: '/',
        queryParams: {
          Action: 'Img2ImgInpaintingEdit',
          Version: '2022-08-31',
        },
        headers: {
          'Content-Type': 'application/json',
        },
        payload: JSON.stringify(requestBody),
        host: hostFromUrl,
      },
    );

    // 更新进度
    result.content = '正在进行涂抹编辑...';

    // 发送请求
    const response = await this.makeDoubaoRequestWithRetry(
      `${doubaoConfig.baseUrl}?Action=Img2ImgInpaintingEdit&Version=2022-08-31`,
      requestBody,
      signature.headers,
      timeout || 60000,
      requestId,
    );

    Logger.log(`涂抹编辑API调用成功`, 'DoubaoImageService');
    this.logDoubaoApiResponse(response.data);

    // 更新进度
    result.content = '正在处理编辑后的图像...';

    // 处理响应并上传图片
    await this.processDoubaoResponseAndUpload(
      response.data,
      result,
      completionReply,
      onSuccess,
      requestId,
    );
  }

  // 准备图像URL列表（从gptImage服务借鉴）
  private async prepareImageUrlList(currentImageUrl, maskUrl) {
    let imageUrls = [];

    // 🔍 添加详细调试信息
    Logger.debug(`=== DoubaoImageService prepareImageUrlList 调试信息 ===`, 'DoubaoImageService');
    Logger.debug(`输入currentImageUrl类型: ${typeof currentImageUrl}`, 'DoubaoImageService');
    Logger.debug(
      `输入currentImageUrl内容: ${JSON.stringify(currentImageUrl)}`,
      'DoubaoImageService',
    );
    Logger.debug(`输入maskUrl: ${maskUrl}`, 'DoubaoImageService');

    if (typeof currentImageUrl === 'string') {
      Logger.debug(`处理字符串类型的currentImageUrl`, 'DoubaoImageService');

      // 检查是否是嵌套的JSON字符串
      if (currentImageUrl.startsWith('[') || currentImageUrl.startsWith('{')) {
        try {
          Logger.debug(`尝试解析JSON字符串...`, 'DoubaoImageService');
          const parsedUrl = JSON.parse(currentImageUrl);
          Logger.debug(`JSON解析结果: ${JSON.stringify(parsedUrl)}`, 'DoubaoImageService');

          if (Array.isArray(parsedUrl)) {
            Logger.debug(`解析得到数组，包含${parsedUrl.length}个元素`, 'DoubaoImageService');
            imageUrls = parsedUrl.map((item, index) => ({
              url: typeof item === 'string' ? item : item.url,
              type: (typeof item === 'object' && item.type) || 'image',
              index: (typeof item === 'object' && item.index) || index,
            }));
          } else if (parsedUrl.url) {
            Logger.debug(`解析得到对象，包含url字段`, 'DoubaoImageService');
            imageUrls = [{ url: parsedUrl.url, type: parsedUrl.type || 'image', index: 0 }];
          } else {
            Logger.warn(
              `JSON对象不包含url字段: ${JSON.stringify(parsedUrl)}`,
              'DoubaoImageService',
            );
          }
        } catch (e) {
          Logger.error(`JSON解析失败: ${e.message}，将作为普通URL处理`, 'DoubaoImageService');
          // 如果解析失败，作为普通URL处理
          imageUrls = [{ url: currentImageUrl, type: 'image', index: 0 }];
        }
      } else {
        Logger.debug(`处理普通URL字符串`, 'DoubaoImageService');
        // 普通URL字符串
        imageUrls = [{ url: currentImageUrl, type: 'image', index: 0 }];
      }
    } else if (Array.isArray(currentImageUrl)) {
      Logger.debug(
        `处理数组类型的currentImageUrl，包含${currentImageUrl.length}个元素`,
        'DoubaoImageService',
      );
      imageUrls = currentImageUrl.map((item, index) => {
        const imgObj = {
          url: typeof item === 'string' ? item : item.url,
          type: (typeof item === 'object' && item.type) || 'image',
          index: (typeof item === 'object' && item.index) || index,
        };
        Logger.debug(`数组元素${index}: ${JSON.stringify(imgObj)}`, 'DoubaoImageService');
        return imgObj;
      });
    } else if (currentImageUrl && typeof currentImageUrl === 'object') {
      Logger.debug(`处理对象类型的currentImageUrl`, 'DoubaoImageService');
      if (currentImageUrl.url) {
        imageUrls = [{ url: currentImageUrl.url, type: currentImageUrl.type || 'image', index: 0 }];
        Logger.debug(`从对象提取URL: ${currentImageUrl.url}`, 'DoubaoImageService');
      } else {
        Logger.error('无法解析图像URL对象，缺少url字段', 'DoubaoImageService');
        throw new Error('无法解析图像URL对象');
      }
    } else {
      Logger.debug(
        `currentImageUrl格式未识别或为空: ${typeof currentImageUrl}`,
        'DoubaoImageService',
      );
    }

    // 如果有蒙版，添加到列表中
    if (maskUrl) {
      Logger.debug(`添加蒙版URL: ${maskUrl}`, 'DoubaoImageService');
      imageUrls.push({ url: maskUrl, type: 'mask', index: 0 });
    }

    Logger.debug(`最终图像URL列表: ${JSON.stringify(imageUrls)}`, 'DoubaoImageService');
    Logger.debug(`总共${imageUrls.length}个图像对象`, 'DoubaoImageService');

    return imageUrls;
  }
}
