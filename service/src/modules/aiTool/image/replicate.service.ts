import { correctApiBaseUrl } from '@/common/utils/correctApiBaseUrl';
import { Injectable, Logger } from '@nestjs/common';
import axios from 'axios';
import OpenAI from 'openai';
import * as sharp from 'sharp';
import { ChatLogService } from '../../chatLog/chatLog.service';
import { GlobalConfigService } from '../../globalConfig/globalConfig.service';
import { UploadService } from '../../upload/upload.service';

// Replicate image generation optimization tool
const replicateImageTool = {
  type: 'function' as const,
  function: {
    name: 'optimize_replicate_image',
    description:
      'Optimize and translate Replicate image generation parameters, convert prompts to English',
    parameters: {
      type: 'object',
      properties: {
        enhanced_prompt: {
          type: 'string',
          description:
            'Optimized English image prompt, clear and detailed, suitable for AI image generation models',
        },
        aspect_ratio: {
          type: 'string',
          enum: ['auto', '4:3', '3:4', '16:9', '9:16'],
          description:
            'Recommended aspect ratio. auto means match input image, or 1:1 if no input image',
        },
        input_image_url: {
          type: 'string',
          description: 'If image-to-image generation, provide the most relevant input image URL',
        },
        operation_type: {
          type: 'string',
          enum: ['text_to_image', 'image_to_image'],
          description: 'Operation type: text-to-image or image-to-image',
        },
        completion_reply: {
          type: 'string',
          description:
            'Generate a completion reply text to inform the user that image processing is complete, with a friendly and natural tone. Reply in the language used by the user',
        },
      },
      required: ['enhanced_prompt', 'aspect_ratio', 'operation_type', 'completion_reply'],
    },
  },
};

@Injectable()
export class ReplicateImageService {
  constructor(
    private readonly uploadService: UploadService,
    private readonly globalConfigService: GlobalConfigService,
    private readonly chatLogService: ChatLogService,
  ) {}

  // 将 size 参数转换为 aspect_ratio
  private convertSizeToAspectRatio(size: string): string {
    if (!size) return 'auto';

    // 支持的宽高比直接返回
    const validAspectRatios = ['auto', '4:3', '3:4', '16:9', '9:16'];
    if (validAspectRatios.includes(size)) {
      return size;
    }

    // 如果传入了其他值，默认返回auto
    Logger.warn(`不支持的尺寸参数: ${size}，将使用auto`, 'ReplicateImageService');
    return 'auto';
  }

  // 处理aspect_ratio，将auto转换为实际的API参数
  private processAspectRatio(aspectRatio: string, hasInputImage: boolean): string {
    if (aspectRatio === 'auto') {
      if (hasInputImage) {
        return 'match_input_image';
      } else {
        return '1:1'; // 没有输入图像时使用1:1
      }
    }
    return aspectRatio;
  }

  // 从历史消息中提取最相关的图像URL
  private async extractImageFromHistory(
    messagesHistory: any[],
    prompt: string,
  ): Promise<string | null> {
    if (!messagesHistory || messagesHistory.length === 0) return null;

    const allImageUrls: string[] = [];

    // 从最近的消息开始查找图像
    for (let i = messagesHistory.length - 1; i >= 0; i--) {
      const message = messagesHistory[i];

      // 检查消息内容中的图像URL
      if (message.content && Array.isArray(message.content)) {
        for (const content of message.content) {
          if (content.type === 'image_url' && content.image_url?.url) {
            const url = content.image_url.url.trim();
            Logger.log(`从历史消息中找到图像: ${url}`, 'ReplicateImageService');
            allImageUrls.push(url);
          }
        }
      }

      // 检查 fileUrl 字段
      if (message.fileUrl) {
        try {
          const fileUrls = JSON.parse(message.fileUrl);
          if (Array.isArray(fileUrls) && fileUrls.length > 0) {
            fileUrls.forEach(url => {
              if (url && typeof url === 'string') {
                Logger.log(`从历史消息的fileUrl中找到图像: ${url}`, 'ReplicateImageService');
                allImageUrls.push(url);
              }
            });
          }
        } catch (e) {
          // 可能是单个URL字符串
          if (typeof message.fileUrl === 'string' && message.fileUrl.startsWith('http')) {
            Logger.log(
              `从历史消息的fileUrl中找到图像: ${message.fileUrl}`,
              'ReplicateImageService',
            );
            allImageUrls.push(message.fileUrl);
          }
        }
      }

      // 如果找到图片就停止搜索（只处理最近一条消息中的图片）
      if (allImageUrls.length > 0) {
        break;
      }
    }

    // 如果没有找到图片，返回null
    if (allImageUrls.length === 0) {
      return null;
    }

    // 如果只有一张图片，直接返回
    if (allImageUrls.length === 1) {
      return allImageUrls[0];
    }

    // 如果有多张图片，进行拼接
    Logger.log(
      `从历史消息中检测到${allImageUrls.length}张图片，将进行横向拼接`,
      'ReplicateImageService',
    );
    return await this.stitchImagesHorizontally(allImageUrls);
  }

  // 横向拼接多张图片
  private async stitchImagesHorizontally(imageUrls: string[]): Promise<string> {
    if (!imageUrls || imageUrls.length <= 1) {
      return imageUrls?.[0] || null;
    }

    try {
      Logger.log(`开始拼接${imageUrls.length}张图片`, 'ReplicateImageService');

      // 下载所有图片
      const imageBuffers = await Promise.all(
        imageUrls.map(async url => {
          try {
            const response = await axios.get(url, {
              responseType: 'arraybuffer',
              timeout: 30000,
            });
            return Buffer.from(response.data);
          } catch (error) {
            Logger.warn(`下载图片失败: ${url}, 错误: ${error.message}`, 'ReplicateImageService');
            return null;
          }
        }),
      );

      // 过滤掉下载失败的图片
      const validBuffers = imageBuffers.filter(buffer => buffer !== null);
      if (validBuffers.length === 0) {
        throw new Error('所有图片下载失败');
      }

      if (validBuffers.length === 1) {
        // 只有一张有效图片，直接返回原URL
        return imageUrls[imageBuffers.findIndex(buffer => buffer !== null)];
      }

      // 获取所有图片的元数据
      const imageMetadata = await Promise.all(validBuffers.map(buffer => sharp(buffer).metadata()));

      // 使用第一张图片的高度作为基准
      const targetHeight = imageMetadata[0].height;
      Logger.log(`使用第一张图片的高度作为基准: ${targetHeight}px`, 'ReplicateImageService');

      // 调整所有图片到相同高度，保持宽高比
      const resizedImages = await Promise.all(
        validBuffers.map(async (buffer, index) => {
          const metadata = imageMetadata[index];
          const aspectRatio = metadata.width / metadata.height;
          const targetWidth = Math.round(targetHeight * aspectRatio);

          return sharp(buffer)
            .resize(targetWidth, targetHeight, {
              fit: 'fill',
              background: { r: 255, g: 255, b: 255, alpha: 1 },
            })
            .png()
            .toBuffer();
        }),
      );

      // 计算拼接后的总宽度
      const resizedMetadata = await Promise.all(
        resizedImages.map(buffer => sharp(buffer).metadata()),
      );
      const totalWidth = resizedMetadata.reduce((sum, meta) => sum + meta.width, 0);

      Logger.log(`拼接后图片尺寸: ${totalWidth}x${targetHeight}`, 'ReplicateImageService');

      // 创建拼接画布
      let stitchedImage = sharp({
        create: {
          width: totalWidth,
          height: targetHeight,
          channels: 4,
          background: { r: 255, g: 255, b: 255, alpha: 1 },
        },
      });

      // 准备合成操作
      const composite = [];
      let currentLeft = 0;

      for (let i = 0; i < resizedImages.length; i++) {
        composite.push({
          input: resizedImages[i],
          left: currentLeft,
          top: 0,
        });
        currentLeft += resizedMetadata[i].width;
      }

      // 执行拼接
      const stitchedBuffer = await stitchedImage.composite(composite).png().toBuffer();

      Logger.log(`图片拼接完成，大小: ${stitchedBuffer.length} 字节`, 'ReplicateImageService');

      // 保存拼接后的图片
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const currentDate = `${year}${month}/${day}`;

      const file = {
        buffer: stitchedBuffer,
        mimetype: 'image/png',
      };

      const savedUrl = await this.uploadService.uploadFile(
        file,
        `images/replicate/stitched/${currentDate}`,
      );
      Logger.log(`拼接图片保存成功: ${savedUrl}`, 'ReplicateImageService');

      return savedUrl;
    } catch (error) {
      Logger.error(`图片拼接失败: ${error.message}`, 'ReplicateImageService');
      // 拼接失败时返回第一张图片
      return imageUrls[0];
    }
  }

  // 使用 Function Calling 优化参数
  private async optimizeWithTool(
    prompt: string,
    imageUrl: string | null,
    size: string,
    messagesHistory: any[],
  ) {
    try {
      // 获取 Function Calling 配置
      const {
        toolCallUrl,
        toolCallKey,
        toolCallModel,
        openaiBaseKey,
        openaiBaseUrl,
        openaiBaseModel,
      } = await this.globalConfigService.getConfigs([
        'toolCallUrl',
        'toolCallKey',
        'toolCallModel',
        'openaiBaseKey',
        'openaiBaseUrl',
        'openaiBaseModel',
      ]);

      const openai = new OpenAI({
        apiKey: toolCallKey || openaiBaseKey,
        baseURL: await correctApiBaseUrl(toolCallUrl || openaiBaseUrl),
        timeout: 30000,
      });

      const model = toolCallModel || openaiBaseModel;

      const systemMessage = `You are a Replicate image generation assistant. You need to:
1. Translate and optimize user prompts into English to make them more suitable for AI image generation models
2. English prompts should be clear, detailed, and specific, including style, details, and quality descriptions
3. Determine the operation type (text-to-image or image-to-image)
4. Recommend appropriate aspect ratio from: auto, 4:3, 3:4, 16:9, 9:16
   - auto: match input image if available, otherwise 1:1 square
   - 4:3: traditional landscape (like computer screen)
   - 3:4: traditional portrait
   - 16:9: widescreen landscape (like movie poster)
   - 9:16: vertical mobile format (like phone wallpaper)
5. If image-to-image, provide the most relevant input image URL
6. Generate a friendly completion reply in the language used by the user

Note: enhanced_prompt must be an optimized English prompt, completion_reply should reply in the language used by the user's prompt.`;

      const userMessage = `
Current request prompt: "${prompt}"

${imageUrl ? `Current provided image: ${imageUrl}` : 'No image directly provided'}
${size ? `User specified size: ${size}` : 'User did not specify size'}

Message history: ${JSON.stringify(messagesHistory.slice(-3))}

Please translate and optimize the prompt into English, and generate corresponding parameters and image completion reply.`;

      const response = await openai.chat.completions.create({
        model: model,
        messages: [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage },
        ],
        tools: [replicateImageTool],
        tool_choice: 'auto',
      });

      const toolCalls = response.choices[0]?.message?.tool_calls;
      if (toolCalls && toolCalls.length > 0) {
        const functionArgs = JSON.parse(toolCalls[0].function.arguments);
        Logger.log(
          `Function Calling 优化结果: ${JSON.stringify(functionArgs)}`,
          'ReplicateImageService',
        );
        return functionArgs;
      }
    } catch (error) {
      Logger.error(`Function Calling 优化失败: ${error}`, 'ReplicateImageService');
    }

    // 如果优化失败，返回默认值
    return {
      enhanced_prompt: prompt,
      aspect_ratio: this.convertSizeToAspectRatio(size),
      input_image_url: imageUrl,
      operation_type: imageUrl ? 'image_to_image' : 'text_to_image',
      completion_reply: '图片已生成完成',
    };
  }

  // 主要的绘图方法
  async replicateDraw(inputs: any, buildMessageFromParentMessageId: any) {
    Logger.log('==== 开始 Replicate 绘图任务 ====', 'ReplicateImageService');

    const result: any = { content: '', imageUrl: '', status: 2 };

    try {
      const {
        apiKey,
        model,
        proxyUrl,
        prompt,
        extraParam,
        imageUrl: inputImageUrl,
        timeout = 60000,
        onSuccess,
        onFailure,
        groupId,
      } = inputs;

      // 获取历史消息
      const { messagesHistory } = await buildMessageFromParentMessageId(
        {
          groupId: groupId,
          systemMessage: '',
          maxModelTokens: 8000,
          maxRounds: 5,
          isImageUpload: 2,
          isConvertToBase64: 0,
        },
        this.chatLogService,
      );

      // 解析输入的图像URL
      let currentImageUrl = inputImageUrl;
      let imageUrls: string[] = [];

      Logger.log(`输入的 imageUrl: ${JSON.stringify(inputImageUrl)}`, 'ReplicateImageService');

      if (inputImageUrl && typeof inputImageUrl === 'string') {
        try {
          // 先尝试按逗号分隔处理
          if (
            inputImageUrl.includes(',') &&
            !inputImageUrl.startsWith('[') &&
            !inputImageUrl.startsWith('{')
          ) {
            // 逗号分隔的字符串格式，如："url1,url2,url3"
            imageUrls = inputImageUrl
              .split(',')
              .map(url => url.trim())
              .filter(url => url);
            Logger.log(
              `检测到逗号分隔格式，解析出${imageUrls.length}个URL`,
              'ReplicateImageService',
            );

            if (imageUrls.length > 1) {
              Logger.log(
                `检测到${imageUrls.length}张图片，将进行横向拼接`,
                'ReplicateImageService',
              );
              currentImageUrl = await this.stitchImagesHorizontally(imageUrls);
            } else {
              currentImageUrl = imageUrls[0];
            }
          } else {
            // 尝试JSON解析
            const parsed = JSON.parse(inputImageUrl);
            if (parsed && parsed.url) {
              currentImageUrl = parsed.url;
              imageUrls = [parsed.url];
            } else if (Array.isArray(parsed) && parsed.length > 0) {
              // 处理多张图片的情况
              imageUrls = parsed.map(item => item.url || item).filter(url => url);
              if (imageUrls.length > 1) {
                Logger.log(
                  `检测到${imageUrls.length}张图片，将进行横向拼接`,
                  'ReplicateImageService',
                );
                currentImageUrl = await this.stitchImagesHorizontally(imageUrls);
              } else {
                currentImageUrl = imageUrls[0];
              }
            }
          }
        } catch (e) {
          // JSON解析失败时，检查是否是单个URL
          if (inputImageUrl.startsWith('http')) {
            Logger.log(`作为单个URL处理: ${inputImageUrl}`, 'ReplicateImageService');
            currentImageUrl = inputImageUrl;
            imageUrls = [inputImageUrl];
          } else {
            Logger.log(
              `解析 inputImageUrl 失败: ${e.message}，保持原始值`,
              'ReplicateImageService',
            );
          }
        }
      } else if (Array.isArray(inputImageUrl)) {
        // 直接传入数组的情况
        imageUrls = inputImageUrl
          .map(item => (typeof item === 'string' ? item : item.url))
          .filter(url => url);
        if (imageUrls.length > 1) {
          Logger.log(`检测到${imageUrls.length}张图片，将进行横向拼接`, 'ReplicateImageService');
          currentImageUrl = await this.stitchImagesHorizontally(imageUrls);
        } else {
          currentImageUrl = imageUrls[0];
        }
      }

      Logger.log(`解析后的 currentImageUrl: ${currentImageUrl}`, 'ReplicateImageService');

      // 如果没有直接的图像URL，尝试从历史消息中提取
      if (!currentImageUrl) {
        currentImageUrl = await this.extractImageFromHistory(messagesHistory, prompt);
        Logger.log(
          `从历史消息中提取的 currentImageUrl: ${currentImageUrl}`,
          'ReplicateImageService',
        );
      }

      // 使用 Function Calling 优化参数
      const optimized = await this.optimizeWithTool(
        prompt,
        currentImageUrl,
        extraParam?.size,
        messagesHistory,
      );

      Logger.log(
        `Function Calling 优化结果: ${JSON.stringify(optimized)}`,
        'ReplicateImageService',
      );

      // 构建 Replicate API 请求
      const apiUrl = `${await correctApiBaseUrl(proxyUrl)}/models/${model}/predictions`;
      const requestBody: any = {
        input: {
          prompt: optimized.enhanced_prompt,
          aspect_ratio: this.processAspectRatio(
            optimized.aspect_ratio,
            !!optimized.input_image_url,
          ),
        },
      };

      // 如果是图生图，添加输入图像
      if (optimized.operation_type === 'image_to_image' && optimized.input_image_url) {
        requestBody.input.input_image = optimized.input_image_url;
      }

      Logger.log(`Replicate API URL: ${apiUrl}`, 'ReplicateImageService');
      Logger.log(`请求参数: ${JSON.stringify(requestBody, null, 2)}`, 'ReplicateImageService');

      // 调用 Replicate API
      const response = await axios.post(apiUrl, requestBody, {
        headers: {
          Authorization: `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          Prefer: 'wait',
        },
        timeout: timeout,
      });

      Logger.log(`Replicate API 响应状态: ${response.status}`, 'ReplicateImageService');
      Logger.log(
        `Replicate API 响应: ${JSON.stringify(response.data, null, 2)}`,
        'ReplicateImageService',
      );

      // 处理响应
      const responseData = response.data;

      // 检查响应状态
      if (responseData.status === 'succeeded' && responseData.output) {
        // 任务已完成，直接处理结果
        const imageUrl = responseData.output;
        Logger.log(`获取到生成的图像URL: ${imageUrl}`, 'ReplicateImageService');

        result.imageUrl = await this.downloadAndSaveImage(imageUrl);
        result.content = optimized.completion_reply;
        result.status = 3;

        Logger.log(`图片保存成功: ${result.imageUrl}`, 'ReplicateImageService');
        onSuccess(result);
      } else if (responseData.status === 'starting' || responseData.status === 'processing') {
        // 任务还在处理中，需要轮询
        Logger.log(
          `任务状态: ${responseData.status}，任务ID: ${responseData.id}，开始轮询获取结果`,
          'ReplicateImageService',
        );

        const pollUrl = `${await correctApiBaseUrl(proxyUrl)}/predictions/${responseData.id}`;
        const finalResult = await this.pollForResult(pollUrl, apiKey, timeout);

        if (finalResult && finalResult.output) {
          const imageUrl = finalResult.output;
          Logger.log(`轮询获取到生成的图像URL: ${imageUrl}`, 'ReplicateImageService');

          result.imageUrl = await this.downloadAndSaveImage(imageUrl);
          result.content = optimized.completion_reply;
          result.status = 3;

          Logger.log(`图片保存成功: ${result.imageUrl}`, 'ReplicateImageService');
          onSuccess(result);
        } else {
          throw new Error(`任务失败或超时: ${JSON.stringify(finalResult)}`);
        }
      } else if (responseData.status === 'failed') {
        throw new Error(`Replicate 任务失败: ${responseData.error || '未知错误'}`);
      } else if (responseData.status === 'canceled') {
        throw new Error('Replicate 任务被取消');
      } else {
        throw new Error(`未知的任务状态: ${responseData.status}`);
      }

      return result;
    } catch (error) {
      return this.handleError(error, result, inputs.onFailure);
    }
  }

  // 轮询获取任务结果
  private async pollForResult(pollUrl: string, apiKey: string, timeout: number): Promise<any> {
    const startTime = Date.now();
    const maxTimeout = timeout || 60000; // 默认60秒超时
    const pollInterval = 1000; // 每1秒轮询一次

    Logger.log(`开始轮询任务结果，URL: ${pollUrl}`, 'ReplicateImageService');

    while (Date.now() - startTime < maxTimeout) {
      try {
        const response = await axios.get(pollUrl, {
          headers: {
            Authorization: `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
          timeout: 10000, // 单次请求10秒超时
        });

        const data = response.data;
        Logger.log(`轮询状态: ${data.status}`, 'ReplicateImageService');

        if (data.status === 'succeeded' && data.output) {
          Logger.log('任务完成，返回结果', 'ReplicateImageService');
          return data;
        } else if (data.status === 'failed') {
          Logger.error(`任务失败: ${data.error}`, 'ReplicateImageService');
          throw new Error(`任务失败: ${data.error || '未知错误'}`);
        } else if (data.status === 'canceled') {
          Logger.error('任务被取消', 'ReplicateImageService');
          throw new Error('任务被取消');
        }

        // 如果任务还在处理中，等待后继续轮询
        if (data.status === 'starting' || data.status === 'processing') {
          Logger.log(`任务仍在处理中，${pollInterval / 1000}秒后重试`, 'ReplicateImageService');
          await new Promise(resolve => setTimeout(resolve, pollInterval));
          continue;
        }

        // 未知状态
        Logger.warn(`未知任务状态: ${data.status}，继续轮询`, 'ReplicateImageService');
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      } catch (error) {
        if (
          error.message &&
          (error.message.includes('任务失败') || error.message.includes('任务被取消'))
        ) {
          throw error; // 重新抛出任务级别的错误
        }

        Logger.warn(`轮询请求失败: ${error.message}，继续重试`, 'ReplicateImageService');
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    }

    // 超时
    Logger.error(`轮询超时 (${maxTimeout / 1000}秒)`, 'ReplicateImageService');
    throw new Error(`任务超时，请稍后重试`);
  }

  // 下载并保存图片
  private async downloadAndSaveImage(imageUrl: string): Promise<string> {
    try {
      Logger.log(`开始下载图片: ${imageUrl}`, 'ReplicateImageService');

      const response = await axios.get(imageUrl, {
        responseType: 'arraybuffer',
        timeout: 30000,
      });

      const buffer = Buffer.from(response.data);
      Logger.log(`图片下载成功，大小: ${buffer.length} 字节`, 'ReplicateImageService');

      // 生成保存路径
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const currentDate = `${year}${month}/${day}`;

      const file = {
        buffer: buffer,
        mimetype: 'image/png',
      };

      const savedUrl = await this.uploadService.uploadFile(file, `images/replicate/${currentDate}`);
      Logger.log(`图片保存成功: ${savedUrl}`, 'ReplicateImageService');

      return savedUrl;
    } catch (error) {
      Logger.error(`下载或保存图片失败: ${error}`, 'ReplicateImageService');
      // 如果下载失败，返回原始URL
      return imageUrl;
    }
  }

  // 错误处理
  private handleError(error: any, result: any, onFailure: any) {
    result.status = 5;
    Logger.error(`Replicate 绘图失败: ${error}`, 'ReplicateImageService');

    const status = error?.status || error?.response?.status || 500;
    const message = error?.message || error?.response?.data?.detail || '';

    if (status === 429) {
      result.content = '当前请求过载，请稍后再试！';
    } else if (status === 401) {
      result.content = 'API密钥无效，请检查配置！';
    } else if (status === 400) {
      result.content = `请求参数错误: ${message}`;
    } else if (message.includes('timeout')) {
      result.content = '请求超时，请稍后重试！';
    } else {
      result.content = message || 'Replicate 图像生成失败，请稍后重试！';
    }

    onFailure(result);
    Logger.log(`错误处理完成: ${result.content}`, 'ReplicateImageService');
    return result;
  }
}
