import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GlobalConfigModule } from '../globalConfig/globalConfig.module';
import { MCPController } from './mcp.controller';
import { MCPConfigEntity } from './mcp.entity';
import { MCPService } from './mcp.service';
import { McpToolService } from './mcpTool.service';

@Module({
  imports: [TypeOrmModule.forFeature([MCPConfigEntity]), GlobalConfigModule],
  controllers: [MCPController],
  providers: [MCPService, McpToolService],
  exports: [MCPService, McpToolService],
})
export class MCPModule {}
