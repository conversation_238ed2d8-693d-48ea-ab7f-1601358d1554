import { handleError } from '@/common/utils';
import { correctApiBaseUrl } from '@/common/utils/correctApiBaseUrl';
import { Injectable, Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { GlobalConfigService } from '../globalConfig/globalConfig.service';
import { MCPService } from './mcp.service';

@Injectable()
export class McpToolService {
  constructor(
    private readonly globalConfigService: GlobalConfigService,
    private readonly mcpService: MCPService,
  ) {}

  /**
   * 处理MCP工具调用
   * @param messagesHistory 消息历史
   * @param inputs 输入参数
   * @param result 结果对象
   * @returns 工具调用结果
   */
  async processMcpToolCalls(
    messagesHistory: any,
    inputs: {
      timeout: any;
      isMcpTool?: boolean;
      usingMcpTool?: boolean;
      onProgress?: (data: any) => void;
      onDatabase?: (data: any) => void;
    },
    result: any,
  ): Promise<string> {
    const { timeout, isMcpTool, usingMcpTool, onProgress, onDatabase } = inputs;
    let mcpToolResults = '';

    // 如果不使用MCP工具，直接返回空结果
    if (!usingMcpTool || !isMcpTool) {
      return mcpToolResults;
    }

    const {
      openaiBaseUrl = '',
      openaiBaseKey = '',
      openaiBaseModel,
      toolCallUrl,
      toolCallKey,
      toolCallModel,
    } = await this.globalConfigService.getConfigs([
      'openaiBaseKey',
      'openaiBaseUrl',
      'openaiBaseModel',
      'toolCallUrl',
      'toolCallKey',
      'toolCallModel',
    ]);

    try {
      await this.mcpService.initialize();
      const mcpClients = this.mcpService.getClients();
      if (Object.keys(mcpClients).length > 0) {
        Logger.debug(`找到 ${Object.keys(mcpClients).length} 个MCP客户端`, 'McpToolService');
        const tools = [];
        const clientToolsMap = {};

        for (const [clientName, client] of Object.entries(mcpClients)) {
          if (client.tools && client.tools.length > 0) {
            Logger.debug(`客户端 ${clientName} 有 ${client.tools.length} 个工具`, 'McpToolService');
            for (const tool of client.tools) {
              const toolDefinition = {
                type: 'function',
                function: {
                  name: `${tool.name}`,
                  description:
                    tool.description || `通过 ${clientName} 客户端调用 ${tool.name} 工具`,
                  parameters: tool.inputSchema || {
                    type: 'object',
                    properties: {},
                    required: [],
                  },
                },
              };
              tools.push(toolDefinition);
              const toolKey = `${tool.name}`;
              clientToolsMap[toolKey] = {
                clientName,
                toolName: tool.name,
              };
            }
          }
        }

        if (tools.length > 0) {
          Logger.log(`准备使用 ${tools.length} 个MCP工具进行function calling`, 'McpToolService');

          const openai = new OpenAI({
            apiKey: toolCallKey || openaiBaseKey,
            baseURL: await correctApiBaseUrl(toolCallUrl || openaiBaseUrl),
            timeout: timeout,
          });

          // 初始化工具调用循环相关变量
          let toolCallLoop = 0;
          const maxToolCallLoops = 3;
          let currentToolCallMessages: Array<{
            role: 'system' | 'user' | 'assistant';
            content: string;
          }> = [
            {
              role: 'system',
              content:
                '你是一个强大的AI助手，拥有调用各种工具的能力。请判断用户可能需要的工具，而不是回答问题。\n\n' +
                '工具使用指南：\n' +
                '1. 当用户询问需要实时信息、搜索内容或特定功能时，优先考虑使用相关工具\n' +
                '2. 分析用户问题，确定最合适的工具，并正确提供所有必需参数\n' +
                '3. 对于搜索类工具，提供具体、明确的查询关键词\n' +
                '4. 如果多个工具可以解决问题，请使用多个工具\n' +
                '5. 工具调用失败时，尝试调整参数后重试\n\n' +
                '记住：主动使用工具能够提供更准确、更新、更有价值的回答。不要仅依赖你已有的知识，特别是当用户需要最新信息或特定数据时。',
            },
          ];

          // 初始化消息历史
          messagesHistory.forEach(message => {
            if (message.role !== 'system') {
              const role = message.role === 'user' ? ('user' as const) : ('assistant' as const);
              currentToolCallMessages.push({
                role,
                content: message.content,
              });
            }
          });

          // 所有已调用工具的结果保存
          let allToolResults = '';
          let allToolCalls = [];

          // 工具调用循环
          while (toolCallLoop < maxToolCallLoops) {
            Logger.debug(
              `开始第 ${toolCallLoop + 1}/${maxToolCallLoops} 轮工具调用`,
              'McpToolService',
            );

            try {
              // 如果不是第一轮，更新最后一条用户消息，附加上一轮的工具结果
              if (toolCallLoop > 0 && allToolResults) {
                const lastUserMessageIndex = currentToolCallMessages
                  .map(m => m.role)
                  .lastIndexOf('user');
                if (lastUserMessageIndex !== -1) {
                  const lastUserMessage = currentToolCallMessages[lastUserMessageIndex];
                  currentToolCallMessages[lastUserMessageIndex] = {
                    role: 'user',
                    content: `${lastUserMessage.content}\n\n已获取的工具结果：${allToolResults}\n\n请判断是否还有其他适合调用的工具来帮助解决问题？如果有，请直接调用，无需解释。`,
                  };
                }
              }

              const functionCallResponse = await openai.chat.completions.create({
                model: toolCallModel || openaiBaseModel,
                messages: currentToolCallMessages as OpenAI.Chat.ChatCompletionMessageParam[],
                tools: tools,
                tool_choice: 'auto',
              });

              // 添加助手回复到消息历史
              if (functionCallResponse.choices[0]?.message) {
                currentToolCallMessages.push({
                  role: 'assistant',
                  content: functionCallResponse.choices[0]?.message?.content || '',
                });
              }

              const toolCalls = functionCallResponse.choices[0]?.message?.tool_calls;
              Logger.debug(`工具调用响应: ${JSON.stringify(toolCalls)}`, 'McpToolService');

              // 如果没有工具调用或已达到最大循环次数，退出循环
              if (!toolCalls || toolCalls.length === 0) {
                Logger.debug('没有更多工具需要调用，退出循环', 'McpToolService');
                break;
              }

              Logger.debug(`工具调用响应: ${toolCalls.length} 个工具被调用`, 'McpToolService');

              // 保存这一轮的工具调用，合并到总结果中
              allToolCalls = [...allToolCalls, ...toolCalls];
              result.tool_calls = JSON.stringify(allToolCalls);
              onProgress?.({
                tool_calls: result.tool_calls,
              });

              // 每轮工具调用的数据库记录
              const roundToolCallsData = {
                round: toolCallLoop + 1,
                tool_calls: JSON.stringify(toolCalls),
                timestamp: new Date(),
              };

              // 即时存储这一轮的工具调用信息
              onDatabase?.({
                tool_calls_round: roundToolCallsData,
              });

              let currentRoundResults = '';
              let hasSuccessfulToolCall = false;

              for (const toolCall of toolCalls) {
                if (toolCall.type === 'function') {
                  const functionName = toolCall.function.name;
                  const mappedTool = clientToolsMap[functionName];
                  if (mappedTool) {
                    try {
                      const args = JSON.parse(toolCall.function.arguments || '{}');
                      if (Object.keys(args).length === 0) {
                        Logger.warn(
                          `工具调用参数为空: ${mappedTool.clientName}:${mappedTool.toolName}`,
                          'McpToolService',
                        );
                        const errorMsg = `调用失败，原因: 参数不完整`;
                        currentRoundResults += `\n\n【工具${functionName}】${errorMsg}`;
                        mcpToolResults += `\n\n【工具${functionName}】${errorMsg}`;

                        // 即时存储工具调用失败信息
                        onDatabase?.({
                          tool_call_result: {
                            tool_id: toolCall.id,
                            tool_name: functionName,
                            status: 'error',
                            error_message: errorMsg,
                            arguments: JSON.parse(toolCall.function.arguments || '{}'),
                            timestamp: new Date(),
                          },
                        });

                        continue;
                      }

                      onProgress?.({
                        text: '',
                      });

                      Logger.debug(
                        `调用MCP工具: ${mappedTool.clientName}:${mappedTool.toolName}`,
                        'McpToolService',
                      );
                      Logger.debug(`工具参数: ${JSON.stringify(args)}`, 'McpToolService');

                      const toolResult = await this.mcpService.callTool(
                        mappedTool.clientName,
                        mappedTool.toolName,
                        args,
                      );

                      hasSuccessfulToolCall = true;
                      const toolResultStr = `\n\n【工具${functionName}】\n${JSON.stringify(
                        toolResult,
                        null,
                        2,
                      )}`;

                      currentRoundResults += toolResultStr;
                      mcpToolResults += toolResultStr;

                      // 更新已有的tool_calls记录，添加response字段
                      let toolCallsArray = JSON.parse(result.tool_calls);
                      const toolCallIndex = toolCallsArray.findIndex(
                        tc =>
                          tc.type === 'function' &&
                          tc.function?.name === functionName &&
                          tc.id === toolCall.id,
                      );
                      if (toolCallIndex !== -1) {
                        toolCallsArray[toolCallIndex].function.response =
                          JSON.stringify(toolResult);
                        result.tool_calls = JSON.stringify(toolCallsArray);
                      }

                      onProgress?.({
                        tool_calls: result.tool_calls,
                      });

                      // 即时存储工具调用成功结果
                      onDatabase?.({
                        tool_call_result: {
                          tool_id: toolCall.id,
                          tool_name: functionName,
                          status: 'success',
                          result: toolResult,
                          arguments: JSON.parse(toolCall.function.arguments || '{}'),
                          timestamp: new Date(),
                        },
                      });
                    } catch (error) {
                      Logger.error(`调用MCP工具失败: ${handleError(error)}`, 'McpToolService');
                      const errorMsg = handleError(error);
                      let userFriendlyError = `调用失败，原因: ${errorMsg}`;

                      if (errorMsg.includes('query') && errorMsg.includes('Required')) {
                        userFriendlyError = `调用失败，缺少必需的查询参数。使用此工具时必须提供搜索关键词。`;
                      } else if (errorMsg.includes('Invalid input')) {
                        userFriendlyError = `调用失败，输入参数无效。请检查参数格式和类型是否正确。`;
                      } else if (errorMsg.includes('rate limit')) {
                        userFriendlyError = `调用失败，已达到GitHub API速率限制。请稍后再试。`;
                      }

                      mcpToolResults += `\n\n【工具${functionName}】${userFriendlyError}`;

                      // 更新失败的工具调用信息到tool_calls数组
                      let toolCallsArray = JSON.parse(result.tool_calls);
                      currentRoundResults += `\n\n【工具${functionName}】【内容：${JSON.stringify(
                        toolCallsArray,
                      )}】${userFriendlyError}`;
                      const toolCallIndex = toolCallsArray.findIndex(
                        tc =>
                          tc.type === 'function' &&
                          tc.function?.name === functionName &&
                          tc.id === toolCall.id,
                      );
                      if (toolCallIndex !== -1) {
                        toolCallsArray[toolCallIndex].function.error = userFriendlyError;
                        result.tool_calls = JSON.stringify(toolCallsArray);
                      }

                      onProgress?.({
                        tool_calls: result.tool_calls,
                      });

                      // 即时存储工具调用错误信息
                      onDatabase?.({
                        tool_call_result: {
                          tool_id: toolCall.id,
                          tool_name: functionName,
                          status: 'error',
                          error_message: userFriendlyError,
                          error_detail: errorMsg,
                          arguments: JSON.parse(toolCall.function.arguments || '{}'),
                          timestamp: new Date(),
                        },
                      });
                    }
                  }
                }
              }

              // 更新总的工具结果
              allToolResults += currentRoundResults;

              // 即时存储当前轮次的累积工具调用结果
              onDatabase?.({
                tool_calls_accumulated: {
                  round: toolCallLoop + 1,
                  accumulated_results: mcpToolResults,
                  timestamp: new Date(),
                },
              });

              // 如果没有成功的工具调用或已经是最后一轮，退出循环
              if (!hasSuccessfulToolCall || toolCallLoop >= maxToolCallLoops - 1) {
                Logger.debug(`没有成功的工具调用或已达到最大循环次数，退出循环`, 'McpToolService');
                break;
              }

              toolCallLoop++;
            } catch (error) {
              Logger.error(`Function call循环失败: ${handleError(error)}`, 'McpToolService');

              // 即时存储循环失败信息
              onDatabase?.({
                tool_calls_error: {
                  round: toolCallLoop + 1,
                  error: handleError(error),
                  timestamp: new Date(),
                },
              });

              break;
            }
          }

          Logger.debug(`工具调用循环完成，共执行 ${toolCallLoop + 1} 轮`, 'McpToolService');

          // 存储最终的工具调用完整结果
          onDatabase?.({
            mcpToolResults: mcpToolResults,
          });
        }
      }
    } catch (error) {
      Logger.error(`MCP服务调用失败: ${handleError(error)}`, 'McpToolService');

      // 即时存储MCP服务失败信息
      onDatabase?.({
        mcp_service_error: {
          error: handleError(error),
          timestamp: new Date(),
        },
      });
    }

    return mcpToolResults;
  }
}
