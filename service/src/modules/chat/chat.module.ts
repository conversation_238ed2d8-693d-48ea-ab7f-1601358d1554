import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgentModule } from '../agent/agent.module';
import { OpenAIChatService } from '../aiTool/chat/chat.service';
import { FlowithService } from '../aiTool/chat/flowith.service';
import { GeminiService } from '../aiTool/chat/gemini.service';
import { BflImageService } from '../aiTool/image/bfl.service';
import { DoubaoImageService } from '../aiTool/image/doubaoImage.service';
import { Gpt4oDrawService } from '../aiTool/image/gpt4oDraw.service';
import { GptImageService } from '../aiTool/image/gptImage.service';
import { MidjourneyService } from '../aiTool/image/midjourneyDraw.service';
import { ReplicateImageService } from '../aiTool/image/replicate.service';
import { SunoService } from '../aiTool/music/suno.service';
import { AiPptService } from '../aiTool/other/aiPPT';
import { FileVectorSearchService } from '../aiTool/search/fileVectorSearch.service';
import { NetSearchService } from '../aiTool/search/netSearch.service';
import { CogVideoService } from '../aiTool/video/cogVideo.service';
import { LumaVideoService } from '../aiTool/video/lumaVideo.service';
import { AppEntity } from '../app/app.entity';
import { AppService } from '../app/app.service';
import { AppCatsEntity } from '../app/appCats.entity';
import { UserAppsEntity } from '../app/userApps.entity';
import { AutoReplyEntity } from '../autoReply/autoReply.entity';
import { AutoReplyService } from '../autoReply/autoReply.service';
import { BadWordsEntity } from '../badWords/badWords.entity';
import { BadWordsService } from '../badWords/badWords.service';
import { ViolationLogEntity } from '../badWords/violationLog.entity';
import { ChatGroupEntity } from '../chatGroup/chatGroup.entity';
import { ChatGroupService } from '../chatGroup/chatGroup.service';
import { ChatLogEntity } from '../chatLog/chatLog.entity';
import { ChatLogService } from '../chatLog/chatLog.service';
import { CramiPackageEntity } from '../crami/cramiPackage.entity';
import { ConfigEntity } from '../globalConfig/config.entity';
import { MailerService } from '../mailer/mailer.service';
import { MCPModule } from '../mcp/mcp.module';
import { ModelsEntity } from '../models/models.entity';
import { ModelsService } from '../models/models.service';
import { PluginEntity } from '../plugin/plugin.entity';
import { RedisCacheService } from '../redisCache/redisCache.service';
import { UploadService } from '../upload/upload.service';
import { UserEntity } from '../user/user.entity';
import { UserService } from '../user/user.service';
import { AccountLogEntity } from '../userBalance/accountLog.entity';
import { BalanceEntity } from '../userBalance/balance.entity';
import { FingerprintLogEntity } from '../userBalance/fingerprint.entity';
import { UserBalanceEntity } from '../userBalance/userBalance.entity';
import { UserBalanceService } from '../userBalance/userBalance.service';
import { VerificationEntity } from '../verification/verification.entity';
import { VerificationService } from '../verification/verification.service';
import { ChatController } from './chat.controller';
import { ChatService } from './chat.service';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      BalanceEntity,
      UserEntity,
      PluginEntity,
      VerificationEntity,
      ChatLogEntity,
      AccountLogEntity,
      ConfigEntity,
      UserEntity,
      CramiPackageEntity,
      ChatGroupEntity,
      AppEntity,
      UserBalanceEntity,
      FingerprintLogEntity,
      AppCatsEntity,
      UserAppsEntity,
      AutoReplyEntity,
      BadWordsEntity,
      ViolationLogEntity,
      ModelsEntity,
    ]),
    MCPModule,
    AgentModule,
  ],
  controllers: [ChatController],
  providers: [
    ChatService,
    UserBalanceService,
    UserService,
    VerificationService,
    ChatLogService,
    RedisCacheService,
    MailerService,
    UploadService,
    AutoReplyService,
    BadWordsService,
    ChatGroupService,
    ModelsService,
    SunoService,
    OpenAIChatService,
    MidjourneyService,
    LumaVideoService,
    CogVideoService,
    AiPptService,
    NetSearchService,
    AppService,
    Gpt4oDrawService,
    FlowithService,
    FileVectorSearchService,
    GptImageService,
    DoubaoImageService,
    GeminiService,
    ReplicateImageService,
    BflImageService,
  ],
  exports: [ChatService],
})
export class ChatModule {}
