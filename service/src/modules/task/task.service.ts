import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { createVerify } from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import { LessThanOrEqual, Repository } from 'typeorm';
import { ModelsService } from '../models/models.service';
import { UserBalanceEntity } from '../userBalance/userBalance.entity';
import { GlobalConfigService } from './../globalConfig/globalConfig.service';

// 系统授权状态
const LICENSE_STATUS = {
  valid: false,
  message: '',
  machineId: '',
  licenseKey: '',
  checkedAt: null as Date,
  publicKeyPath: '', // 添加公钥路径记录
  expiresAt: null as Date | null, // 添加过期时间字段
  isExpired: false, // 是否已过期
  daysRemaining: null as number | null, // 剩余天数
};

@Injectable()
export class TaskService {
  constructor(
    @InjectRepository(UserBalanceEntity)
    private readonly userBalanceEntity: Repository<UserBalanceEntity>,
    private readonly globalConfigService: GlobalConfigService,
    private readonly modelsService: ModelsService,
  ) {}

  /* 每小时刷新一次微信的token */
  @Cron(CronExpression.EVERY_HOUR)
  handleCron() {
    Logger.debug('Automatically refresh WeChat access every hour Token', 'TaskService');
    this.globalConfigService.getWechatAccessToken();
  }

  /* 每小时刷新一次旧账号的token */
  @Cron(CronExpression.EVERY_HOUR)
  handleOldWechatCron() {
    Logger.debug('Automatically refresh WeChat access every hour Token', 'TaskService');
    this.globalConfigService.getOldWechatAccessToken();
  }

  /* 每两钟执行一次检测会员过期任务 */
  // @Cron(CronExpression.EVERY_2_SECONDS)
  @Cron(CronExpression.EVERY_5_MINUTES)
  async checkUserMemerExpire() {
    const expireUsers = await this.userBalanceEntity.find({
      where: { expirationTime: LessThanOrEqual(new Date()) },
    });
    if (!expireUsers || !expireUsers.length) return;
    expireUsers.forEach((user: any) => {
      this.userBalanceEntity
        .update(
          { id: user.id },
          {
            expirationTime: null,
            packageId: 0,
            memberModel3Count: 0,
            memberModel4Count: 0,
            memberDrawMjCount: 0,
            appCats: '',
          },
        )
        .then(res => {
          Logger.debug(`${user.id}会员已到期、清空所有余额并移除会员身份！`, 'TaskService');
        });
    });
  }

  /* 每天零点验证授权码 */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  checkLicenseValidity() {
    const isValid = this.validateLicense();

    if (!isValid) {
      console.log('\n======================================');
      console.log('  授权状态: 未授权/已过期 ❌');
      console.log('  原因: ' + LICENSE_STATUS.message);
      console.log('  请确保LICENSE_KEY配置正确且有效');
      console.log('======================================\n');
      process.exit(1); // 授权无效，直接退出进程
    }
  }

  /**
   * 验证授权许可
   */
  private validateLicense(): boolean {
    try {
      LICENSE_STATUS.checkedAt = new Date();

      const licenseKey = process.env.LICENSE_KEY;
      LICENSE_STATUS.licenseKey = licenseKey ? '已配置' : '未配置';

      if (!licenseKey) {
        LICENSE_STATUS.message = '未配置LICENSE_KEY环境变量，授权验证失败';
        return false;
      }

      const machineId = this.getMachineId();
      LICENSE_STATUS.machineId = machineId || '获取失败';

      if (!machineId) {
        LICENSE_STATUS.message = '无法获取机器标识，授权验证失败';
        return false;
      }

      const publicKeyPath = LICENSE_STATUS.publicKeyPath || this.findFilePath('root-pub.pem');

      if (!publicKeyPath) {
        LICENSE_STATUS.message = '未找到公钥文件root-pub.pem，尝试了多个位置';
        return false;
      }

      LICENSE_STATUS.publicKeyPath = publicKeyPath;
      const publicKey = fs.readFileSync(publicKeyPath);

      const parts = licenseKey.split('.');
      if (parts.length !== 2) {
        LICENSE_STATUS.message = '授权码格式无效，应为"数据.签名"格式';
        return false;
      }

      const [dataBase64, signature] = parts;
      if (!dataBase64 || !signature) {
        LICENSE_STATUS.message = '授权码格式无效，数据或签名部分为空';
        return false;
      }

      let licenseData;
      try {
        const licenseDataString = Buffer.from(dataBase64, 'base64').toString('utf8');
        licenseData = JSON.parse(licenseDataString);
      } catch (error) {
        LICENSE_STATUS.message = '授权码数据无效或损坏，无法解析';
        return false;
      }

      if (licenseData.machineId !== machineId) {
        LICENSE_STATUS.message = '授权码与当前机器不匹配';
        return false;
      }

      if (licenseData.expiryDate) {
        const expiryDate = new Date(licenseData.expiryDate);
        LICENSE_STATUS.expiresAt = expiryDate;

        if (isNaN(expiryDate.getTime())) {
          LICENSE_STATUS.message = '授权码中的过期时间无效';
          return false;
        }

        const now = new Date();
        if (now > expiryDate) {
          LICENSE_STATUS.isExpired = true;
          LICENSE_STATUS.message = `授权已过期，过期时间: ${
            expiryDate.toISOString().split('T')[0]
          }`;
          return false;
        }

        const daysRemaining = Math.ceil(
          (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
        );
        LICENSE_STATUS.daysRemaining = daysRemaining;
      }

      const verify = createVerify('RSA-SHA256');
      verify.update(dataBase64);
      verify.end();

      const isSignatureValid = verify.verify(
        { key: publicKey, padding: process.versions.node >= '12.0.0' ? 1 : 1 },
        signature,
        'base64',
      );

      if (!isSignatureValid) {
        LICENSE_STATUS.message = '授权码签名验证失败，可能已被篡改';
        return false;
      }

      LICENSE_STATUS.valid = true;
      LICENSE_STATUS.message = '授权验证成功';
      return true;
    } catch (error) {
      LICENSE_STATUS.message = `授权验证过程出错: ${error.message}`;
      return false;
    }
  }

  /**
   * 获取机器唯一标识
   */
  private getMachineId(): string {
    try {
      // 首先尝试从环境变量获取
      const envMachineId = process.env.MACHINE_ID;
      if (envMachineId) {
        return envMachineId;
      }

      // 如果环境变量中没有，尝试使用node-machine-id库获取
      const { machineIdSync } = require('node-machine-id');
      return machineIdSync({ original: true });
    } catch (error) {
      return '';
    }
  }

  /**
   * 查找文件的多种可能路径
   * @param filename 文件名
   * @returns 找到的文件路径或null
   */
  private findFilePath(filename: string): string | null {
    const possiblePaths = [
      path.join(process.cwd(), filename), // 当前工作目录
      path.join(__dirname, '..', '..', '..', filename), // 应用根目录
      path.join(__dirname, filename), // 与主程序同级
      path.resolve(filename), // 绝对路径解析
      path.join(process.cwd(), '..', filename), // 上级目录
      path.join(process.cwd(), 'dist', filename), // dist目录
    ];

    for (const filePath of possiblePaths) {
      if (fs.existsSync(filePath)) {
        return filePath;
      }
    }

    return null;
  }
}
