import { handleError } from '@/common/utils';
import { correctApiBaseUrl } from '@/common/utils/correctApiBaseUrl';
import { Injectable, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { GlobalConfigService } from '../globalConfig/globalConfig.service';
import { MCPService } from '../mcp/mcp.service';
import { ModelsService } from '../models/models.service';
import { RedisCacheService } from '../redisCache/redisCache.service';
import { AgentRequestDto } from './dto/agent-request.dto';
import { SimplePromptGenerateDto } from './dto/generate-meta-prompt.dto';

// 导入LangGraph和LangChain相关依赖
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';

// 导入工作流框架
import { Workflow, WorkflowOptions, WorkflowType, createWorkflow } from './workflows';

@Injectable()
export class AgentService {
  private logger = new Logger(AgentService.name);
  private chatModel: ChatOpenAI;
  private workflows: Map<WorkflowType, Workflow> = new Map();
  // Redis键前缀
  private readonly WORKFLOW_STATE_PREFIX = 'workflow:state:';
  // 工作流状态过期时间（秒）
  private readonly WORKFLOW_STATE_EXPIRY = 3600; // 1小时

  constructor(
    private readonly globalConfigService: GlobalConfigService,
    private readonly mcpService: MCPService,
    private readonly modelsService: ModelsService,
    private readonly redisCacheService: RedisCacheService,
  ) {
    // 初始化LangChain模型和工具
    this.initLangChain().catch(error => {
      this.logger.error(`初始化LangChain失败: ${handleError(error)}`, error.stack);
      // 这里不抛出错误，因为构造函数中不能直接抛出异步错误
      // 但我们会在agentChat方法中检查初始化状态
    });
  }

  private async initLangChain() {
    try {
      const config = await this.globalConfigService.getConfigs([
        'openaiBaseKey',
        'openaiBaseUrl',
        'openaiBaseModel',
      ]);

      // 使用默认值或配置的值
      const apiKey = config.openaiBaseKey || 'temp-key-for-initialization';
      const apiBaseUrl = await correctApiBaseUrl(
        config.openaiBaseUrl || 'https://api.openai.com/v1',
      );
      const modelName = config.openaiBaseModel || 'gpt-4-turbo-preview';

      this.logger.log(`使用模型: ${modelName}, API基础URL: ${apiBaseUrl}`);

      // 即使没有配置API密钥，也初始化ChatOpenAI模型
      // 稍后在实际使用时会检查和更新API密钥
      try {
        this.chatModel = new ChatOpenAI({
          modelName: modelName,
          temperature: 0,
          apiKey: apiKey,
          configuration: {
            baseURL: apiBaseUrl,
          },
        });
        this.logger.log('ChatOpenAI模型初始化成功');
      } catch (modelError) {
        this.logger.error(`ChatOpenAI模型初始化失败: ${handleError(modelError)}`);
        // 使用默认配置创建一个基本模型，以便工作流可以初始化
        this.chatModel = new ChatOpenAI({
          modelName: 'gpt-3.5-turbo',
          temperature: 0,
          apiKey: 'sk-temp-initialization-key',
        });
        this.logger.warn('使用默认配置创建基本模型');
      }

      // 初始化MCP服务
      try {
        await this.mcpService.initialize();
        this.logger.log('MCP服务初始化成功');
      } catch (mcpError) {
        this.logger.error(`MCP服务初始化失败: ${handleError(mcpError)}`);
        // 不抛出错误，允许系统在没有MCP服务的情况下继续运行
      }

      // 初始化各种工作流
      await this.initWorkflows();

      this.logger.log('LangChain和工作流初始化成功');
    } catch (error) {
      this.logger.error(`初始化LangChain失败: ${handleError(error)}`, error.stack);
      throw error;
    }
  }

  /**
   * 初始化所有工作流
   */
  private async initWorkflows() {
    try {
      this.logger.log('开始初始化工作流...');

      // 初始化研究工作流
      try {
        const researchWorkflow = createWorkflow(WorkflowType.RESEARCH);
        this.logger.log(`已创建研究工作流实例，准备初始化...`);

        await researchWorkflow.initialize(
          this.chatModel,
          this.mcpService,
          this.globalConfigService,
        );
        this.logger.log(`研究工作流初始化成功`);

        this.workflows.set(WorkflowType.RESEARCH, researchWorkflow);
        this.logger.log(`研究工作流已添加到工作流集合中，key: ${WorkflowType.RESEARCH}`);
      } catch (workflowError) {
        this.logger.error(`研究工作流初始化失败: ${handleError(workflowError)}`);
        throw workflowError;
      }

      // 将来可以添加更多工作流的初始化

      this.logger.log(
        `工作流初始化完成，共 ${this.workflows.size} 个工作流：${Array.from(
          this.workflows.keys(),
        ).join(', ')}`,
      );
    } catch (error) {
      this.logger.error(`初始化工作流失败: ${handleError(error)}`);
      throw error; // 抛出错误以便上层处理
    }
  }

  /**
   * 获取指定类型的工作流
   * @param type 工作流类型
   * @returns 工作流实例
   */
  private getWorkflow(type: WorkflowType): Workflow {
    this.logger.debug(
      `尝试获取工作流: ${type}, 当前工作流列表: ${Array.from(this.workflows.keys()).join(', ')}`,
    );
    const workflow = this.workflows.get(type);
    if (!workflow) {
      this.logger.error(`工作流 ${type} 未初始化或不存在，当前工作流数量: ${this.workflows.size}`);
      throw new Error(`工作流 ${type} 未初始化或不存在`);
    }
    return workflow;
  }

  /**
   * 保存工作流状态到Redis
   * @param workflowId 工作流ID
   * @param state 工作流状态
   */
  private async saveWorkflowState(workflowId: string, state: any): Promise<void> {
    try {
      // 创建一个可序列化的状态副本
      const serializableState = { ...state };

      // 删除不可序列化的函数对象
      delete serializableState.onProgress;
      delete serializableState.onFailure;

      // 保存到Redis
      await this.redisCacheService.set(
        {
          key: `${this.WORKFLOW_STATE_PREFIX}${workflowId}`,
          val: JSON.stringify(serializableState),
        },
        this.WORKFLOW_STATE_EXPIRY,
      );

      this.logger.log(
        `工作流状态已保存, ID: ${workflowId}, 过期时间: ${this.WORKFLOW_STATE_EXPIRY}秒`,
      );
    } catch (error) {
      this.logger.error(`保存工作流状态失败: ${handleError(error)}`);
      throw error;
    }
  }

  /**
   * 从Redis恢复工作流状态
   * @param workflowId 工作流ID
   * @returns 恢复的工作流状态，如果不存在则返回null
   */
  private async getWorkflowState(workflowId: string): Promise<any | null> {
    try {
      const state = await this.redisCacheService.get({
        key: `${this.WORKFLOW_STATE_PREFIX}${workflowId}`,
      });

      if (!state) {
        this.logger.log(`未找到工作流状态或已过期, ID: ${workflowId}`);
        return null;
      }

      this.logger.log(`已恢复工作流状态, ID: ${workflowId}`);
      return JSON.parse(state);
    } catch (error) {
      this.logger.error(`恢复工作流状态失败: ${handleError(error)}`);
      return null;
    }
  }

  /**
   * 与OpenAIChatService兼容的接口，默认使用研究工作流
   */
  async agentChat(
    messagesHistory: any,
    inputs: {
      chatId?: any;
      apiKey?: string;
      model?: string;
      modelName?: string;
      temperature?: number;
      timeout?: number;
      proxyUrl?: string;
      abortController?: AbortController;
      onProgress?: (data: {
        text?: string;
        content?: any[];
        reasoning_content?: any[];
        finishReason?: string;
      }) => void;
      onFailure?: (error: any) => void;
      enableBackgroundInvestigation?: boolean;
      debug?: boolean;
      workflowType?: WorkflowType;
      workflowId?: string; // 工作流ID，用于恢复之前的工作流状态
      waitForUserInput?: boolean; // 指示是否需要等待用户输入
    } = {},
  ) {
    const {
      chatId,
      apiKey,
      model,
      modelName,
      temperature = 0,
      timeout = 60000,
      proxyUrl,
      abortController = new AbortController(),
      onProgress,
      onFailure,
      enableBackgroundInvestigation = true,
      debug = false,
      workflowType = WorkflowType.RESEARCH,
      workflowId = '',
      waitForUserInput = false,
    } = inputs;

    // 准备结果对象
    const result: any = {
      chatId: chatId || `agent-${Date.now()}`,
      content: [],
      reasoning_content: [],
      full_content: '',
      full_reasoning_content: '',
      finishReason: null,
      model: model || '',
      modelName: modelName || 'AI Agent',
      errMsg: '',
    };

    try {
      // 检查或初始化
      if (!this.chatModel) {
        this.logger.warn('模型未初始化，尝试重新初始化...');
        try {
          await this.initLangChain();
        } catch (initError) {
          this.logger.error(`重新初始化失败: ${handleError(initError)}`);
        }

        // 如果仍未初始化成功，则抛出错误
        if (!this.chatModel) {
          throw new Error('AI模型初始化失败，请稍后再试或联系管理员');
        }
      }

      // API密钥处理
      const effectiveApiKey =
        apiKey || (await this.globalConfigService.getConfigs(['openaiBaseKey'])).openaiBaseKey;
      if (!effectiveApiKey) {
        this.logger.error('未提供有效的API密钥');
        throw new Error('未配置API密钥，请在设置中配置或提供有效的API密钥');
      }

      // 确保工作流已初始化
      if (this.workflows.size === 0) {
        this.logger.warn('工作流未初始化，尝试初始化...');
        await this.initWorkflows();
        if (this.workflows.size === 0) {
          throw new Error('工作流初始化失败，请稍后再试');
        }
      }

      // 如果传入了apiKey或proxyUrl，需要更新模型
      if (apiKey || proxyUrl) {
        await this.updateChatModel(effectiveApiKey, proxyUrl, model);
      } else if (
        this.chatModel.apiKey === 'temp-key-for-initialization' ||
        this.chatModel.apiKey === 'sk-temp-initialization-key'
      ) {
        // 如果使用的是临时密钥，尝试更新为有效密钥
        await this.updateChatModel(effectiveApiKey, proxyUrl, model);
      }

      // 提取用户问题
      const lastUserMessage = messagesHistory.filter(msg => msg.role === 'user').pop();
      if (!lastUserMessage) {
        throw new Error('没有找到用户问题');
      }

      // 提取问题内容
      const content = lastUserMessage.content;
      let question = '';
      if (typeof content === 'string') {
        question = content;
      } else if (Array.isArray(content)) {
        question = content
          .map(item => {
            if (typeof item === 'string') return item;
            if (typeof item === 'object' && item !== null) {
              return 'text' in item ? String(item.text) : JSON.stringify(item);
            }
            return '';
          })
          .join(' ');
      } else if (content !== null && typeof content === 'object') {
        question = JSON.stringify(content);
      }

      // 根据workflowId决定是继续旧工作流还是创建新工作流
      if (workflowId) {
        const savedState = await this.getWorkflowState(workflowId);

        if (savedState) {
          this.logger.log(`继续执行工作流ID: ${workflowId}, 工作流类型: ${workflowType}`);

          // 获取工作流
          const workflow = this.getWorkflow(workflowType);

          // 添加用户输入和回调到状态
          savedState.messages.push(new HumanMessage(question));

          // 配置工作流选项
          const workflowOptions: WorkflowOptions = {
            chatId,
            apiKey,
            model,
            modelName,
            temperature,
            timeout,
            proxyUrl,
            abortController,
            onProgress: data => {
              // 如果设置了等待用户输入标志，并且工作流通知它需要用户输入，保存当前状态
              if (data.waitForUserInput && data.workflowStateId) {
                this.saveWorkflowState(data.workflowStateId, savedState).catch(error => {
                  this.logger.error(`保存工作流状态失败: ${handleError(error)}`);
                });
              }

              // 传递进度回调
              onProgress?.(data);
            },
            onFailure,
            debug,
            workflowStateId: workflowId, // 传递工作流状态ID
          };

          // 继续执行工作流
          const workflowResult = await workflow.continueFromState(savedState, workflowOptions);

          // 处理工作流执行结果
          if (workflow && workflowResult) {
            // 如果有错误信息
            if (workflowResult.errMsg) {
              // 记录错误但尝试返回尽可能多的已生成内容
              this.logger.error(`工作流执行出错: ${workflowResult.errMsg}`);

              // 如果有内容，仍然使用它，并标记中断
              if (workflowResult.content.length > 0 || workflowResult.full_content) {
                result.errMsg = workflowResult.errMsg;
                result.finishReason = 'error';

                // 优先使用针对性格式的内容
                if (workflowResult.content.length > 0) {
                  result.content = workflowResult.content;
                }
                // 如果没有格式化内容但有全文，使用全文
                else if (workflowResult.full_content) {
                  result.content = [{ type: 'text', text: workflowResult.full_content }];
                }

                // 确保full_content有值
                if (workflowResult.full_content) {
                  result.full_content = workflowResult.full_content;

                  // 添加中断说明
                  if (!result.full_content.includes('研究过程被中断')) {
                    result.full_content += '\n\n[注意: 研究过程被中断，以上是已完成的部分]';
                  }
                }

                // 仍然返回部分结果而不是抛出错误
                return result;
              } else {
                throw new Error(`工作流执行失败: ${workflowResult.errMsg}`);
              }
            }

            // 复制结果
            result.content = workflowResult.content;
            result.full_content = workflowResult.full_content;
            result.finishReason = workflowResult.finishReason;
          } else {
            this.logger.warn(`未找到工作流状态或已过期，创建新工作流. ID: ${workflowId}`);
          }
        } else {
          this.logger.warn(`未找到工作流状态或已过期，创建新工作流. ID: ${workflowId}`);
        }
      }

      // 获取工作流
      this.logger.debug(`准备获取工作流: ${workflowType}`);
      const workflow = this.getWorkflow(workflowType);

      // 配置工作流选项
      const newWorkflowId =
        workflowId || `wf-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      const workflowOptions: WorkflowOptions = {
        chatId,
        apiKey,
        model,
        modelName,
        temperature,
        timeout,
        proxyUrl,
        abortController,
        onProgress: data => {
          // 如果工作流通知它需要用户输入，保存当前状态
          if (data.waitForUserInput && data.workflowStateId) {
            // 这里需要访问当前工作流状态，我们使用回调参数中的状态ID
            // 保存状态操作应该由工作流内部调用
            // 此处仅传递回调
          }

          // 传递进度回调
          onProgress?.(data);
        },
        onFailure,
        debug,
        enableBackgroundInvestigation,
        workflowStateId: newWorkflowId, // 将新的工作流ID传递给工作流
        waitForUserInput,
      };

      // 执行工作流
      this.logger.debug(`开始执行工作流: ${workflowType}, 问题: ${question.substring(0, 100)}...`);
      const workflowResult = await workflow.execute(question, workflowOptions);

      // 添加工作流ID到结果
      workflowResult.workflowId = newWorkflowId;

      // 将工作流结果合并到最终结果
      Object.assign(result, workflowResult);

      return result;
    } catch (error) {
      const errorMessage = handleError(error);
      this.logger.error(`代理执行失败: ${errorMessage}`);
      result.errMsg = errorMessage;
      onFailure?.(result);
      return result;
    }
  }

  /**
   * 更新聊天模型
   */
  private async updateChatModel(apiKey?: string, proxyUrl?: string, model?: string) {
    try {
      // 如果没有提供apiKey，尝试从配置中获取
      if (!apiKey) {
        const config = await this.globalConfigService.getConfigs(['openaiBaseKey']);
        apiKey = config.openaiBaseKey;
        if (!apiKey) {
          this.logger.warn('未提供API密钥，且全局配置中也没有设置API密钥');
          // 保持临时密钥，但创建模型对象
          apiKey = 'temp-key-for-initialization';
        }
      }

      const config = await this.globalConfigService.getConfigs([
        'openaiBaseUrl',
        'openaiBaseModel',
      ]);
      // 确保始终有值
      const apiBaseUrl = await correctApiBaseUrl(
        proxyUrl || config.openaiBaseUrl || 'https://api.openai.com/v1',
      );
      const modelName = model || config.openaiBaseModel || 'gpt-3.5-turbo';

      this.logger.log(`更新聊天模型: ${modelName}, API URL: ${apiBaseUrl}`);

      // 初始化或更新聊天模型
      try {
        this.chatModel = new ChatOpenAI({
          modelName: modelName,
          temperature: 0,
          apiKey: apiKey,
          configuration: {
            baseURL: apiBaseUrl,
          },
        });
        this.logger.log('聊天模型更新成功');
      } catch (modelError) {
        this.logger.error(`更新聊天模型失败: ${handleError(modelError)}`);
        throw modelError;
      }

      // 如果工作流已初始化，更新它们
      if (this.workflows.size > 0) {
        // 更新各工作流的模型
        this.logger.log(`开始更新 ${this.workflows.size} 个工作流的模型...`);
        let updateCount = 0;

        for (const [type, workflow] of this.workflows.entries()) {
          try {
            await workflow.initialize(this.chatModel, this.mcpService, this.globalConfigService);
            updateCount++;
            this.logger.log(`工作流 ${type} 的模型更新成功`);
          } catch (workflowError) {
            this.logger.error(`更新工作流 ${type} 的模型失败: ${handleError(workflowError)}`);
            // 继续更新其他工作流
          }
        }

        this.logger.log(`成功更新了 ${updateCount}/${this.workflows.size} 个工作流的模型`);
      } else {
        this.logger.warn('没有已初始化的工作流，跳过工作流模型更新');
        // 尝试重新初始化工作流
        await this.initWorkflows();
      }
    } catch (error) {
      this.logger.error(`更新聊天模型失败: ${handleError(error)}`);
      throw error;
    }
  }

  /**
   * 公共接口，保持向后兼容
   */
  async handleRequest(dto: AgentRequestDto): Promise<Observable<string>> {
    try {
      // 确定要使用的工作流类型
      const workflowType = WorkflowType.RESEARCH; // 默认使用研究工作流

      // 获取工作流
      const workflow = this.getWorkflow(workflowType);

      // 直接调用工作流的Observable执行方法
      return await workflow.executeAsObservable(dto.question, {
        debug: dto.debug,
        maxPlanIterations: dto.maxPlanIterations,
        maxStepNum: dto.maxStepNum,
      });
    } catch (error) {
      const errorMessage = handleError(error);
      this.logger.error(errorMessage);
      throw error;
    }
  }

  /**
   * 确保ChatModel已经初始化
   */
  private async ensureChatModelInitialized() {
    if (!this.chatModel) {
      this.logger.warn('ChatModel尚未初始化，正在尝试初始化...');
      await this.initLangChain();
    }
  }

  /**
   * 通用提示词优化器
   * 接收简单文本和模型参数，返回标准化格式的优化提示词
   */
  async generateSimplePrompt(dto: SimplePromptGenerateDto) {
    try {
      this.logger.log(`开始生成通用提示词优化，原文本: ${dto.text.substring(0, 30)}...`);

      // 获取模型信息
      const modelInfo = await this.modelsService.getCurrentModelKeyInfo(dto.model || 'gpt-4');
      if (!modelInfo) {
        throw new Error(`未找到模型信息: ${dto.model || 'gpt-4'}`);
      }

      // 确保ChatModel已经初始化
      await this.ensureChatModelInitialized();

      // 创建临时模型实例，使用指定模型的API密钥和参数
      const tempModel = new ChatOpenAI({
        modelName: modelInfo.model || 'gpt-4',
        temperature: 0,
        apiKey: modelInfo.key,
        configuration: {
          baseURL: modelInfo.proxyUrl || 'https://api.openai.com/v1',
        },
      });

      // 标准的提示词优化规则JSON Schema
      const metaPromptSchema = {
        title: '提示词优化专家',
        description: 'AI提示词结构化优化系统',
        prompt: `# 提示词优化引擎
## 角色定义
你是一位提示词工程专家，擅长优化各类AI提示词。

## 核心目标
分析原始提示词，应用结构化优化技术，生成高质量提示词，确保AI能产出最佳回应。

## 优化方法
1. **结构优化**
   - 创建清晰层次结构
   - 去除冗余内容
   - 突出关键指令

2. **意图明确化**
   - 提炼核心需求
   - 消除模糊表达
   - 设置明确目标

3. **上下文丰富**
   - 补充必要背景
   - 设定合理约束
   - 添加领域知识

4. **执行指导**
   - 拆分复杂任务
   - 提供步骤引导
   - 设置质量检查

5. **输出规范**
   - 定义输出格式
   - 设置组织标准
   - 明确质量要求

## 任务类型优化策略
- **推理型**: 建立思考框架，设置验证机制
- **创意型**: 扩展思维空间，激发创新思路
- **分析型**: 构建评估体系，强化逻辑链条
- **结构化输出**: 提供格式规范，设计验证流程

## 规范要求
以JSON格式返回以下内容:
- title: 简短应用标题(5-10字)
- description: 简要描述(不超过20字)
- prompt: 完整优化后的提示词`,
      };

      // 创建系统提示词
      const systemPrompt = new SystemMessage(`你是一位提示词工程专家，专门优化AI提示词，使其更有效。

请分析并优化用户提供的原始提示词，返回格式严格遵循JSON：
{
  "title": "简短的应用标题(5-10字)",
  "description": "简要描述(不超过20字)",
  "prompt": "完整优化后的提示词内容"
}

优化时应用以下原则:
${JSON.stringify(metaPromptSchema.prompt, null, 2)}

重要：输出必须是可解析的JSON格式，不要添加任何其他内容。`);

      // 创建用户提示词
      const userPrompt = new HumanMessage(`请优化以下提示词：\n\n${dto.text}`);

      // 调用模型生成优化后的提示词
      const response = await tempModel.invoke([systemPrompt, userPrompt]);
      let responseContent = response.content as string;

      try {
        // 尝试提取JSON部分
        if (responseContent.includes('```json')) {
          responseContent = responseContent.split('```json')[1].split('```')[0].trim();
        } else if (responseContent.includes('```')) {
          responseContent = responseContent.split('```')[1].split('```')[0].trim();
        }

        // 解析JSON
        const parsedResponse = JSON.parse(responseContent);

        // 确保包含所需字段
        if (!parsedResponse.title || !parsedResponse.description || !parsedResponse.prompt) {
          throw new Error('响应缺少必要字段');
        }

        // 返回处理后的结果
        return parsedResponse;
      } catch (parseError) {
        this.logger.error(`解析响应失败: ${handleError(parseError)}`);

        // 如果无法解析，构造一个基本结构
        return {
          title: '提示词优化结果',
          description: 'AI优化的提示词',
          prompt: responseContent,
        };
      }
    } catch (error) {
      this.logger.error(`生成通用提示词失败: ${handleError(error)}`);
      throw error;
    }
  }
}
