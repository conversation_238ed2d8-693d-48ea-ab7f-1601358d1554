import { Modu<PERSON> } from '@nestjs/common';
import { GlobalConfigModule } from '../globalConfig/globalConfig.module';
import { MCPModule } from '../mcp/mcp.module';
import { ModelsModule } from '../models/models.module';
import { RedisCacheModule } from '../redisCache/redisCache.module';
import { RedisCacheService } from '../redisCache/redisCache.service';
import { AgentController } from './agent.controller';
import { AgentService } from './agent.service';

@Module({
  imports: [GlobalConfigModule, MCPModule, ModelsModule, RedisCacheModule],
  controllers: [AgentController],
  providers: [AgentService, RedisCacheService],
  exports: [AgentService],
})
export class AgentModule {}
