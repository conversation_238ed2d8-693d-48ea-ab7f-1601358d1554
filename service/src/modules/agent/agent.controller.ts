import { SuperAuthGuard } from '@/common/auth/superAuth.guard';
import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AgentService } from './agent.service';
import { SimplePromptGenerateDto } from './dto/generate-meta-prompt.dto';

@ApiTags('agent')
@Controller('agent')
export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  @Post('optimize-prompt')
  @ApiOperation({ summary: '通用提示词优化' })
  @UseGuards(SuperAuthGuard)
  @ApiBearerAuth()
  optimizePrompt(@Body() body: SimplePromptGenerateDto) {
    return this.agentService.generateSimplePrompt(body);
  }
}
