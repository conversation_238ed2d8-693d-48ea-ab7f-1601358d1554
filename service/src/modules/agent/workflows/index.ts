/**
 * 工作流模块索引文件
 * 导出所有工作流实现
 */

// 导出类型定义
export * from './types';

// 导出研究工作流
export { ResearchWorkflow } from './research';

// 导出工作流工厂函数
import { ResearchWorkflow } from './research';
import { Workflow } from './types';

/**
 * 工作流类型枚举
 */
export enum WorkflowType {
  RESEARCH = 'research',
  // 未来可能的其他工作流类型
}

/**
 * 工作流工厂函数
 * @param type 工作流类型
 * @returns 工作流实例
 */
export function createWorkflow(type: WorkflowType): Workflow {
  switch (type) {
    case WorkflowType.RESEARCH:
      return new ResearchWorkflow();
    default:
      throw new Error(`未知的工作流类型: ${type}`);
  }
}
