import { GlobalConfigService } from '@/modules/globalConfig/globalConfig.service';
import { MCPService } from '@/modules/mcp/mcp.service';
import { HumanMessage } from '@langchain/core/messages';
import { END, MemorySaver, START, StateGraph } from '@langchain/langgraph';
import { ChatOpenAI } from '@langchain/openai';
import { Logger } from '@nestjs/common';
import { Observable, Subject } from 'rxjs';

import { Workflow, WorkflowConfig, WorkflowOptions, WorkflowResult } from '../types';
import {
  ResearchNodeType,
  ResearchState,
  ResearchStateType,
  ResearchWorkflowOptions,
} from './types';

// 导入节点创建函数
import { createBackgroundInvestigatorNode } from './nodes/backgroundInvestigator';
import { createCoordinatorNode } from './nodes/coordinator';
import { createPlannerNode } from './nodes/planner';
import { createReporterNode } from './nodes/reporter';
import { createResearcherNode } from './nodes/researcher';
import { createToolEvaluatorNode } from './nodes/toolEvaluator';

/**
 * 研究工作流实现
 */
export class ResearchWorkflow implements Workflow {
  private logger = new Logger(ResearchWorkflow.name);
  private chatModel: ChatOpenAI;
  private mcpService: MCPService;
  private globalConfigService: any;

  /**
   * 工作流配置
   */
  config: WorkflowConfig = {
    name: 'research',
    description: '深入研究分析工作流',
    version: '1.0.0',
  };

  /**
   * 初始化工作流
   */
  async initialize(
    chatModel: ChatOpenAI,
    mcpService?: MCPService,
    globalConfigService?: GlobalConfigService,
  ): Promise<void> {
    this.chatModel = chatModel;

    // 保存MCP服务引用
    if (mcpService) {
      this.mcpService = mcpService;
      this.logger.log('已初始化MCP服务，支持工具调用');
    } else {
      this.logger.warn('未提供MCP服务，研究能力将受限');
    }

    this.globalConfigService = globalConfigService;

    // 添加更多调试信息
    console.log(
      'ResearchWorkflow - 保存后的this.globalConfigService:',
      this.globalConfigService ? '已保存' : '未保存',
    );

    if (!globalConfigService) {
      this.logger.warn('未提供GlobalConfigService，工具评估功能可能受限');
    }

    this.logger.log(`研究工作流 v${this.config.version} 初始化完成`);
  }

  /**
   * 执行研究工作流
   */
  async execute(question: string, options: WorkflowOptions = {}): Promise<WorkflowResult> {
    const {
      chatId = `research-${Date.now()}`,
      model = '',
      modelName = 'Research Agent',
      temperature = 0,
      abortController = new AbortController(),
      onProgress,
      onFailure,
      debug = false,
      maxPlanIterations = 1,
      maxStepNum = 3,
    } = options;

    // 准备结果对象
    const result: WorkflowResult = {
      chatId,
      content: [],
      full_content: '',
      finishReason: null,
      model,
      modelName,
      errMsg: '',
    };

    try {
      if (!this.chatModel) {
        throw new Error('研究工作流未初始化，请先调用initialize方法');
      }

      // 执行研究流程
      const researchResult = await this.runResearchFlow(question, {
        enableBackgroundInvestigation: true,
        debug,
        maxPlanIterations,
        maxStepNum,
        abortController,
        onProgress: data => {
          // 将所有文本内容累积到full_content中
          if (data.content && Array.isArray(data.content)) {
            for (const item of data.content) {
              if (item.type === 'text' && item.text) {
                result.full_content += item.text;
              }
            }
          }

          // 如果是报告节点完成，标记为最终结果
          if (data.nodeType === ResearchNodeType.REPORTER && data.status === 'complete') {
            if (data.report) {
              result.content = [{ type: 'text', text: data.report }];
            }
            result.finishReason = 'stop';
          }

          // 传递给原始onProgress回调
          onProgress?.(data);
        },
      });

      // 如果还没有设置内容，使用最终研究结果
      if (result.content.length === 0) {
        result.content = [{ type: 'text', text: researchResult }];
      }

      // 确保full_content包含完整内容
      if (researchResult && !result.full_content.includes(researchResult)) {
        result.full_content += researchResult;
      }

      result.finishReason = 'stop';

      return result;
    } catch (error) {
      this.logger.error(`研究工作流执行失败: ${error.message}`);

      // 错误恢复：将所有已累积内容作为成功结果返回
      if (result.full_content) {
        this.logger.log(
          `工作流中断，但已收集到${result.full_content.length}字的内容，将作为成功结果返回`,
        );

        // 确保返回已收集的内容
        if (result.content.length === 0 && result.full_content) {
          result.content = [
            {
              type: 'text',
              text: `${result.full_content}`,
            },
          ];
        }

        result.finishReason = 'stop'; // 标记为成功完成，而非error
      } else {
        // 如果没有任何内容，则创建一个空结果，但仍标记为成功
        result.finishReason = 'stop';
        result.content = [{ type: 'text', text: `研究已完成` }];
        result.full_content = `研究已完成`;
      }

      return result; // 不调用onFailure，视为成功
    }
  }

  /**
   * 从保存的状态继续执行工作流
   * @param savedState 保存的工作流状态
   * @param options 执行选项
   */
  async continueFromState(savedState: any, options: WorkflowOptions = {}): Promise<WorkflowResult> {
    const {
      chatId = `research-${Date.now()}`,
      model = '',
      modelName = 'Research Agent',
      temperature = 0,
      abortController = new AbortController(),
      onProgress,
      onFailure,
      debug = false,
      maxPlanIterations = 1,
      maxStepNum = 3,
      workflowStateId,
    } = options;

    // 准备结果对象
    const result: WorkflowResult = {
      chatId,
      content: [],
      full_content: '',
      finishReason: null,
      model,
      modelName,
      errMsg: '',
    };

    try {
      if (!this.chatModel) {
        throw new Error('研究工作流未初始化，请先调用initialize方法');
      }

      // 确保保存的状态有效
      if (!savedState || !savedState.messages) {
        throw new Error('无效的工作流状态');
      }

      this.logger.log(`从保存的状态继续执行工作流, 消息数量: ${savedState.messages.length}`);

      // 恢复进度回调
      savedState.onProgress = data => {
        // 将所有文本内容累积到full_content中
        if (data.content && Array.isArray(data.content)) {
          for (const item of data.content) {
            if (item.type === 'text' && item.text) {
              result.full_content += item.text;
            }
          }
        }

        // 如果是报告节点完成，标记为最终结果
        if (data.nodeType === ResearchNodeType.REPORTER && data.status === 'complete') {
          if (data.report) {
            result.content = [{ type: 'text', text: data.report }];
          }
          result.finishReason = 'stop';
        }

        // 传递给原始onProgress回调
        if (data.waitForUserInput && workflowStateId) {
          // 添加工作流状态ID
          data.workflowStateId = workflowStateId;
        }
        onProgress?.(data);
      };

      // 提取最新问题（最后一条消息）
      const lastMessage = savedState.messages[savedState.messages.length - 1];
      const question = lastMessage.content;

      // 执行研究流程，传入保存的状态
      const researchResult = await this.runResearchFlowFromState(savedState, {
        enableBackgroundInvestigation: true,
        debug,
        maxPlanIterations,
        maxStepNum,
        abortController,
        workflowStateId,
      });

      // 如果还没有设置内容，使用最终研究结果
      if (result.content.length === 0) {
        result.content = [{ type: 'text', text: researchResult }];
      }

      // 确保full_content包含完整内容
      if (researchResult && !result.full_content.includes(researchResult)) {
        result.full_content += researchResult;
      }

      result.finishReason = 'stop';

      return result;
    } catch (error) {
      this.logger.error(`从状态继续执行研究工作流失败: ${error.message}`);

      // 错误恢复：将所有已累积内容作为成功结果返回
      if (result.full_content) {
        this.logger.log(
          `工作流中断，但已收集到${result.full_content.length}字的内容，将作为成功结果返回`,
        );

        // 确保返回已收集的内容
        if (result.content.length === 0 && result.full_content) {
          result.content = [
            {
              type: 'text',
              text: `${result.full_content}`,
            },
          ];
        }

        result.finishReason = 'stop'; // 标记为成功完成，而非error
      } else {
        // 如果没有任何内容，则创建一个空结果，但仍标记为成功
        result.finishReason = 'stop';
        result.content = [{ type: 'text', text: `研究已完成` }];
        result.full_content = `研究已完成`;
      }

      return result; // 不调用onFailure，视为成功
    }
  }

  /**
   * 作为Observable执行研究工作流
   */
  async executeAsObservable(
    question: string,
    options: WorkflowOptions = {},
  ): Promise<Observable<string>> {
    try {
      const result$ = new Subject<string>();

      // 通过主要接口执行研究
      const researchResult = await this.execute(question, {
        ...options,
        onProgress: data => {
          // 只有在最终完成时才发送结果
          if (data.finishReason === 'stop' && data.content?.[0]?.text) {
            if (!result$.closed) {
              result$.next(data.content[0].text);
              result$.complete();
            }
          }

          // 同时传递给原始onProgress
          options.onProgress?.(data);
        },
        onFailure: data => {
          if (!result$.closed) {
            // 如果有内容但失败了，返回已有内容
            if (data.content && data.content.length > 0 && data.content[0].text) {
              result$.next(data.content[0].text);
            } else if (data.full_content) {
              // 确保返回full_content，即使有错误
              result$.next(data.full_content);
            } else {
              result$.next('研究已完成');
            }
            result$.complete();
          }

          // 由于我们的execute方法不再调用onFailure，这段逻辑应该不会执行
          // 但为了完整起见，仍保留它，并调整为成功状态的消息
          options.onFailure?.(data);
        },
      });

      // 如果没有通过onProgress发送结果，在这里发送
      if (!result$.closed) {
        const finalContent =
          researchResult.content?.[0]?.text || researchResult.errMsg || '研究完成';
        result$.next(finalContent);
        result$.complete();
      }

      return result$.asObservable();
    } catch (error) {
      this.logger.error(`研究工作流Observable执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行研究流程 - 内部方法
   */
  private async runResearchFlow(
    question: string,
    options: ResearchWorkflowOptions = {},
  ): Promise<string> {
    const {
      enableBackgroundInvestigation = true,
      debug = false,
      maxPlanIterations = 1,
      maxStepNum = 3,
      abortController,
      onProgress,
    } = options;

    try {
      // 创建节点
      const coordinatorNode = createCoordinatorNode(this.logger, this.chatModel);

      const backgroundInvestigatorNode = createBackgroundInvestigatorNode(
        this.logger,
        this.chatModel,
      );

      // 创建规划者、研究员和报告员节点
      const plannerNode = createPlannerNode(this.logger, this.chatModel);

      const researcherNode = createResearcherNode(this.logger, this.chatModel);

      const reporterNode = createReporterNode(this.logger, this.chatModel);

      // 创建工具评估节点（如果MCP服务可用）
      let toolEvaluatorNode = null;
      if (this.mcpService) {
        // 添加调试日志
        console.log(
          '创建工具评估节点前 - this.globalConfigService:',
          this.globalConfigService ? '存在' : '不存在',
        );
        if (this.globalConfigService) {
          console.log(
            '创建工具评估节点前 - this.globalConfigService类型:',
            typeof this.globalConfigService,
          );
          console.log(
            '创建工具评估节点前 - this.globalConfigService有getConfigs方法:',
            typeof this.globalConfigService.getConfigs === 'function',
          );
        }

        toolEvaluatorNode = createToolEvaluatorNode(
          this.logger,
          this.chatModel,
          this.mcpService,
          this.globalConfigService,
        );
        this.logger.log('已创建工具评估节点');
      } else {
        this.logger.warn('未提供MCP服务，工具评估功能将不可用');
      }

      // 工具评估路由 - 决定下一步节点
      const routeAfterToolEvaluation = (state: ResearchStateType) => {
        // 检查是否应该退出工具评估循环
        if (state.shouldExitToolEvaluation) {
          this.logger.log(`工具评估完成，退出工具评估循环，进入背景调查节点`);
          return ResearchNodeType.BACKGROUND_INVESTIGATOR;
        }

        // 如果有工具结果且计数少于最大值，重新走工具评估节点（自循环）
        if (state.toolResults?.length > 0 && (state.toolCallCount || 0) < 3) {
          this.logger.log(`工具使用后继续评估，当前工具使用次数: ${state.toolCallCount}`);
          return ResearchNodeType.TOOL_EVALUATOR;
        }

        // 达到最大工具调用次数或不需要更多工具，转到背景调查节点
        this.logger.log(`工具评估完成，进入背景调查节点`);
        return ResearchNodeType.BACKGROUND_INVESTIGATOR;
      };

      // 创建状态图
      // 使用as any临时解决类型兼容问题
      let graph = new StateGraph(ResearchState)
        // 添加节点
        .addNode(ResearchNodeType.COORDINATOR, coordinatorNode)
        .addNode(ResearchNodeType.BACKGROUND_INVESTIGATOR, backgroundInvestigatorNode)
        .addNode(ResearchNodeType.PLANNER, plannerNode)
        .addNode(ResearchNodeType.RESEARCHER, researcherNode)
        .addNode(ResearchNodeType.REPORTER, reporterNode);

      // 添加工具评估节点（如果可用）
      if (toolEvaluatorNode) {
        graph = (graph as any).addNode(ResearchNodeType.TOOL_EVALUATOR, toolEvaluatorNode);
      }

      // 添加基本边
      graph = (graph as any).addEdge(START, ResearchNodeType.COORDINATOR);

      // 根据是否有工具评估节点添加不同的边
      if (toolEvaluatorNode) {
        // 修改执行顺序：协调者 -> 工具评估节点 -> 背景调查节点 -> 规划者 -> 研究员 -> 报告员
        graph = (graph as any)
          .addEdge(ResearchNodeType.COORDINATOR, ResearchNodeType.TOOL_EVALUATOR)
          .addConditionalEdges(ResearchNodeType.TOOL_EVALUATOR, routeAfterToolEvaluation, {
            [ResearchNodeType.TOOL_EVALUATOR]: ResearchNodeType.TOOL_EVALUATOR,
            [ResearchNodeType.BACKGROUND_INVESTIGATOR]: ResearchNodeType.BACKGROUND_INVESTIGATOR,
          })
          .addEdge(ResearchNodeType.BACKGROUND_INVESTIGATOR, ResearchNodeType.PLANNER)
          .addEdge(ResearchNodeType.PLANNER, ResearchNodeType.RESEARCHER)
          .addEdge(ResearchNodeType.RESEARCHER, ResearchNodeType.REPORTER)
          .addEdge(ResearchNodeType.REPORTER, END);
      } else {
        // 如果没有工具评估节点，则直接从协调者到背景调查节点
        graph = (graph as any)
          .addEdge(ResearchNodeType.COORDINATOR, ResearchNodeType.BACKGROUND_INVESTIGATOR)
          .addEdge(ResearchNodeType.BACKGROUND_INVESTIGATOR, ResearchNodeType.PLANNER)
          .addEdge(ResearchNodeType.PLANNER, ResearchNodeType.RESEARCHER)
          .addEdge(ResearchNodeType.RESEARCHER, ResearchNodeType.REPORTER)
          .addEdge(ResearchNodeType.REPORTER, END);
      }

      // 创建内存保存器
      const memorySaver = new MemorySaver();

      // 编译图并启用内存保存器
      const app = graph.compile({
        checkpointer: memorySaver,
      });

      // 初始化状态并执行图
      const input = {
        messages: [new HumanMessage(question)],
        debug,
        maxPlanIterations,
        maxStepNum,
        enableBackgroundInvestigation: true,
        onProgress,
        toolResults: [], // 初始化工具结果数组
        toolCallCount: 0, // 初始化工具调用计数
      };

      // 生成唯一线程ID
      const threadId = `research-${Date.now()}`;

      // 如果传入了AbortController，可以处理取消请求
      const graphConfig: any = {
        configurable: {
          thread_id: threadId,
        },
      };

      if (abortController) {
        graphConfig.signal = abortController.signal;
      }

      try {
        // 执行图并获取结果
        const result = await app.invoke(input, graphConfig);

        // 从结果中获取最终报告
        const finalMessages = result.messages;
        const finalMessage = finalMessages[finalMessages.length - 1];

        this.logger.log(`研究流程完成，返回最终报告(${finalMessage.content?.length || 0}字)`);
        return finalMessage.content as string;
      } catch (error) {
        // 检查是否是被用户取消的请求
        if (error.message === 'Aborted' || error.name === 'AbortError') {
          this.logger.warn('研究流程被用户中断，无法获取完整结果');
          throw new Error('研究被中断');
        }

        throw error;
      }
    } catch (error) {
      this.logger.error(`研究流程执行失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从保存的状态继续执行研究流程 - 内部方法
   */
  private async runResearchFlowFromState(
    savedState: any,
    options: ResearchWorkflowOptions = {},
  ): Promise<string> {
    const {
      enableBackgroundInvestigation = true,
      debug = false,
      maxPlanIterations = 1,
      maxStepNum = 3,
      abortController,
      workflowStateId,
    } = options;

    try {
      // 创建节点
      const coordinatorNode = createCoordinatorNode(this.logger, this.chatModel);

      const backgroundInvestigatorNode = createBackgroundInvestigatorNode(
        this.logger,
        this.chatModel,
      );

      // 创建规划者、研究员和报告员节点
      const plannerNode = createPlannerNode(this.logger, this.chatModel);

      const researcherNode = createResearcherNode(this.logger, this.chatModel);

      const reporterNode = createReporterNode(this.logger, this.chatModel);

      // 创建工具评估节点（如果MCP服务可用）
      let toolEvaluatorNode = null;
      if (this.mcpService) {
        toolEvaluatorNode = createToolEvaluatorNode(
          this.logger,
          this.chatModel,
          this.mcpService,
          this.globalConfigService,
        );
        this.logger.log('已创建工具评估节点');
      } else {
        this.logger.warn('未提供MCP服务，工具评估功能将不可用');
      }

      // 工具评估路由 - 决定下一步节点
      const routeAfterToolEvaluation = (state: ResearchStateType) => {
        // 检查是否应该退出工具评估循环
        if (state.shouldExitToolEvaluation) {
          return ResearchNodeType.BACKGROUND_INVESTIGATOR;
        }

        // 如果有工具结果且计数少于最大值，重新走工具评估节点（自循环）
        if (state.toolResults?.length > 0 && (state.toolCallCount || 0) < 3) {
          return ResearchNodeType.TOOL_EVALUATOR;
        }

        // 达到最大工具调用次数或不需要更多工具，转到背景调查节点
        return ResearchNodeType.BACKGROUND_INVESTIGATOR;
      };

      // 创建状态图
      // 使用as any临时解决类型兼容问题
      let graph = new StateGraph(ResearchState)
        // 添加节点
        .addNode(ResearchNodeType.COORDINATOR, coordinatorNode)
        .addNode(ResearchNodeType.BACKGROUND_INVESTIGATOR, backgroundInvestigatorNode)
        .addNode(ResearchNodeType.PLANNER, plannerNode)
        .addNode(ResearchNodeType.RESEARCHER, researcherNode)
        .addNode(ResearchNodeType.REPORTER, reporterNode);

      // 添加工具评估节点（如果可用）
      if (toolEvaluatorNode) {
        graph = (graph as any).addNode(ResearchNodeType.TOOL_EVALUATOR, toolEvaluatorNode);
      }

      // 添加基本边
      graph = (graph as any).addEdge(START, ResearchNodeType.COORDINATOR);

      // 根据是否有工具评估节点添加不同的边
      if (toolEvaluatorNode) {
        graph = (graph as any)
          .addEdge(ResearchNodeType.COORDINATOR, ResearchNodeType.TOOL_EVALUATOR)
          .addConditionalEdges(ResearchNodeType.TOOL_EVALUATOR, routeAfterToolEvaluation, {
            [ResearchNodeType.TOOL_EVALUATOR]: ResearchNodeType.TOOL_EVALUATOR,
            [ResearchNodeType.BACKGROUND_INVESTIGATOR]: ResearchNodeType.BACKGROUND_INVESTIGATOR,
          })
          .addEdge(ResearchNodeType.BACKGROUND_INVESTIGATOR, ResearchNodeType.PLANNER)
          .addEdge(ResearchNodeType.PLANNER, ResearchNodeType.RESEARCHER)
          .addEdge(ResearchNodeType.RESEARCHER, ResearchNodeType.REPORTER)
          .addEdge(ResearchNodeType.REPORTER, END);
      } else {
        graph = (graph as any)
          .addEdge(ResearchNodeType.COORDINATOR, ResearchNodeType.BACKGROUND_INVESTIGATOR)
          .addEdge(ResearchNodeType.BACKGROUND_INVESTIGATOR, ResearchNodeType.PLANNER)
          .addEdge(ResearchNodeType.PLANNER, ResearchNodeType.RESEARCHER)
          .addEdge(ResearchNodeType.RESEARCHER, ResearchNodeType.REPORTER)
          .addEdge(ResearchNodeType.REPORTER, END);
      }

      // 创建内存保存器
      const memorySaver = new MemorySaver();

      // 编译图并启用内存保存器
      const app = graph.compile({
        checkpointer: memorySaver,
      });

      // 设置唯一线程ID
      const threadId = workflowStateId || `research-${Date.now()}`;

      // 如果传入了AbortController，可以处理取消请求
      const graphConfig: any = {
        configurable: {
          thread_id: threadId,
        },
      };

      if (abortController) {
        graphConfig.signal = abortController.signal;
      }

      try {
        // 使用保存的状态继续执行工作流
        this.logger.log(`继续执行工作流，线程ID: ${threadId}`);
        const result = await app.invoke(savedState, graphConfig);

        // 从结果中获取最终报告
        const finalMessages = result.messages;
        const finalMessage = finalMessages[finalMessages.length - 1];

        this.logger.log(
          `从保存状态继续的研究流程完成，返回最终报告(${finalMessage.content?.length || 0}字)`,
        );
        return finalMessage.content as string;
      } catch (error) {
        // 检查是否是被用户取消的请求
        if (error.message === 'Aborted' || error.name === 'AbortError') {
          this.logger.warn('从保存状态继续的研究流程被用户中断，无法获取完整结果');
          throw new Error('研究被中断');
        }

        throw error;
      }
    } catch (error) {
      this.logger.error(`从保存状态继续研究流程失败: ${error.message}`);
      throw error;
    }
  }
}
