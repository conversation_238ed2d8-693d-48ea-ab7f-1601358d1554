import { SystemMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { Logger } from '@nestjs/common';
import { ResearcherNodeType, ResearchNodeType, ResearchStateType } from '../types';

/**
 * Researcher的提示词模板
 * 负责执行研究计划中的各个步骤
 */
const RESEARCHER_PROMPT = `
你是一位专业的研究员，擅长深入分析问题并提供高质量的研究结果。

# 职责

你的主要职责是：
- 执行分配给你的研究步骤
- 深入挖掘相关信息
- 从多个角度分析问题
- 提供基于事实和逻辑的详细分析

# 研究输出结构

请为每个研究步骤产出以下内容：
1. **关键发现**：列出本步骤研究的主要发现和结论
2. **详细分析**：提供支持结论的详细分析和论据
3. **局限性**：指出研究的局限性和需要进一步确认的地方
4. **参考资料**：列出所有使用的参考资料

# 研究方法

在执行研究时，请：
- 尝试从多个角度进行深入分析
- 考虑不同的观点和可能的结论
- 提供具体的事实、数据和论据来支持你的发现
- 基于提供的背景信息和研究计划进行分析
- 确保研究是深入、全面且有洞察力的

# 执行规则

- 分析并理解当前研究步骤的目标和范围
- 使用提供的背景信息作为起点
- 深入挖掘该步骤要求研究的各个方面
- 从多个角度分析问题
- 提供详细、具体的分析而非笼统的观点
- 关注事实和逻辑推理，不要仅依赖个人观点
- 明确指出分析中的任何假设或限制
- 结构化输出你的发现和分析

# 注意事项

- 保持研究的客观性和全面性
- 避免个人偏见，专注于事实和逻辑
- 不要超出分配给你的研究步骤范围
- 如果某些信息无法确定，请明确说明
- 在结论中考虑多种可能性和观点
- 突出重要的发现和见解
- 确保准确引用所有来源，并在报告末尾列出参考资料
`;

/**
 * 创建研究员节点
 * @param logger 日志记录器
 * @param chatModel 聊天模型
 * @returns 研究员节点函数
 */
export function createResearcherNode(logger: Logger, chatModel: ChatOpenAI): ResearcherNodeType {
  return async (state: ResearchStateType) => {
    logger.log('执行研究员节点 - 深入研究与分析');
    const { messages, checkpoints, maxStepNum = 3 } = state;

    // 通知进度
    const onProgress = state.onProgress;

    // 获取研究计划和当前进度
    const researchPlan = checkpoints.research_plan || '';
    let structuredPlan;
    try {
      structuredPlan = checkpoints.structured_plan
        ? JSON.parse(checkpoints.structured_plan)
        : { steps: [{ title: '综合研究', description: '基于所有可用信息进行研究' }] };
    } catch (error) {
      logger.error(`解析结构化计划失败: ${error.message}`);
      structuredPlan = {
        steps: [{ title: '综合研究', description: '基于所有可用信息进行研究' }],
      };

      // 向前端发送错误信息
      onProgress?.({
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
        stepName: '结构化计划解析失败',
        progress: 0.1,
      });
    }

    // 获取背景调查和搜索数据
    const backgroundData = checkpoints.background_investigation || '';

    // 准备搜索结果数据
    let searchData = '';
    try {
      const rawSearchResults = checkpoints.raw_search_results || '[]';
      const parsedResults = JSON.parse(rawSearchResults);

      if (parsedResults && parsedResults.length > 0) {
        searchData = parsedResults
          .map(
            (result, idx) =>
              `[${idx + 1}] ${result.title}\nURL: ${result.link}\n${result.content}\n`,
          )
          .join('\n');
        logger.log(`成功解析了 ${parsedResults.length} 条搜索结果用于研究`);
      } else {
        logger.warn('没有可用的搜索结果用于研究');
      }
    } catch (error) {
      logger.error(`解析搜索结果失败: ${error.message}`);
      searchData = '警告: 搜索结果无法解析，请基于背景调查结果进行研究。';
    }

    // 确定要执行的步骤数量
    const steps = structuredPlan.steps || [];
    const stepCount = Math.min(steps.length, maxStepNum);

    if (stepCount === 0) {
      logger.warn('没有可执行的研究步骤');
      onProgress?.({
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
        stepName: '未找到研究步骤',
        progress: 0.1,
      });
      steps.push({ title: '综合研究', description: '基于所有可用信息进行综合分析' });
    }

    logger.log(`将执行 ${stepCount} 个研究步骤`);

    // 通知初始化状态
    onProgress?.({
      nodeType: ResearchNodeType.RESEARCHER,
      stepName: '初始化研究',
      totalSteps: stepCount,
      progress: 0.8,
      content: [
        {
          type: 'text',
          text: '\n',
        },
      ],
    });

    // 依次执行每个研究步骤
    const stepResults = [];
    let combinedFindings = '';

    for (let i = 0; i < stepCount; i++) {
      const step = steps[i];
      logger.log(`执行研究步骤 ${i + 1}/${stepCount}: ${step.title}`);

      // 计算当前整体进度 - 基于0.8到0.9之间的范围
      const progressStep = 0.1 / stepCount; // 将0.1的进度范围均分给各步骤
      const currentProgress = 0.8 + i * progressStep; // 起始进度0.8

      // 向前端发送当前步骤信息 - 过渡状态表达
      onProgress?.({
        nodeType: ResearchNodeType.RESEARCHER,
        stepName: `开始步骤 ${i + 1}: ${step.title}`,
        currentStep: i + 1,
        totalSteps: stepCount,
        stepTitle: step.title,
        progress: currentProgress,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 构建步骤特定的提示词
      const stepPrompt = `${RESEARCHER_PROMPT}

研究背景:
${backgroundData}

研究计划:
${researchPlan}

当前研究步骤 (${i + 1}/${stepCount}):
标题: ${step.title}
描述: ${step.description || '未提供详细描述'}

相关数据:
${searchData}

请基于以上信息，深入分析并完成当前研究步骤。你的回应应该包括:
1. 对步骤相关资料的分析
2. 关键发现和洞见
3. 可能的结论或假设
4. 与研究问题的关联性
5. 参考资料列表

引用格式要求:
请在分析和总结时，使用恰当的引用格式，在句子末尾标注引用的链接，使用[[序号](链接地址)]格式。例如：研究发现这种方法效果显著[[3](http://example.com)]。如果需要引用多个来源，可以连续使用如[[2](链接地址)][[5](链接地址)]的格式。

在报告末尾，添加"参考资料"部分，列出所有引用的来源，使用编号列表格式：
1. [来源标题] - [链接]
2. [来源标题] - [链接]

注意维持高质量的分析，确保你的结论有搜索结果的支持。`;

      // 执行当前研究步骤 - 流式处理
      let stepContent = '';

      try {
        // 使用流式调用
        const stream = await chatModel.stream([new SystemMessage(stepPrompt), ...messages]);

        // 通知进度切换到研究状态
        onProgress?.({
          nodeType: ResearchNodeType.RESEARCHER,
          stepName: `研究中: ${step.title}`,
          currentStep: i + 1,
          totalSteps: stepCount,
          stepTitle: step.title,
          progress: currentProgress + progressStep * 0.5, // 步骤进行中，增加一半步骤进度
          content: [
            {
              type: 'text',
              text: '\n',
            },
          ],
        });

        // 处理流式响应
        for await (const chunk of stream) {
          if (chunk.content) {
            stepContent += chunk.content;

            // 实时发送内容到前端
            onProgress?.({
              content: [
                {
                  type: 'text',
                  text: chunk.content,
                },
              ],
            });
          }
        }

        // 流式处理完成后添加额外空行
        onProgress?.({
          content: [
            {
              type: 'text',
              text: '\n',
            },
          ],
        });

        logger.log(`步骤 ${i + 1} 流式处理完成，内容长度: ${stepContent.length}字`);
      } catch (error) {
        logger.error(`步骤 ${i + 1} 流式处理失败: ${error.message}`);

        // 检查是否是被取消的请求
        if (error.message === 'Aborted' || error.name === 'AbortError') {
          // 如果已收集一些内容，添加说明并使用已收集的内容
          if (stepContent.length > 0) {
            logger.log(`步骤 ${i + 1} 被中断，但已收集到${stepContent.length}字的内容`);

            // 添加中断说明
            const interruptNote = `\n\n[注意: 该步骤执行过程被中断，以上是已完成的部分内容]`;
            stepContent += interruptNote;

            // 发送完整内容到前端
            onProgress?.({
              content: [
                {
                  type: 'text',
                  text: interruptNote,
                },
              ],
            });
          } else {
            // 如果没有内容，设置一个默认消息
            stepContent = `研究步骤 ${i + 1} (${step.title}) 被中断，未能完成研究。`;

            // 发送中断消息到前端
            onProgress?.({
              content: [
                {
                  type: 'text',
                  text: stepContent,
                },
              ],
            });
          }

          // 收集这个不完整的结果，然后结束研究
          if (stepContent) {
            stepResults.push({
              step: i + 1,
              title: step.title,
              content: stepContent,
              interrupted: true,
            });
            combinedFindings += `\n\n## ${step.title}\n${stepContent}`;
          }

          // 中断循环，不再处理后续步骤
          break;
        }

        // 如果是其他错误，尝试使用普通调用
        try {
          const stepResponse = await chatModel.invoke([new SystemMessage(stepPrompt), ...messages]);
          stepContent = stepResponse.content as string;

          // 发送完整内容到前端
          onProgress?.({
            content: [
              {
                type: 'text',
                text: stepContent,
              },
            ],
          });
        } catch (fallbackError) {
          // 如果备用调用也失败，使用错误消息
          logger.error(`步骤 ${i + 1} 备用调用也失败: ${fallbackError.message}`);
          stepContent = `研究步骤 ${i + 1} (${step.title}) 无法完成，发生错误: ${error.message}`;

          // 发送错误消息到前端
          onProgress?.({
            content: [
              {
                type: 'text',
                text: stepContent,
              },
            ],
          });

          // 如果是中断错误，停止后续步骤的执行
          if (fallbackError.message === 'Aborted' || fallbackError.name === 'AbortError') {
            // 收集这个不完整的结果，然后结束研究
            if (stepContent) {
              stepResults.push({
                step: i + 1,
                title: step.title,
                content: stepContent,
                interrupted: true,
              });
              combinedFindings += `\n\n## ${step.title}\n${stepContent}`;
            }

            // 中断循环，不再处理后续步骤
            break;
          }
        }
      }

      logger.log(`步骤 ${i + 1} 完成，响应长度: ${stepContent.length}字`);

      // 将步骤结果发送到前端 - 完成状态通知
      onProgress?.({
        nodeType: ResearchNodeType.RESEARCHER,
        stepName: `完成步骤 ${i + 1}: ${step.title}`,
        currentStep: i + 1,
        totalSteps: stepCount,
        stepTitle: step.title,
        stepResult: stepContent,
        progress: 0.8 + (i + 1) * progressStep, // 步骤完成，进度增加至下一步骤起始点
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 收集结果
      stepResults.push({
        step: i + 1,
        title: step.title,
        content: stepContent,
      });
      combinedFindings += `\n\n## ${step.title}\n${stepContent}`;
    }

    logger.log(`所有 ${stepCount} 个研究步骤已完成`);

    // 向前端发送完成信息 - 过渡到下一阶段
    onProgress?.({
      nodeType: ResearchNodeType.RESEARCHER,
      stepName: '研究阶段完成',
      progress: 0.9,
      content: [
        {
          type: 'text',
          text: '\n',
        },
      ],
    });

    // 通知进度
    onProgress?.({
      nodeType: ResearchNodeType.RESEARCHER,
      stepName: '准备最终报告',
      findings: combinedFindings,
      stepResults: stepResults,
      progress: 0.9,
      content: [
        {
          type: 'text',
          text: '\n',
        },
      ],
    });

    // 将研究结果添加到检查点
    return {
      messages: [...messages],
      checkpoints: {
        ...checkpoints,
        research_findings: combinedFindings,
        step_results: JSON.stringify(stepResults),
        steps_completed: stepCount,
      },
    };
  };
}
