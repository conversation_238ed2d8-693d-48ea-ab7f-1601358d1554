import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { Logger } from '@nestjs/common';
import { PlannerNodeType, ResearchNodeType, ResearchStateType } from '../types';

/**
 * Planner的提示词模板
 * 负责制定研究计划
 */
const PLANNER_PROMPT = `
你是一位专业的研究规划者。你的任务是创建一个结构清晰、逻辑严密的研究计划，目的是全面解答用户的问题。

# 目标

为用户问题创建一个全面、详细的研究计划，该计划应包括收集必要信息的明确步骤。

# 研究计划结构

计划应包括：
1. **研究目标**：简明扼要地说明这项研究要解决的问题
2. **研究步骤**：每个步骤要有明确的目标、方法和预期成果
3. **所需信息**：列出每个步骤需要获取的具体信息

# 信息收集标准

成功的研究计划必须满足以下标准：

1. **全面覆盖**:
   - 信息必须覆盖主题的所有重要方面
   - 必须代表多种观点和角度
   - 应包括主流和替代视角

2. **足够深度**:
   - 表面信息是不够的
   - 需要详细的数据点、事实和统计数据
   - 需要来自多个来源的深入分析

3. **足够数量**:
   - 收集"刚好足够"的信息是不可接受的
   - 以丰富的相关信息为目标
   - 更多高质量信息总是优于较少信息

# 分析框架

规划信息收集时，请考虑以下关键方面，确保全面覆盖：

1. **历史背景**:
   - 需要哪些历史数据和趋势?
   - 相关事件的完整时间线是什么?
   - 主题如何随时间演变?

2. **当前状态**:
   - 需要收集哪些当前数据点?
   - 目前的详细情况如何?
   - 最近的发展有哪些?

3. **未来指标**:
   - 需要哪些预测数据或面向未来的信息?
   - 有哪些相关的预测和预计?
   - 应考虑哪些潜在的未来情景?

4. **利益相关者数据**:
   - 需要收集关于所有相关利益相关者的哪些信息?
   - 不同群体如何受到影响或参与?
   - 有哪些不同的观点和利益?

5. **量化数据**:
   - 应收集哪些全面的数字、统计数据和指标?
   - 需要来自多个来源的哪些数值数据?
   - 哪些统计分析是相关的?

6. **定性数据**:
   - 需要收集哪些非数值信息?
   - 哪些观点、证言和案例研究是相关的?
   - 哪些描述性信息提供背景?

7. **比较数据**:
   - 需要哪些比较点或基准数据?
   - 应该研究哪些类似案例或替代方案?
   - 这在不同情境下如何比较?

8. **风险数据**:
   - 应收集关于所有潜在风险的哪些信息?
   - 有哪些挑战、限制和障碍?
   - 存在哪些应急方案和缓解措施?

# 执行规则

- 计划应包含最多3个步骤，每个步骤应聚焦关键方面
- 确保每个步骤具体但全面，涵盖相关信息类别
- 根据研究问题优先考虑最重要的信息类别
- 适当时将相关研究点合并为单个步骤
- 详细说明每个步骤要收集的确切数据
- 优先考虑相关信息的深度和数量 - 有限信息是不可接受的
- 不要包括总结或整合已收集信息的步骤

# 注意事项

- 确保每个步骤都有明确、具体的数据点或要收集的信息
- 创建一个全面的数据收集计划，涵盖最关键的方面
- 优先考虑广度（覆盖基本方面）和深度（每个方面的详细信息）
- 不要满足于最少信息 - 目标是全面、详细的最终报告
- 有限或不足的信息将导致不充分的最终报告
`;

/**
 * 创建规划者节点
 * @param logger 日志记录器
 * @param chatModel 聊天模型
 * @returns 规划者节点函数
 */
export function createPlannerNode(logger: Logger, chatModel: ChatOpenAI): PlannerNodeType {
  return async (state: ResearchStateType) => {
    logger.log('执行规划者节点 - 制定详细研究计划');

    // 通知初始化状态，无需显式状态文本
    const onProgress = state.onProgress;
    onProgress?.({
      nodeType: ResearchNodeType.PLANNER,
      stepName: '初始化规划',
      progress: 0.6,
    });

    const { messages, checkpoints } = state;

    // 获取协调者分析和背景调查结果
    const coordinatorAnalysis = checkpoints.coordinator_analysis || '';
    const backgroundInvestigation = checkpoints.background_investigation || '';

    // 解析搜索结果并确保它可用于规划
    let searchInfo = '';
    try {
      const rawSearchResults = checkpoints.raw_search_results || '[]';
      const parsedResults = JSON.parse(rawSearchResults);

      if (parsedResults && parsedResults.length > 0) {
        searchInfo =
          '\n\n搜索结果摘要:\n' +
          parsedResults
            .map(
              (result, idx) =>
                `[${idx + 1}] ${result.title}\nURL: ${
                  result.link
                }\n摘要: ${result.content.substring(0, 200)}${
                  result.content.length > 200 ? '...' : ''
                }\n`,
            )
            .join('\n');

        logger.log(`成功解析了 ${parsedResults.length} 条搜索结果用于规划`);
      } else {
        logger.warn('没有可用的搜索结果用于规划');

        // 向前端发送警告
        onProgress?.({
          content: [
            {
              type: 'text',
              text: '\n',
            },
          ],
          stepName: '无搜索结果',
          progress: 0.62,
        });
      }
    } catch (error) {
      logger.error(`解析搜索结果失败: ${error.message}`);
      searchInfo = '\n\n注意: 搜索结果解析失败，请基于背景调查结果进行规划。';

      // 向前端发送错误信息
      onProgress?.({
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
        stepName: '搜索结果处理错误',
        progress: 0.62,
      });
    }

    // 检查背景调查结果
    if (!backgroundInvestigation) {
      logger.warn('没有找到背景调查结果，规划可能不完整');

      // 使用更直接的状态表达
      onProgress?.({
        nodeType: ResearchNodeType.PLANNER,
        stepName: '准备规划数据',
        progress: 0.64,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });
    } else {
      // 使用更直接的状态表达
      onProgress?.({
        nodeType: ResearchNodeType.PLANNER,
        stepName: '分析背景数据',
        progress: 0.64,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });
    }

    let researchPlan = '';
    let structuredPlan = '';

    try {
      // 生成规划前的状态通知
      onProgress?.({
        nodeType: ResearchNodeType.PLANNER,
        stepName: '制定研究计划',
        progress: 0.68,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 构建增强的提示词
      const enhancedPrompt = `${PLANNER_PROMPT}

协调者分析:
${coordinatorAnalysis}

背景调查结果:
${backgroundInvestigation}
${searchInfo}

请基于以上信息制定详细的研究计划，具体包括:
1. 研究目标（简洁明确）
2. 关键问题清单（列出3-5个待解决的核心问题）
3. 研究步骤（详细规划至少3-5个具体步骤，确保覆盖所有关键问题）
4. 每个步骤的预期成果
5. 可能的挑战和解决方案

确保研究计划充分利用了背景调查和搜索结果中的信息，特别是引用具体的信息源和关键数据点。`;

      logger.log('向模型发送规划请求 - 流式处理');

      let planContent = '';

      try {
        // 使用流式调用
        const stream = await chatModel.stream([new SystemMessage(enhancedPrompt), ...messages]);

        // 处理流式响应
        for await (const chunk of stream) {
          if (chunk.content) {
            planContent += chunk.content;

            // 实时发送内容到前端
            onProgress?.({
              content: [
                {
                  type: 'text',
                  text: chunk.content,
                },
              ],
            });
          }
        }

        // 流式处理完成后添加额外空行
        onProgress?.({
          content: [
            {
              type: 'text',
              text: '\n\n',
            },
          ],
        });

        logger.log(`研究计划流式生成完成，内容长度: ${planContent.length}字`);
      } catch (error) {
        logger.error(`研究计划流式处理失败: ${error.message}`);

        // 如果流式处理失败，尝试使用普通调用
        // 重建提示词
        const plannerPrompt = `
你是一位专业的研究规划者，负责制定详细的研究计划。

# 职责
你的主要职责是根据提供的背景信息，制定一个结构化、全面的研究计划。

# 规划要求
- 制定3-5个明确的研究步骤
- 确保步骤逻辑连贯，循序渐进
- 每个步骤应包含明确的目标和范围
- 考虑不同角度和视角
- 针对用户问题设计专门的研究方向

# 输出格式
请提供一个详细的研究计划，包括:
1. **研究目标**: 总体研究目标
2. **研究步骤**: 列出3-5个主要研究步骤，每步包含:
   - 步骤标题
   - 详细描述
   - 期望的具体成果
3. **优先级**: 指出哪些步骤最重要
4. **研究方法**: 推荐的研究方法和资源

以下是背景信息:
${backgroundInvestigation}

请根据以上背景信息，制定一个结构化的研究计划。
`;
        const response = await chatModel.invoke([new SystemMessage(plannerPrompt), ...messages]);

        planContent = response.content as string;

        // 发送完整内容到前端
        onProgress?.({
          content: [
            {
              type: 'text',
              text: planContent,
            },
          ],
        });
      }

      logger.log(`研究计划完成，准备解析和结构化`);

      // 解析和结构化研究计划
      const parsedStructuredPlan = await extractStructuredPlan(chatModel, planContent);
      structuredPlan = JSON.stringify(parsedStructuredPlan);
      logger.log(`研究计划已结构化，包含 ${parsedStructuredPlan.steps?.length || 0} 个步骤`);

      // 通知生成结构化计划
      onProgress?.({
        nodeType: ResearchNodeType.PLANNER,
        stepName: '构建结构化计划',
        progress: 0.75,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      researchPlan = planContent;

      // 完成通知 - 添加过渡到下一阶段
      onProgress?.({
        nodeType: ResearchNodeType.PLANNER,
        stepName: '完成研究计划',
        progress: 0.8,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
        plan: researchPlan,
        structuredPlan: structuredPlan,
      });

      // 将研究计划添加到消息历史
      return {
        messages: [new HumanMessage(researchPlan)],
        checkpoints: {
          research_plan: researchPlan,
          structured_plan: structuredPlan,
          plan_steps: parsedStructuredPlan.steps?.map(s => s.title).join(', ') || '',
          current_step_index: 0,
        },
      };
    } catch (error) {
      logger.error(`研究计划生成失败: ${error.message}`);

      // 如果生成失败，尝试使用普通调用
      // 重建提示词
      const plannerPrompt = `
你是一位专业的研究规划者，负责制定详细的研究计划。

# 职责
你的主要职责是根据提供的背景信息，制定一个结构化、全面的研究计划。

# 规划要求
- 制定3-5个明确的研究步骤
- 确保步骤逻辑连贯，循序渐进
- 每个步骤应包含明确的目标和范围
- 考虑不同角度和视角
- 针对用户问题设计专门的研究方向

# 输出格式
请提供一个详细的研究计划，包括:
1. **研究目标**: 总体研究目标
2. **研究步骤**: 列出3-5个主要研究步骤，每步包含:
   - 步骤标题
   - 详细描述
   - 期望的具体成果
3. **优先级**: 指出哪些步骤最重要
4. **研究方法**: 推荐的研究方法和资源

以下是背景信息:
${backgroundInvestigation}

请根据以上背景信息，制定一个结构化的研究计划。
`;
      const response = await chatModel.invoke([new SystemMessage(plannerPrompt), ...messages]);

      researchPlan = response.content as string;

      try {
        // 尝试解析结构化计划
        const fallbackStructuredPlan = await extractStructuredPlan(chatModel, researchPlan);
        structuredPlan = JSON.stringify(fallbackStructuredPlan);

        // 发送完整内容到前端
        onProgress?.({
          content: [
            {
              type: 'text',
              text: researchPlan,
            },
          ],
        });

        // 将研究计划添加到消息历史
        return {
          messages: [new HumanMessage(researchPlan)],
          checkpoints: {
            research_plan: researchPlan,
            structured_plan: structuredPlan,
            plan_steps: fallbackStructuredPlan.steps?.map(s => s.title).join(', ') || '',
            current_step_index: 0,
          },
        };
      } catch (parseError) {
        // 如果解析失败，使用默认结构
        logger.error(`结构化计划解析失败: ${parseError.message}`);
        const defaultPlan = { steps: [{ title: '综合研究', description: '基于背景信息进行研究' }] };
        structuredPlan = JSON.stringify(defaultPlan);

        return {
          messages: [new HumanMessage(researchPlan)],
          checkpoints: {
            research_plan: researchPlan,
            structured_plan: structuredPlan,
            plan_steps: '综合研究',
            current_step_index: 0,
          },
        };
      }
    }
  };
}

/**
 * 从计划文本中提取结构化计划
 */
async function extractStructuredPlan(chatModel: ChatOpenAI, planText: string): Promise<any> {
  try {
    // 使用自定义提示从计划文本中提取结构化信息
    const extractionPrompt = `请从以下研究计划文本中提取结构化信息，返回JSON格式:\n\n${planText}\n\n提取格式示例:\n{
      "title": "研究计划标题",
      "objective": "主要研究目标",
      "steps": [
        {
          "title": "步骤1标题",
          "description": "步骤1详细描述",
          "expected_outcome": "预期成果"
        }
      ],
      "challenges": ["可能的挑战1", "可能的挑战2"]
    }`;

    const extraction = await chatModel.invoke([
      new SystemMessage(
        '你是一个专业的JSON结构提取器，从文本中提取结构化信息并只返回有效的JSON，不要有任何其他内容。',
      ),
      new HumanMessage(extractionPrompt),
    ]);

    return JSON.parse(extraction.content as string);
  } catch (error) {
    console.error(`计划结构化失败: ${error.message}`, error.stack);
    // 返回基本结构
    return {
      title: '研究计划',
      objective: planText.substring(0, 100) + '...',
      steps: [{ title: '综合研究', description: planText }],
      challenges: [],
    };
  }
}
