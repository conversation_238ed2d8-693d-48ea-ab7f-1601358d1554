import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { Logger } from '@nestjs/common';
import { BackgroundInvestigatorNodeType, ResearchNodeType, ResearchStateType } from '../types';

/**
 * 背景调查者的提示词模板
 * 负责处理工具结果和提供背景信息
 */
const BACKGROUND_INVESTIGATOR_PROMPT = `
你是一位专业的研究背景调查员，善于从各种信息源中整合和分析数据，提供全面的背景报告。

# 主要职责
1. 分析和整合提供的所有工具结果和搜索数据
2. 理解不同工具结果之间的关系和差异
3. 提供深入、全面的问题背景分析
4. 明确引用信息来源，区分事实和推断

# 输出格式
你的背景报告应包含以下内容：

## 1. 问题概述
- 简明扼要地陈述所研究的问题
- 说明其相关性和重要性

## 2. 核心数据点与发现
- 列出从工具结果中提取的关键信息
- 按相关性和重要性组织信息
- 明确标注信息的来源

## 3. 背景分析
- 提供对主题的深入背景分析
- 解释相关概念、历史背景或技术细节
- 基于工具结果进行分析和解释

## 4. 不同观点与争议
- 呈现不同的视角和观点（如果存在）
- 客观呈现可能存在的争议或讨论

## 5. 数据限制与缺口
- 指出信息中可能存在的不确定性或缺口
- 说明工具结果的局限性（如果有）

## 6. 参考资料
- 列出所有使用的参考资料
- 包括完整的链接和来源信息

# 引用格式要求
请在分析和总结时，使用恰当的引用格式，在句子末尾标注引用的链接，使用[[序号](链接地址)]格式。例如：研究发现这种方法效果显著[[3](http://example.com)]。如果需要引用多个来源，可以连续使用如[[2](链接地址)][[5](链接地址)]的格式。

在报告末尾，添加"参考资料"部分，列出所有引用的来源，使用编号列表格式：
1. [来源标题] - [链接]
2. [来源标题] - [链接]

# 注意事项
- 保持分析的客观性，避免加入未经工具支持的个人偏见
- 仅在无法从工具结果中得到信息时，才使用你自己的知识
- 尽可能使用确切的数字、日期和事实，引用来源
- 当信息来源有冲突时，指出差异并提供多个视角
- 清晰区分确定的事实和推测的内容
- 确保引用格式正确，便于读者追踪信息来源

请基于提供的所有工具结果和搜索信息，按照以上格式生成一份详尽的背景报告。如果工具结果有限或不足，可以补充基于你已有知识的相关背景信息，但应明确区分这些补充内容。
`;

/**
 * 创建背景调查节点
 * @param logger 日志记录器
 * @param chatModel 聊天模型
 * @returns 背景调查节点函数
 */
export function createBackgroundInvestigatorNode(
  logger: Logger,
  chatModel: ChatOpenAI,
): BackgroundInvestigatorNodeType {
  return async (state: ResearchStateType) => {
    logger.log('执行背景调查节点 - 利用MCP工具搜索结果整合信息');

    // 通知开始调查
    const onProgress = state.onProgress;
    onProgress?.({
      nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
      stepName: '初始化背景调查',
      progress: 0.4,
    });

    // 通知开始整合结果
    onProgress?.({
      nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
      stepName: '整合数据与背景调查',
      progress: 0.42,
      content: [
        {
          type: 'text',
          text: '\n',
        },
      ],
    });

    const { messages } = state;
    const content = messages[messages.length - 1].content;

    // 更安全地处理消息内容类型，避免类型错误
    let question = '';
    if (typeof content === 'string') {
      question = content;
    } else if (Array.isArray(content)) {
      // 尝试从数组中提取文本内容
      question = content
        .map(item => {
          if (typeof item === 'string') return item;
          // 安全地处理可能的复杂对象
          if (typeof item === 'object' && item !== null) {
            return 'text' in item ? String(item.text) : JSON.stringify(item);
          }
          return '';
        })
        .join(' ');
    } else if (content !== null && typeof content === 'object') {
      // 处理单个对象情况
      question = JSON.stringify(content);
    }

    logger.log(
      `处理后的问题文本: ${question.substring(0, 100)}${question.length > 100 ? '...' : ''}`,
    );

    // 从状态中获取工具结果
    const toolResults = state.toolResults || [];

    // 格式化工具结果
    let toolResultText = '';

    if (toolResults.length > 0) {
      onProgress?.({
        nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
        stepName: '分析工具结果',
        progress: 0.45,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      for (const toolResult of toolResults) {
        try {
          // 尝试格式化工具结果
          const resultData =
            typeof toolResult.result === 'string'
              ? JSON.parse(toolResult.result)
              : toolResult.result;

          if (
            toolResult.toolName.toLowerCase().includes('search') ||
            toolResult.clientName.toLowerCase().includes('search')
          ) {
            // 搜索工具结果格式化
            if (resultData && Array.isArray(resultData.results)) {
              const formattedResults = resultData.results
                .map((item, index) => {
                  return `[结果 ${index + 1}]\n标题: ${item.title || '无标题'}\n链接: ${
                    item.url || item.link || '#'
                  }\n摘要: ${item.content || item.snippet || '无内容'}\n`;
                })
                .join('\n');

              toolResultText += `\n### 搜索工具 (${toolResult.toolName}) 结果:\n${formattedResults}\n`;
            } else {
              toolResultText += `\n### 搜索工具 (${toolResult.toolName}) 结果:\n${JSON.stringify(
                resultData,
                null,
                2,
              )}\n`;
            }
          } else {
            // 其他工具结果
            toolResultText += `\n### ${toolResult.toolName} 工具结果:\n${JSON.stringify(
              resultData,
              null,
              2,
            )}\n`;
          }
        } catch (error) {
          logger.error(`解析工具结果失败: ${error.message}`);
          toolResultText += `\n### ${toolResult.toolName} 工具结果:\n${JSON.stringify(
            toolResult.result,
            null,
            2,
          )}\n`;
        }
      }
    } else {
      logger.log('没有工具结果可用，将基于模型知识生成背景报告');
      onProgress?.({
        nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
        stepName: '使用已有知识进行分析',
        progress: 0.45,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });
    }

    // 使用LLM生成背景报告
    try {
      // 准备提示词，包含工具结果
      const backgroundPrompt = `${BACKGROUND_INVESTIGATOR_PROMPT}\n\n问题: ${question}\n\n${
        toolResults.length > 0 ? toolResultText : '请基于你的知识提供详细回答。'
      }`;

      onProgress?.({
        nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
        stepName: '生成背景报告',
        progress: 0.5,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      let backgroundReport = '';

      // 使用流式调用生成报告
      try {
        // 使用流式调用
        const stream = await chatModel.stream([
          new SystemMessage(backgroundPrompt),
          new HumanMessage(
            `请基于上述信息，为问题"${question}"提供全面的背景报告。尽可能详细，包含相关事实、数据和背景。`,
          ),
        ]);

        // 处理流式输出
        for await (const chunk of stream) {
          if (chunk.content) {
            backgroundReport += chunk.content;

            // 实时发送内容到前端，不包含状态文本
            onProgress?.({
              content: [
                {
                  type: 'text',
                  text: chunk.content,
                },
              ],
              nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
              stepName: '生成背景报告',
              progress: 0.5,
            });
          }
        }

        logger.log(`背景报告流式生成完成，内容长度: ${backgroundReport.length}字`);
      } catch (error) {
        logger.error(`背景报告流式处理失败: ${error.message}，尝试使用普通调用`);

        // 如果流式处理失败，尝试使用普通调用
        const backgroundResponse = await chatModel.invoke([
          new SystemMessage(backgroundPrompt),
          new HumanMessage(
            `请基于上述信息，为问题"${question}"提供全面的背景报告。尽可能详细，包含相关事实、数据和背景。`,
          ),
        ]);

        backgroundReport = String(backgroundResponse.content);

        // 发送完整内容到前端
        onProgress?.({
          content: [
            {
              type: 'text',
              text: backgroundReport,
            },
          ],
          nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
          stepName: '完成背景报告',
          progress: 0.6,
          backgroundReport,
        });
      }

      // 通知进度 - 添加更清晰的过渡状态
      onProgress?.({
        nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
        stepName: '完成背景报告',
        backgroundReport,
        progress: 0.6,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 将背景报告添加到状态的检查点中，以便后续节点使用
      return {
        messages: [new HumanMessage(backgroundReport)],
        checkpoints: {
          ...state.checkpoints,
          background_investigation: backgroundReport,
        },
      };
    } catch (error) {
      logger.error(`生成背景报告失败: ${error.message}`);

      // 通知前端错误
      onProgress?.({
        nodeType: ResearchNodeType.BACKGROUND_INVESTIGATOR,
        stepName: '背景报告生成错误',
        error: true,
        errorMessage: error.message,
        progress: 0.6,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 返回一个基本报告
      return {
        messages: [
          new HumanMessage(`背景调查无法完成，但我将尝试基于现有信息继续分析: ${error.message}`),
        ],
      };
    }
  };
}
