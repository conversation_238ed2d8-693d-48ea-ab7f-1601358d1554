import { SystemMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { Logger } from '@nestjs/common';
import { CoordinatorNodeType, ResearchNodeType, ResearchStateType } from '../types';

/**
 * 协调者的提示词模板
 * 负责分析问题并确定是否需要深入研究
 */
const COORDINATOR_PROMPT = `
你是研究助手的协调者。请分析用户的问题，确定是否需要深入研究。

# 职责

你的主要职责是：
- 分析用户提出的问题
- 确定问题的复杂度和研究需求
- 提供初步见解和方向
- 说明为什么需要更深入的调查

# 问题分类

1. **简单事实型问题**:
   - 基础知识或常识性问题
   - 单一事实或数据点查询
   - 简单定义或解释

2. **需要深入研究的问题**:
   - 复杂主题需要多角度分析
   - 需要收集和整合多个信息源
   - 涉及比较、趋势分析或预测
   - 需要专业或领域特定知识

# 执行规则

- 分析用户的问题，确定其性质和复杂度
- 提供对问题的初步理解和分析
- 说明为什么这个问题需要深入研究
- 指出研究可能需要关注的主要方面
- 保持客观、专业的语气

# 注意事项

- 不要尝试直接回答复杂问题，而是说明需要进一步研究
- 简明扼要地表达初步分析
- 保持与用户使用相同的语言
- 避免使用过于技术性的术语
`;

/**
 * 创建协调者节点
 * @param logger 日志记录器
 * @param chatModel 聊天模型
 * @returns 协调者节点函数
 */
export function createCoordinatorNode(logger: Logger, chatModel: ChatOpenAI): CoordinatorNodeType {
  return async (state: ResearchStateType) => {
    logger.log('执行协调者节点 - 问题分析与方向确定');

    // 通知进度
    const onProgress = state.onProgress;
    onProgress?.({
      nodeType: ResearchNodeType.COORDINATOR,
      stepName: '初始化问题分析',
      progress: 0.1,
      content: [
        {
          type: 'text',
          text: '\n',
        },
      ],
    });

    const { messages } = state;

    // 获取上下文信息
    const systemMessage = new SystemMessage(COORDINATOR_PROMPT);

    // 向模型发送协调者提示和用户问题
    const response = await chatModel.invoke([systemMessage, ...messages]);

    // 分析问题复杂度和类型
    const problemAnalysis = await chatModel.invoke([
      new SystemMessage(
        '分析以下问题的复杂度、类型和所需背景知识:\n1. 问题是否需要最新信息\n2. 问题涉及哪些领域\n3. 问题解决难度评估(简单/中等/复杂)',
      ),
      new SystemMessage(messages[messages.length - 1].content as string),
    ]);

    logger.log('问题分析完成，确定研究方向');

    // 提取研究领域
    const researchFields = extractResearchFields(problemAnalysis.content as string);

    // 通知进度
    onProgress?.({
      nodeType: ResearchNodeType.COORDINATOR,
      stepName: '完成问题分析',
      progress: 1.0,
      analysis: response.content,
      fields: researchFields,
    });

    // 将协调者的分析添加到消息历史
    return {
      messages: [response],
      checkpoints: {
        coordinator_analysis: response.content,
        problem_metadata: problemAnalysis.content,
        research_fields: researchFields,
      },
    };
  };
}

/**
 * 从问题分析中提取研究领域
 */
function extractResearchFields(analysis: string): string[] {
  // 简单实现，实际项目中可以使用更复杂的解析逻辑
  const fieldsMatch = analysis.match(/领域[：:]\s*(.*?)(?:\n|$)/);
  if (fieldsMatch && fieldsMatch[1]) {
    return fieldsMatch[1].split(/[,，、]/);
  }
  return [];
}
