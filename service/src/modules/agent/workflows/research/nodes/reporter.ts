import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { Logger } from '@nestjs/common';
import { ReporterNodeType, ResearchNodeType, ResearchStateType } from '../types';

/**
 * Reporter的提示词模板
 * 负责生成最终研究报告
 */
const REPORTER_PROMPT = `
你是一位专业的研究报告撰写者。基于提供的研究结果，创建一份全面、结构清晰的最终报告。

# 角色

你应当作为客观和分析性的报告撰写者：
- 准确公正地呈现事实
- 逻辑组织信息
- 突出关键发现和洞见
- 使用清晰简洁的语言
- 严格依据提供的信息
- 不编造或假设信息
- 明确区分事实和分析

# 报告结构

请按以下格式构建你的报告：

1. **标题**
   - 为报告提供一个简洁的标题

2. **要点摘要**
   - 最重要发现的项目符号列表（4-6点）
   - 每点应简明扼要（1-2句话）
   - 专注于最重要和可操作的信息

3. **概述**
   - 主题的简短介绍（1-2段）
   - 提供背景和重要性

4. **详细分析**
   - 将信息组织成逻辑部分，有清晰的标题
   - 根据需要包含相关子部分
   - 以结构化、易于理解的方式呈现信息
   - 突出意外或特别值得注意的细节

5. **结论和建议**
   - 基于研究得出的主要结论
   - 可能的行动建议和下一步

6. **参考资料**
   - 列出所有使用的参考资料
   - 包括完整的链接和来源信息

# 写作指南

1. 写作风格:
   - 使用专业语气
   - 简明扼要
   - 避免推测
   - 用证据支持观点
   - 明确说明信息来源
   - 指出数据是否不完整或不可用
   - 不要发明或推断数据

2. 格式:
   - 包含各部分的标题
   - 使用表格呈现比较数据
   - 为重要点添加强调
   - 使用水平线(---)分隔主要部分

# 数据完整性

- 只使用输入中明确提供的信息
- 当数据缺失时，说明"未提供信息"
- 不要创建虚构的例子或场景
- 如果数据似乎不完整，承认其局限性
- 不要对缺失信息做假设

# 表格指南

- 使用表格呈现比较数据、统计信息、功能或选项
- 始终包含一个清晰的标题行，有列名
- 保持表格简洁，专注于关键信息

# 注意事项

- 如果对任何信息不确定，请承认不确定性
- 只包括来自提供的源材料的可验证事实
- 如果某项信息不明确，请说明其局限性
- 保持报告的客观性和事实性
- 使用简洁专业的语言
- 确保准确引用所有来源，并在报告末尾列出参考资料
`;

/**
 * 创建报告员节点
 * @param logger 日志记录器
 * @param chatModel 聊天模型
 * @returns 报告员节点函数
 */
export function createReporterNode(logger: Logger, chatModel: ChatOpenAI): ReporterNodeType {
  return async (state: ResearchStateType) => {
    logger.log('执行报告员节点 - 生成最终报告');

    // 通知初始化状态，无需显式状态文本
    const onProgress = state.onProgress;
    onProgress?.({
      nodeType: ResearchNodeType.REPORTER,
      stepName: '初始化报告生成',
      progress: 0.9,
    });

    const { messages, checkpoints } = state;

    // 收集所有研究成果
    const coordinatorAnalysis = checkpoints.coordinator_analysis || '';
    const backgroundInvestigation = checkpoints.background_investigation || '';
    const researchPlan = checkpoints.research_plan || '';
    const researchFindings = checkpoints.research_findings || '';

    // 获取步骤结果
    let stepResults = [];
    try {
      stepResults = JSON.parse(checkpoints.step_results || '[]');
      logger.log(`成功解析了 ${stepResults.length} 个研究步骤结果`);
    } catch (error) {
      logger.error(`解析步骤结果失败: ${error.message}`);
    }

    // 获取原始搜索结果用于引用
    let references = '无外部参考资料';
    try {
      const rawSearchResults = checkpoints.raw_search_results || '[]';
      const parsedResults = JSON.parse(rawSearchResults);

      if (parsedResults && parsedResults.length > 0) {
        references = parsedResults
          .map((result, idx) => `[${idx + 1}] ${result.title}. ${result.link}`)
          .join('\n');
        logger.log(`为报告准备了 ${parsedResults.length} 条参考资料`);
      }
    } catch (error) {
      logger.error(`准备参考资料失败: ${error.message}`);
    }

    // 构建最终报告提示词
    const reportPrompt = `${REPORTER_PROMPT}

研究问题:
${messages[0].content}

问题分析:
${coordinatorAnalysis}

背景调查:
${backgroundInvestigation}

研究计划:
${researchPlan}

研究发现:
${researchFindings}

外部参考资料:
${references}

请根据以上所有信息，生成一份详尽的研究报告。报告应该:
1. 有清晰的结构和分节
2. 包含摘要和关键发现
3. 详述各研究步骤的结果
4. 提供明确的结论和建议
5. 引用相关的外部资料
6. 在报告末尾列出所有参考资料

引用格式要求:
请在适当位置引用外部资料，使用[[序号](链接地址)]格式。例如：我们发现第二篇文章提到了这一点[[2](https://example.com)]。如果需要引用多个来源，可以连续使用如[[2](链接地址)][[5](链接地址)]的格式。

在报告末尾，添加"参考资料"部分，列出所有引用的来源，使用编号列表格式：
1. [来源标题] - [链接]
2. [来源标题] - [链接]

确保报告易于理解，重点突出，并包含所有关键信息。`;

    logger.log('向模型发送报告生成请求 - 流式处理');

    let finalReport = '';

    try {
      // 使用流式调用
      const stream = await chatModel.stream([new SystemMessage(reportPrompt), ...messages]);

      // 处理流式响应
      onProgress?.({
        nodeType: ResearchNodeType.REPORTER,
        stepName: '撰写研究报告',
        progress: 0.95,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      for await (const chunk of stream) {
        if (chunk.content) {
          finalReport += chunk.content;

          // 实时发送内容到前端
          onProgress?.({
            content: [
              {
                type: 'text',
                text: chunk.content,
              },
            ],
          });
        }
      }

      // 流式处理完成后添加额外空行
      onProgress?.({
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      logger.log(`最终报告流式生成完成，内容长度: ${finalReport.length}字`);
    } catch (error) {
      logger.error(`最终报告流式处理失败: ${error.message}`);

      // 如果流式处理失败，尝试使用普通调用
      const reportResponse = await chatModel.invoke([new SystemMessage(reportPrompt), ...messages]);

      finalReport = reportResponse.content as string;

      // 发送完整内容到前端
      onProgress?.({
        content: [
          {
            type: 'text',
            text: finalReport,
          },
        ],
      });
    }

    logger.log(`最终报告生成完成，长度: ${finalReport.length}字`);

    // 将报告发送到前端
    onProgress?.({
      nodeType: ResearchNodeType.REPORTER,
      stepName: '完成研究报告',
      report: finalReport,
      progress: 1.0,
      content: [
        {
          type: 'text',
          text: '\n',
        },
      ],
    });

    // 返回最终报告
    return {
      messages: [new HumanMessage(finalReport)],
      checkpoints: {
        ...checkpoints,
        final_report: finalReport,
        references: references,
      },
    };
  };
}
