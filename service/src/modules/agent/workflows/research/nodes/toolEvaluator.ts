import { handleError } from '@/common/utils';
import { correctApiBaseUrl } from '@/common/utils/correctApiBaseUrl';
import { MCPService } from '@/modules/mcp/mcp.service';
import { HumanMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { Logger } from '@nestjs/common';
import OpenAI from 'openai';
import { ResearchNodeType, ResearchStateType, ToolEvaluatorNodeType } from '../types';

/**
 * 工具评估者的提示词模板
 * 负责判断是否需要调用外部工具
 */
const TOOL_EVALUATOR_PROMPT = `
你是一个强大的AI助手，拥有调用各种工具的能力。请判断用户可能需要的工具，而不是回答问题。

# 工具使用指南

1. 当用户询问需要实时信息、搜索内容或特定功能时，优先考虑使用相关工具
2. 分析用户问题，确定最合适的工具，并正确提供所有必需参数
3. 对于搜索类工具，提供具体、明确的查询关键词
4. 如果多个工具可以解决问题，请使用多个工具
5. 工具调用失败时，尝试调整参数后重试

# 重要提示

主动使用工具能够提供更准确、更新、更有价值的回答。不要仅依赖你已有的知识，特别是当用户需要最新信息或特定数据时。
`;

/**
 * 创建工具评估节点
 * @param logger 日志记录器
 * @param chatModel 聊天模型
 * @param mcpService MCP服务
 * @param globalConfigService 全局配置服务
 * @returns 工具评估节点函数
 */
export function createToolEvaluatorNode(
  logger: Logger,
  chatModel: ChatOpenAI,
  mcpService: MCPService,
  globalConfigService: any,
): ToolEvaluatorNodeType {
  return async (state: ResearchStateType) => {
    logger.log('执行工具评估节点 - 判断是否需要调用工具');

    // 初始化工具调用计数
    if (!state.toolCallCount) {
      state.toolCallCount = 0;
    }

    // 通知进度
    const onProgress = state.onProgress;
    onProgress?.({
      nodeType: ResearchNodeType.TOOL_EVALUATOR,
      stepName: '初始化工具评估',
      progress: 0.1,
      content: [
        {
          type: 'text',
          text: '\n',
        },
      ],
    });

    // 获取当前的消息历史和状态
    const { messages } = state;
    const lastMessage = messages[messages.length - 1];
    const content =
      typeof lastMessage.content === 'string'
        ? lastMessage.content
        : JSON.stringify(lastMessage.content);

    // 检查是否已达到最大工具调用次数
    const maxToolCalls = 3;
    if (state.toolCallCount >= maxToolCalls) {
      logger.log(`已达到最大工具调用次数(${maxToolCalls})，跳过工具评估`);
      onProgress?.({
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
        stepName: '达到最大工具调用次数',
        progress: 0.2,
      });
      // 重置工具调用计数并设置shouldExit标志，告诉路由函数应该退出工具评估循环
      return { toolCallCount: 0, shouldExitToolEvaluation: true };
    }

    try {
      // 初始化MCP服务
      await mcpService.initialize();
      const mcpClients = mcpService.getClients();

      if (Object.keys(mcpClients).length === 0) {
        logger.warn('没有可用的MCP客户端，跳过工具评估');
        return { shouldExitToolEvaluation: true };
      }

      // 收集所有可用工具
      const tools = [];
      const clientToolsMap = {};

      for (const [clientName, client] of Object.entries(mcpClients)) {
        if (client.tools && client.tools.length > 0) {
          logger.debug(`客户端 ${clientName} 有 ${client.tools.length} 个工具`);

          for (const tool of client.tools) {
            const toolDefinition = {
              type: 'function',
              function: {
                name: tool.name,
                description: tool.description || `通过 ${clientName} 客户端调用 ${tool.name} 工具`,
                parameters: tool.inputSchema || { type: 'object', properties: {}, required: [] },
              },
            };
            tools.push(toolDefinition);
            clientToolsMap[tool.name] = {
              clientName,
              toolName: tool.name,
            };
          }
        }
      }

      if (tools.length === 0) {
        logger.warn('没有可用的工具，跳过工具评估');
        return { shouldExitToolEvaluation: true };
      }

      const {
        openaiBaseUrl = '',
        openaiBaseKey = '',
        openaiBaseModel,
        toolCallUrl,
        toolCallKey,
        toolCallModel,
      } = await globalConfigService.getConfigs([
        'openaiBaseKey',
        'openaiBaseUrl',
        'openaiBaseModel',
        'toolCallUrl',
        'toolCallKey',
        'toolCallModel',
      ]);

      console.log(globalConfigService);

      // 如果没有获取到配置，尝试从ChatModel获取模型名称
      const modelName = toolCallModel || openaiBaseModel;

      // 创建OpenAI客户端
      const openai = new OpenAI({
        apiKey: toolCallKey || openaiBaseKey,
        baseURL: await correctApiBaseUrl(toolCallUrl || openaiBaseUrl),
        timeout: 60000,
      });

      // 准备消息历史
      const openaiMessages = [];

      // 添加系统提示词
      openaiMessages.push({
        role: 'system',
        content: TOOL_EVALUATOR_PROMPT,
      });

      // 添加之前的消息历史
      for (const message of messages) {
        if (message._getType() === 'human') {
          openaiMessages.push({
            role: 'user',
            content: message.content as string,
          });
        } else if (message._getType() === 'ai') {
          openaiMessages.push({
            role: 'assistant',
            content: message.content as string,
          });
        }
      }

      // 如果有之前的工具结果，添加到用户消息中
      if (state.toolResults && state.toolResults.length > 0) {
        let toolResultsText = '已获取的工具结果：\n';
        for (const toolResult of state.toolResults) {
          toolResultsText += `\n【工具${toolResult.toolName}】\n${JSON.stringify(
            toolResult.result,
            null,
            2,
          )}\n`;
        }

        // 添加一条新的用户消息，包含原始问题和工具结果
        openaiMessages.push({
          role: 'user',
          content: `${content}\n\n${toolResultsText}\n\n请判断是否还有其他适合调用的工具来帮助解决问题？如果有，请直接调用，无需解释。`,
        });
      } else {
        // 如果没有之前的工具结果，直接使用原始问题
        openaiMessages.push({
          role: 'user',
          content: `${content}\n\n请判断是否需要使用工具来解决这个问题？如果需要，请直接使用合适的工具，无需解释。`,
        });
      }

      // 调用OpenAI函数调用API
      onProgress?.({
        nodeType: ResearchNodeType.TOOL_EVALUATOR,
        stepName: '分析问题需求',
        progress: 0.15,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      const functionCallResponse = await openai.chat.completions.create({
        model: modelName,
        messages: openaiMessages,
        tools: tools,
        tool_choice: 'auto',
      });

      const toolCalls = functionCallResponse.choices[0]?.message?.tool_calls;

      // 如果没有工具调用，说明不需要使用工具
      if (!toolCalls || toolCalls.length === 0) {
        logger.log('工具评估结果：不需要使用工具');

        onProgress?.({
          nodeType: ResearchNodeType.TOOL_EVALUATOR,
          stepName: '无需使用工具',
          progress: 0.2,
          content: [
            {
              type: 'text',
              text: '\n',
            },
          ],
        });

        return { shouldExitToolEvaluation: true };
      }

      logger.log(`工具评估结果：需要使用 ${toolCalls.length} 个工具`);

      // 显示即将调用的工具
      onProgress?.({
        nodeType: ResearchNodeType.TOOL_EVALUATOR,
        stepName: '准备调用工具',
        progress: 0.25,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 执行工具调用
      const toolResults = [];
      for (const toolCall of toolCalls) {
        try {
          const functionName = toolCall.function.name;
          const functionArgs = JSON.parse(toolCall.function.arguments);
          const clientTool = clientToolsMap[functionName];

          if (!clientTool) {
            logger.error(`工具 ${functionName} 未找到对应的客户端和工具信息`);
            continue;
          }

          const { clientName, toolName } = clientTool;
          logger.log(`调用工具 ${clientName}/${toolName} 参数:`, functionArgs);

          // 显示正在调用工具
          onProgress?.({
            nodeType: ResearchNodeType.TOOL_EVALUATOR,
            stepName: `调用工具 ${toolName}`,
            progress: 0.3,
            content: [
              {
                type: 'text',
                text: '\n',
              },
            ],
          });

          // 执行工具调用
          const toolResult = await mcpService.callTool(clientName, toolName, functionArgs);

          // 记录工具结果
          toolResults.push({
            clientName,
            toolName,
            result: toolResult,
          });

          // 计算结果中的数据条数
          let dataCount = 0;
          try {
            const resultData = typeof toolResult === 'string' ? JSON.parse(toolResult) : toolResult;

            // 尝试计算搜索结果数量
            if (resultData && Array.isArray(resultData.results)) {
              dataCount = resultData.results.length;
            } else if (resultData && Array.isArray(resultData)) {
              // 处理数组类型结果
              dataCount = resultData.length;
            } else if (resultData && typeof resultData === 'object') {
              // 对于对象类型，计算顶级键数量
              dataCount = Object.keys(resultData).length;
            }
          } catch (error) {
            // 无法解析，使用默认计数 1
            dataCount = 1;
          }

          // 显示简洁的工具调用结果
          onProgress?.({
            nodeType: ResearchNodeType.TOOL_EVALUATOR,
            stepName: `工具 ${toolName} 调用成功: 获取到 ${dataCount} 条数据`,
            progress: 0.3,
            content: [
              {
                type: 'text',
                text: '\n',
              },
            ],
          });
        } catch (error) {
          const errorMessage = handleError(error);
          logger.error(`工具调用失败: ${errorMessage}`);

          // 显示工具调用错误
          onProgress?.({
            nodeType: ResearchNodeType.TOOL_EVALUATOR,
            stepName: `工具调用失败`,
            progress: 0.3,
            content: [
              {
                type: 'text',
                text: '\n',
              },
            ],
          });
        }
      }

      // 记录工具调用次数增加
      const updatedToolCallCount = (state.toolCallCount || 0) + 1;

      // 通知工具调用完成
      onProgress?.({
        nodeType: ResearchNodeType.TOOL_EVALUATOR,
        stepName: '工具调用完成',
        progress: 0.4,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 将工具结果添加到现有结果中
      const existingResults = state.toolResults || [];
      const allToolResults = [...existingResults, ...toolResults];

      // 返回更新后的状态，添加工具结果
      return {
        messages: [
          new HumanMessage(
            `我需要更多关于"${content}"的信息，已经使用工具查询了相关内容，请基于查询结果提供帮助。`,
          ),
        ],
        toolResults: allToolResults,
        toolCallCount: updatedToolCallCount,
      };
    } catch (error) {
      const errorMessage = handleError(error);
      logger.error(`工具评估过程出错: ${errorMessage}`);

      // 显示错误信息
      onProgress?.({
        nodeType: ResearchNodeType.TOOL_EVALUATOR,
        stepName: '工具评估错误',
        progress: 0.4,
        error: true,
        errorMessage: errorMessage,
        content: [
          {
            type: 'text',
            text: '\n',
          },
        ],
      });

      // 出错时依然增加工具调用计数，避免无限循环
      const updatedToolCallCount = (state.toolCallCount || 0) + 1;
      return { toolCallCount: updatedToolCallCount };
    }
  };
}
