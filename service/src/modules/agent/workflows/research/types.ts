import { Annotation, MessagesAnnotation } from '@langchain/langgraph';
import { NodeFunction } from '../types';

/**
 * 研究工作流状态定义
 */
export const ResearchState = Annotation.Root({
  // 导入 MessagesAnnotation 的 messages 状态
  ...MessagesAnnotation.spec,
  // 添加额外的研究相关状态
  debug: Annotation<boolean>(),
  maxPlanIterations: Annotation<number>(),
  maxStepNum: Annotation<number>(),
  enableBackgroundInvestigation: Annotation<boolean>(),
  onProgress: Annotation<(data: any) => void>(),
  checkpoints: Annotation<Record<string, any>>({
    reducer: (curr, update) => ({ ...curr, ...update }),
    default: () => ({}),
  }),
  toolResults: Annotation<
    Array<{
      toolName: string;
      clientName: string;
      args: any;
      result: any;
      timestamp: number;
    }>
  >({
    reducer: (curr = [], update = []) => [...curr, ...update],
    default: () => [],
  }),
  toolCallCount: Annotation<number>({
    reducer: (curr = 0, update = 0) => curr + update,
    default: () => 0,
  }),
  shouldExitToolEvaluation: Annotation<boolean>({
    reducer: (curr = false, update = false) => update,
    default: () => false,
  }),
});

/**
 * 研究工作流的状态类型
 */
export type ResearchStateType = typeof ResearchState.State;

/**
 * 协调者节点类型
 */
export type CoordinatorNodeType = NodeFunction<ResearchStateType>;

/**
 * 背景调查节点类型
 */
export type BackgroundInvestigatorNodeType = NodeFunction<ResearchStateType>;

/**
 * 规划者节点类型
 */
export type PlannerNodeType = NodeFunction<ResearchStateType>;

/**
 * 研究员节点类型
 */
export type ResearcherNodeType = NodeFunction<ResearchStateType>;

/**
 * 报告员节点类型
 */
export type ReporterNodeType = NodeFunction<ResearchStateType>;

/**
 * 路由函数类型
 */
export type RouterFunction = (state: ResearchStateType) => string;

/**
 * 研究工作流执行选项
 */
export interface ResearchWorkflowOptions {
  enableBackgroundInvestigation?: boolean;
  debug?: boolean;
  maxPlanIterations?: number;
  maxStepNum?: number;
  apiKey?: string;
  proxyUrl?: string;
  temperature?: number;
  abortController?: AbortController;
  onProgress?: (data: any) => void;
  workflowStateId?: string; // 工作流状态ID，用于恢复之前的工作流状态
}

/**
 * 研究流程状态节点标识
 */
export enum ResearchNodeType {
  COORDINATOR = 'coordinator',
  BACKGROUND_INVESTIGATOR = 'background_investigator',
  TOOL_EVALUATOR = 'tool_evaluator',
  PLANNER = 'planner',
  RESEARCHER = 'researcher',
  REPORTER = 'reporter',
}

export type ToolEvaluatorNodeType = (
  state: ResearchStateType,
) => Promise<Partial<ResearchStateType>>;
