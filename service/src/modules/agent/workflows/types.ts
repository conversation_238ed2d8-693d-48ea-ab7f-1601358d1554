import { MCPService } from '@/modules/mcp/mcp.service';
import { ChatOpenAI } from '@langchain/openai';
import { TavilySearch } from '@langchain/tavily';
import { Observable } from 'rxjs';

/**
 * 工作流状态标签枚举
 * 定义了工作流节点中通用的状态类型
 */
export enum WorkflowStatus {
  DEFAULT = 'default', // 默认状态，不显示特定进度标签
  INITIALIZING = 'initializing', // 节点开始初始化阶段
  SEARCHING = 'searching', // 搜索或收集信息阶段
  PROCESSING = 'processing', // 处理数据/分析信息阶段
  GENERATING = 'generating', // 生成内容阶段
  WAITING = 'waiting', // 等待外部资源或输入
  COMPLETE = 'complete', // 任务完成
}

/**
 * 工作流配置接口
 */
export interface WorkflowConfig {
  name: string;
  description: string;
  version: string;
}

/**
 * 工作流节点类型
 */
export type NodeFunction<TState> = (state: TState) => Promise<Partial<TState>>;

/**
 * 工作流进度回调
 */
export type ProgressCallback = (data: {
  nodeType?: string;
  status?: string;
  content?: Array<{ type: string; text: string }>;
  [key: string]: any;
}) => void;

/**
 * 错误回调
 */
export type FailureCallback = (error: any) => void;

/**
 * 工作流执行选项
 */
export interface WorkflowOptions {
  chatId?: string;
  apiKey?: string;
  model?: string;
  modelName?: string;
  temperature?: number;
  timeout?: number;
  proxyUrl?: string;
  abortController?: AbortController;
  onProgress?: ProgressCallback;
  onFailure?: FailureCallback;
  debug?: boolean;
  [key: string]: any;
}

/**
 * 工作流结果接口
 */
export interface WorkflowResult {
  chatId: string;
  content: Array<any>;
  full_content: string;
  finishReason: string | null;
  model: string;
  modelName: string;
  errMsg: string;
  [key: string]: any;
}

/**
 * 工作流接口
 */
export interface Workflow {
  /**
   * 工作流配置
   */
  config: WorkflowConfig;

  /**
   * 执行工作流
   * @param input 输入参数
   * @param options 执行选项
   */
  execute(input: any, options: WorkflowOptions): Promise<WorkflowResult>;

  /**
   * 从保存的状态继续执行工作流
   * @param savedState 保存的工作流状态
   * @param options 执行选项
   */
  continueFromState(savedState: any, options: WorkflowOptions): Promise<WorkflowResult>;

  /**
   * 作为Observable执行工作流
   * @param input 输入参数
   * @param options 执行选项
   */
  executeAsObservable(input: any, options?: WorkflowOptions): Promise<Observable<string>>;

  /**
   * 初始化工作流
   * @param chatModel 聊天模型
   * @param mcpService MCP服务（可选）
   * @param globalConfigService 全局配置服务（可选）
   * @param tools 可用工具（可选）
   */
  initialize(
    chatModel: ChatOpenAI,
    mcpService?: MCPService,
    globalConfigService?: any,
    tools?: any[],
  ): Promise<void>;
}

/**
 * 工具包类型
 */
export interface ToolKit {
  chatModel: ChatOpenAI;
  tavilySearchTool?: TavilySearch;
  mcpService?: MCPService;
  [key: string]: any;
}
