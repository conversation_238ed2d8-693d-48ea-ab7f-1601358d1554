import { TypeOrmQueryFailedFilter } from '@/common/filters/typeOrmQueryFailed.filter';
import { TransformInterceptor } from '@/common/interceptors/transform.interceptor';
import { CustomLoggerService } from '@/common/logger/custom-logger.service';
import { FastXmlMiddleware } from '@/common/middleware/fast-xml-middleware';
import { Logger, RequestMethod, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as compression from 'compression';
import { createVerify, randomBytes } from 'crypto';
import * as Dotenv from 'dotenv';
import * as fs from 'fs';
import Redis from 'ioredis';
import * as path from 'path';
import 'reflect-metadata';
import { AppModule } from './app.module';
import { AllExceptionsFilter } from './common/filters/allExceptions.filter';
Dotenv.config({ path: '.env' });

/**
 * 系统授权状态
 */
export const LICENSE_STATUS = {
  valid: false,
  message: '',
  machineId: '',
  licenseKey: '',
  checkedAt: null as Date,
  publicKeyPath: '', // 添加公钥路径记录
  expiresAt: null as Date | null, // 添加过期时间字段
  isExpired: false, // 是否已过期
  daysRemaining: null as number | null, // 剩余天数
};

/**
 * 查找文件的多种可能路径
 * @param filename 文件名
 * @returns 找到的文件路径或null
 */
function findFilePath(filename: string): string | null {
  const possiblePaths = [
    path.join(process.cwd(), filename), // 当前工作目录
    path.join(__dirname, '..', filename), // 应用根目录
    path.join(__dirname, filename), // 与主程序同级
    path.resolve(filename), // 绝对路径解析
    path.join(process.cwd(), '..', filename), // 上级目录
    path.join(process.cwd(), 'dist', filename), // dist目录
  ];

  for (const filePath of possiblePaths) {
    if (fs.existsSync(filePath)) {
      return filePath;
    }
  }

  return null;
}

/**
 * 验证授权许可
 */
function validateLicense(): boolean {
  try {
    Logger.log('=== 开始系统授权验证 ===', 'License');
    LICENSE_STATUS.checkedAt = new Date();

    // 检查环境变量是否包含LICENSE_KEY
    const licenseKey = process.env.LICENSE_KEY;
    LICENSE_STATUS.licenseKey = licenseKey ? '已配置' : '未配置';

    if (!licenseKey) {
      const errorMsg = '未配置LICENSE_KEY环境变量，授权验证失败';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    // 获取机器码
    const machineId = getMachineId();
    LICENSE_STATUS.machineId = machineId || '获取失败';

    if (!machineId) {
      const errorMsg = '无法获取机器标识，授权验证失败';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    Logger.log(`当前机器码: ${machineId}`, 'License');

    // 查找公钥文件的多种可能路径
    const publicKeyPath = LICENSE_STATUS.publicKeyPath || findFilePath('root-pub.pem');

    if (!publicKeyPath) {
      const errorMsg = '未找到公钥文件root-pub.pem，尝试了多个位置';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    Logger.log(`公钥文件存在: ${publicKeyPath}`, 'License');
    LICENSE_STATUS.publicKeyPath = publicKeyPath;

    // 读取公钥
    const publicKey = fs.readFileSync(publicKeyPath);

    // 解析授权码（格式: Base64编码的JSON.签名）
    const parts = licenseKey.split('.');
    if (parts.length !== 2) {
      const errorMsg = '授权码格式无效，应为"数据.签名"格式';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    const [dataBase64, signature] = parts;
    if (!dataBase64 || !signature) {
      const errorMsg = '授权码格式无效，数据或签名部分为空';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    // 解码授权数据
    let licenseData;
    try {
      const licenseDataString = Buffer.from(dataBase64, 'base64').toString('utf8');
      licenseData = JSON.parse(licenseDataString);
    } catch (error) {
      const errorMsg = '授权码数据无效或损坏，无法解析';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    // 检查授权数据中的机器码
    if (licenseData.machineId !== machineId) {
      const errorMsg = '授权码与当前机器不匹配';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    // 检查过期时间
    if (licenseData.expiryDate) {
      const expiryDate = new Date(licenseData.expiryDate);
      LICENSE_STATUS.expiresAt = expiryDate;

      if (isNaN(expiryDate.getTime())) {
        const errorMsg = '授权码中的过期时间无效';
        Logger.error(errorMsg, 'License');
        LICENSE_STATUS.message = errorMsg;
        return false;
      }

      const now = new Date();
      if (now > expiryDate) {
        LICENSE_STATUS.isExpired = true;
        const errorMsg = `授权已过期，过期时间: ${expiryDate.toISOString().split('T')[0]}`;
        Logger.error(errorMsg, 'License');
        LICENSE_STATUS.message = errorMsg;
        return false;
      }

      // 计算剩余天数
      const daysRemaining = Math.ceil(
        (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
      );
      LICENSE_STATUS.daysRemaining = daysRemaining;

      Logger.log(
        `授权有效期至: ${expiryDate.toISOString().split('T')[0]}，剩余 ${daysRemaining} 天`,
        'License',
      );
    } else {
      Logger.log('授权码未包含过期时间，使用永久有效授权', 'License');
    }

    // 验证签名
    const verify = createVerify('RSA-SHA256');
    verify.update(dataBase64);
    verify.end();

    const isSignatureValid = verify.verify(
      { key: publicKey, padding: process.versions.node >= '12.0.0' ? 1 : 1 },
      signature,
      'base64',
    );

    if (!isSignatureValid) {
      const errorMsg = '授权码签名验证失败，可能已被篡改';
      Logger.error(errorMsg, 'License');
      LICENSE_STATUS.message = errorMsg;
      return false;
    }

    LICENSE_STATUS.valid = true;
    LICENSE_STATUS.message = '授权验证成功';
    Logger.log('授权验证成功 ✅', 'License');
    Logger.log('=== 授权验证完成 ===', 'License');
    return true;
  } catch (error) {
    const errorMsg = `授权验证过程出错: ${error.message}`;
    Logger.error(errorMsg, 'License');
    LICENSE_STATUS.message = errorMsg;
    return false;
  }
}

/**
 * 获取机器唯一标识
 */
function getMachineId(): string {
  try {
    // 首先尝试从环境变量获取
    const envMachineId = process.env.MACHINE_ID;
    if (envMachineId) {
      Logger.log('从环境变量获取到机器码', 'License');
      return envMachineId;
    }

    // 如果环境变量中没有，尝试使用node-machine-id库获取
    Logger.log('尝试使用node-machine-id获取机器码', 'License');
    const { machineIdSync } = require('node-machine-id');
    return machineIdSync({ original: true });
  } catch (error) {
    Logger.error(`获取机器ID时出错: ${error.message}`, 'License');
    return '';
  }
}

/**
 * 检查系统文件完整性
 */
function checkSystemIntegrity(): boolean {
  try {
    Logger.log('=== 开始系统完整性检查 ===', 'System');

    // 检查公钥文件
    const publicKeyPath = findFilePath('root-pub.pem');

    if (!publicKeyPath) {
      Logger.error('系统完整性检查失败: 未找到root-pub.pem文件', 'System');
      return false;
    }

    Logger.log(`找到公钥文件: ${publicKeyPath}`, 'System');
    LICENSE_STATUS.publicKeyPath = publicKeyPath;

    // 检查其他关键文件
    const requiredFiles = ['.env', 'package.json'];

    let allFilesExist = true;
    for (const filename of requiredFiles) {
      const filePath = findFilePath(filename);
      if (!filePath) {
        Logger.error(`系统完整性检查失败: 缺少关键文件 ${filename}`, 'System');
        allFilesExist = false;
      } else {
        Logger.log(`文件检查通过: ${filename} -> ${filePath}`, 'System');
      }
    }

    if (!allFilesExist) {
      Logger.error('系统完整性检查未通过: 部分文件缺失', 'System');
      return false;
    }

    Logger.log('系统完整性检查通过 ✅', 'System');
    Logger.log('=== 系统完整性检查完成 ===', 'System');
    return true;
  } catch (error) {
    Logger.error(`系统完整性检查出错: ${error.message}`, 'System');
    return false;
  }
}

async function bootstrap() {
  console.log('\n======================================');
  console.log('        99AI 服务启动中...            ');
  console.log('======================================\n');

  // 先检查系统完整性
  const systemIntegrityValid = checkSystemIntegrity();
  if (!systemIntegrityValid) {
    Logger.error('系统完整性检查未通过，程序无法启动', 'System');
    process.exit(1); // 直接退出进程
  }

  // 启动前验证授权
  const licenseValid = validateLicense();
  if (!licenseValid) {
    Logger.error('授权验证失败，程序无法启动', 'License');
    console.log('\n======================================');
    console.log('  授权状态: 未授权 ❌');
    console.log('  原因: ' + LICENSE_STATUS.message);
    console.log('  请确保LICENSE_KEY配置正确且有效');
    console.log('======================================\n');
    process.exit(1); // 直接退出进程
  } else {
    Logger.log('授权验证通过，程序将以完整功能运行', 'License');
  }

  const redis = new Redis({
    host: process.env.REDIS_HOST,
    port: Number(process.env.REDIS_PORT),
    password: process.env.REDIS_PASSWORD,
    db: Number(process.env.REDIS_DB || 0),
  });

  // 尝试获取现有的 JWT_SECRET
  const existingSecret = await redis.get('JWT_SECRET');

  if (!existingSecret) {
    // 如果不存在，生成新的 JWT_SECRET
    const jwtSecret = randomBytes(256).toString('base64');
    Logger.log('Generating and setting new JWT_SECRET');
    await redis.set('JWT_SECRET', jwtSecret);
  }

  // 导入初始化数据库函数
  const { initDatabase } = require('./modules/database/initDatabase');

  const app = await NestFactory.create(AppModule, {
    bufferLogs: true,
    logger: ['log', 'error', 'warn', 'debug', 'verbose'],
  });

  // 在应用配置后，但在监听端口前初始化数据库表结构
  try {
    Logger.log('正在预初始化数据库结构...', 'Bootstrap');
    await initDatabase();
    Logger.log('数据库结构预初始化完成', 'Bootstrap');
  } catch (dbError) {
    Logger.error(`数据库预初始化失败: ${dbError.message}`, 'Bootstrap');
    // 即使失败也继续启动应用
  }

  // 根据环境变量设置全局 Logger
  app.useLogger(app.get(CustomLoggerService));

  // 使用我们的自定义XML中间件替代express-xml-bodyparser
  const xmlMiddleware = new FastXmlMiddleware();
  app.use(xmlMiddleware.use.bind(xmlMiddleware));

  app.use(
    // Re-enable compression
    compression({
      filter: (req, res) => {
        // 对流式响应路由禁用压缩
        if (req.path.includes('/api/chatgpt/chat-process')) {
          return false;
        }
        return compression.filter(req, res);
      },
    }),
  );

  // 启用并配置 CORS
  app.enableCors({
    origin: '*', // 或者配置允许的具体域名
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 204,
  });

  // app.enableCors();
  app.setGlobalPrefix('/api', {
    exclude: [{ path: '*', method: RequestMethod.GET }], // 排除GET请求的通配符路由
  });
  app.useGlobalInterceptors(new TransformInterceptor()); // Re-enable TransformInterceptor
  app.useGlobalFilters(new TypeOrmQueryFailedFilter());
  app.useGlobalFilters(new AllExceptionsFilter()); // Re-enable AllExceptionsFilter
  app.useGlobalPipes(new ValidationPipe());
  app.getHttpAdapter().getInstance().set('views', 'templates/pages');
  app.getHttpAdapter().getInstance().set('view engine', 'hbs');

  // 只在测试环境下启用Swagger
  if (process.env.ISDEV === 'true') {
    const config = new DocumentBuilder()
      .setTitle('99AI API')
      .setDescription('99AI服务API文档')
      .setVersion('1.0')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);

    // 添加全局响应定义
    const responseSchema = {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        data: { type: 'object' },
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: '请求成功' },
      },
    };

    // 为每个路由添加标准响应格式
    Object.values(document.paths).forEach(path => {
      Object.values(path).forEach(method => {
        method.responses = {
          ...method.responses,
          '200': {
            description: '成功响应',
            content: {
              'application/json': {
                schema: responseSchema,
              },
            },
          },
        };
      });
    });

    SwaggerModule.setup('api-docs', app, document);
    Logger.log(
      'Swagger API文档已启用: http://localhost:' + (process.env.PORT || 3000) + '/api-docs',
      'Main',
    );
  }

  const PORT = process.env.PORT || 3000;

  const server = await app.listen(PORT, () => {
    console.log('\n======================================');
    console.log(`  服务启动成功: http://localhost:${PORT}`);
    console.log(`  授权状态: ${LICENSE_STATUS.valid ? '已授权 ✅' : '未授权 ❌'}`);

    // 显示过期信息
    if (LICENSE_STATUS.expiresAt) {
      const expiryDate = LICENSE_STATUS.expiresAt;
      console.log(`  授权有效期: ${expiryDate.toISOString().split('T')[0]}`);
      console.log(`  剩余天数: ${LICENSE_STATUS.daysRemaining} 天`);
    } else {
      console.log(`  授权有效期: 永久有效`);
    }

    console.log('======================================\n');
  });

  server.timeout = 5 * 60 * 1000;
}

bootstrap();
