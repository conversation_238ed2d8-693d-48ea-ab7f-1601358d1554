#!/usr/bin/env bash
set -e # Exit immediately if a command exits with a non-zero status.

export LANG=zh_CN.UTF-8

# Set up color variables for output
red='\033[0;31m'
green='\033[0;32m'
yellow='\033[0;33m'
plain='\033[0m'

# Default values
NVM_URL="https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh"
default_node_version="18.16.0"
# default_chatgpt_port=9520 # Port configuration will be handled in .env
CONFIG_FILE=docker-compose.yml
ENV_FILE=.env # Use a single .env file
SERVICE_DIR=$(pwd) # Assuming script is run from the service root directory
PM2_APP_NAME="99AI" # Define PM2 App Name as variable
DOCKER_CONTAINER_APP_DIR="/app" # Define Docker App Dir as variable
DOCKER_INSTANCE_ID_DIR="/data" # Directory for instance ID persistence

# --- 前置提醒 ---
echo -e "${yellow}==================== 重要提醒 ====================${plain}"
echo -e "${yellow}- 请确保您已拥有执行此脚本的权限。如果遇到权限问题，请尝试: chmod +x deploy.sh${plain}"
echo -e "${yellow}- Node.js 部署 (选项 1, 2): 需要您已预先安装并运行 MySQL 和 Redis 服务。${plain}"
echo -e "${yellow}- Docker 部署 (选项 3, 4): 可以通过 docker-compose.yml 配置自动创建 MySQL 和 Redis 容器。${plain}"
echo -e "${yellow}- 升级操作前 (选项 2, 4): 强烈建议备份您的数据库和重要数据！${plain}"
echo -e "${yellow}- 脚本中的 PM2 应用名默认为 '$PM2_APP_NAME'，Docker 内应用目录为 '$DOCKER_CONTAINER_APP_DIR'。${plain}"
echo -e "${yellow}  如果您的配置不同，可能需要手动修改脚本中的相关命令。${plain}"
echo -e "${yellow}- Docker 部署/更新需要将持久卷挂载到容器的 '$DOCKER_INSTANCE_ID_DIR' 目录以保证授权稳定。${plain}"
echo -e "${yellow}==================================================${plain}"
read -p "按 Enter 键继续，或按 Ctrl+C 退出..."

# --- 授权说明 ---
# Node.js 部署: 依赖物理机硬件ID，相对稳定。首次启动如果缺少 LICENSE_KEY，会提示机器码。
# Docker 部署:
#   - 依赖在持久卷 /data 中生成的 instance.id。首次部署容器如果缺少或 LICENSE_KEY 无效，
#     **请查看容器日志** 获取实例 ID (instance_id)。
#   - 将获取到的 机器码 或 实例ID 提供给开发者获取 LICENSE_KEY。
#   - **重要:** Docker 部署必须使用 -v 或 --mount 将持久卷挂载到容器的 /data 目录。
#   - 注意: 直接更新运行中 Docker 容器内部文件 (docker cp / docker exec git pull)
#     通常不会改变其环境标识符，但这并非标准的更新方式，且不能防止因容器重建导致的 ID 变化。
# -----------------

# Welcome message
echo -e "${green}欢迎使用99AI一键部署和升级脚本${plain}"
echo -e "${green}----------------------------------------${plain}"
echo -e "${green}注意：${plain}"
echo -e "${green}1. Node.js 部署方式需要提前安装好 MySQL 和 Redis。${plain}"
echo -e "${green}2. Docker 部署方式可以选择新建 MySQL 和 Redis 容器。${plain}"
echo -e "${green}3. 从旧版本升级 Docker 时，请确保 data 和 SQL 文件已备份到根目录。${plain}"
echo -e "${green}----------------------------------------${plain}"

check_cpu_arch() {
  case "$(uname -m)" in
    aarch64) arch=linux_arm64 ;;
    i686) arch=linux_386 ;;
    arm) arch=linux_arm ;;
    x86_64) arch=linux_amd64 ;;
  esac
}

check_os() {
  if [[ "$(uname)" == "Darwin" ]]; then
    os_name="macOS"
    InstallMethod="brew"
  else
    if command -v lsb_release >/dev/null; then
      DISTRO=$(lsb_release -i -s)
    else
      DISTRO=$(grep -oP '^ID=\K.*' /etc/*-release)
    fi
    case "$DISTRO" in
      Debian|Ubuntu) os_name="${DISTRO}-based Linux"; InstallMethod="sudo apt-get" ;;
      centos)
        if [[ "$(grep -oP '^VERSION_ID="\K[0-9]+' /etc/*-release)" == "7" ]]; then
          os_name="CentOS 7"; InstallMethod="yum"
        else
          os_name="CentOS 8"; InstallMethod="dnf"
        fi ;;
      fedora) os_name="Fedora"; InstallMethod="dnf" ;;
      opensuse-leap) os_name="openSUSE Leap"; InstallMethod="sudo zypper" ;;
      *) echo "未知操作系统，脚本不支持"; exit 1 ;;
    esac
  fi
}

install_if_missing() {
  if ! command -v "$1" >/dev/null; then
    echo -e "${red}$1 未安装，正在安装 $1...${plain}"
    ${InstallMethod} install -y "$1"
    echo -e "${green}$1 已安装${plain}"
  else
    echo -e "${green}$1 已安装${plain}"
  fi
}

install_nvm_and_node() {
  if ! command -v node >/dev/null; then
    echo -e "${red}node 未安装${plain}"
    echo -e "${red}开始安装NVM${plain}"
    curl -o- $NVM_URL | bash || wget -qO- $NVM_URL | bash
    source ~/.bashrc
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

    echo -e "${green}列出node可用版本${plain}"
    nvm ls-remote
    read -p "请输入要安装的 Node.js 版本号 ，不知道安装啥的请回车[默认版本：$default_node_version]：" node_version
    node_version="${node_version:-$default_node_version}"
    echo -e "${red}正在安装 node-v$node_version${plain}"
    nvm install $node_version
    nvm use $node_version
    source ~/.bashrc

    if ! command -v node >/dev/null; then
      echo -e "${red}Node.js 安装失败，请检查错误信息！${plain}"
      exit 1
    fi
    installed_version=$(node -v)
    if [[ "$installed_version" != "v$node_version" ]]; then
      echo -e "${red}Node.js 安装失败，当前安装版本为 $installed_version, 期望安装版本为 v$node_version ${plain}"
      exit 1
    fi
    echo -e "${green}Node.js 安装完成!${plain}"
  else
    echo -e "${green}node 已安装${plain}"
  fi
}

install_add_docker() {
  if [ -f "/etc/alpine-release" ]; then
    echo -e "${red}docker 未安装，正在安装 docker...${plain}"
    apk update
    apk add docker docker-compose
    echo -e "${green}docker 和 docker-compose 已安装${plain}"
    rc-update add docker default
    service docker start
  else
    echo -e "${red}docker 未安装，正在安装 docker...${plain}"
    curl -fsSL https://get.docker.com | sh
    echo -e "${green}docker 已安装${plain}"
    ln -s /usr/libexec/docker/cli-plugins/docker-compose /usr/local/bin
    echo -e "${green}docker-compose 已安装${plain}"
    systemctl start docker
    systemctl enable docker
  fi
  echo -e "${green}docker 和 docker-compose 已安装${plain}"
  sleep 2
}

docker_install() {
  if ! command -v docker &>/dev/null; then
    install_add_docker
  else
    echo "Docker 已经安装"
  fi
}

check_dependencies() {
  check_cpu_arch
  check_os

  ${InstallMethod} update -y >/dev/null 2>&1

  install_if_missing git
  install_if_missing cat
  install_if_missing curl

  if [[ "$operation_choice" == "1" || "$operation_choice" == "2" ]]; then
    install_nvm_and_node
    install_if_missing npm

    if ! command -v pm2 >/dev/null; then
      echo -e "${red}pm2 未安装，正在安装 pm2...${plain}"
      npm install -g pm2
      echo -e "${green}pm2 已安装${plain}"
    else
      echo -e "${green}pm2 已安装${plain}"
    fi
  elif [[ "$operation_choice" == "3" || "$operation_choice" == "4" ]]; then
    docker_install
  fi
}

# Node.js 升级任务 (假设用户已获取包含 dist 的新代码包或使用 git)
node_deploy_update() {
  echo -e "${green}开始更新 Node.js 部署...${plain}"

  detect_package_manager

  # 备份提醒
  echo -e "${yellow}提醒：执行升级操作前，请确保已备份数据库和重要文件。${plain}"
  read -p "按 Enter 键继续，或按 Ctrl+C 取消..."

  prompt_for_license_key

  # 检查是否存在 .git 目录，如果存在则询问是否 git pull
  if [ -d ".git" ]; then
    read -p "是否需要从 Git 拉取最新代码？(如果代码已手动更新，请选 n) (y/n) [默认 n]: " pull_choice
    pull_choice=${pull_choice:-n}
    if [[ "$pull_choice" == "y" ]]; then
      echo "正在从 Git 拉取最新代码..."
      git pull
      if [ $? -ne 0 ]; then
          echo -e "${red}Git 拉取失败，请检查您的网络连接和 Git 配置。${plain}"
          exit 1
      fi
      echo -e "${green}Git 拉取完成。${plain}"
    else
      echo "跳过 Git 拉取。"
    fi
  else
      echo "未找到 .git 目录，跳过 Git 拉取。(假设代码已通过其他方式更新)"
  fi

  echo "正在安装/更新依赖包 ($package_manager install --frozen-lockfile)..."
  echo "(确保依赖与提供的 package.json/pnpm-lock.yaml 一致)"
  $package_manager install --frozen-lockfile
  if [ $? -ne 0 ]; then
      echo -e "${red}依赖安装/更新失败，请检查错误日志。${plain}"
      exit 1
  fi

  echo "正在执行构建 ($package_manager build)..." # 构建还是需要的，因为依赖可能更新
  $package_manager build
   if [ $? -ne 0 ]; then
      echo -e "${red}构建失败，请检查错误日志。${plain}"
      exit 1
  fi

  echo "正在重启应用 (pm2 restart $PM2_APP_NAME)..."
  if pm2 list | grep -q "$PM2_APP_NAME"; then
      pm2 restart "$PM2_APP_NAME"
      pm2 save
      echo -e "${green}应用已通过 PM2 重启。${plain}"
  else
      echo -e "${red}未找到名为 '$PM2_APP_NAME' 的 PM2 应用。请尝试手动启动或检查 PM2 配置。${plain}"
  fi

  echo -e "${green}Node.js 部署更新完成！${plain}"
}

# Node.js 全新部署任务 (假设用户已获取包含 dist 的代码包)
node_deploy_new() {
  echo -e "${green}开始全新 Node.js 部署 (使用已有代码/构建产物)...${plain}"

  detect_package_manager # 检测包管理器

  echo -e "${green}请确保已安装 MySQL 和 Redis，并准备好连接信息。${plain}"

  create_env_file # 创建 .env
  prompt_for_license_key # 检查/提示 License Key
  select_npm_mirror # 选择 npm 源

  echo "正在安装依赖包 ($package_manager install)..."
  $package_manager install --frozen-lockfile # 使用 lockfile 确保依赖一致性
   if [ $? -ne 0 ]; then
      echo -e "${red}依赖安装失败，请检查错误日志。${plain}"
      exit 1
  fi

  echo "正在首次启动应用 ($package_manager start)..."
  # 首次启动使用 start，它会通过 pm2.conf.json 启动并命名应用
  # 它会使用已有的 dist 目录
  $package_manager start
  if [ $? -ne 0 ]; then
      echo -e "${red}首次启动失败，请检查错误日志和 .env 配置。${plain}"
      echo -e "${red}如果提示授权失败，请查看日志获取机器码，并联系开发者获取 LICENSE_KEY 后填入 .env 文件，然后尝试手动运行 '$package_manager start'。${plain}"
      exit 1
  fi
  pm2 save # 保存 PM2 进程列表

  echo -e "${green}Node.js 全新部署完成！${plain}"
  echo -e "${green}如果首次启动因缺少 LICENSE_KEY 而失败，请检查程序日志获取机器码，并将其提供给开发者获取授权码填入 .env 文件，然后再次运行本脚本选择"Node.js 升级"或手动重启应用。${plain}"
}

# --- Helper Functions for Node.js --- #

detect_package_manager() {
  if command -v pnpm >/dev/null; then
    package_manager="pnpm"
    echo -e "${green}检测到 pnpm，将使用 pnpm。${plain}"
  elif command -v yarn >/dev/null; then
    package_manager="yarn"
    echo -e "${green}未检测到 pnpm，但检测到 yarn，将使用 yarn。${plain}"
  elif command -v npm >/dev/null; then
    package_manager="npm"
    echo -e "${green}未检测到 pnpm 或 yarn，将使用 npm。${plain}"
  else
    echo -e "${red}错误：未检测到 pnpm, yarn 或 npm。请先安装其中一个。${plain}"
    exit 1
  fi
}

create_env_file() {
  if [ -f "$ENV_FILE" ]; then
    read -p ".env 文件已存在，是否覆盖？(y/n) [默认 n]: " overwrite_env
    if [[ "$overwrite_env" != "y" ]]; then
      echo "保留现有 .env 文件。"
      return
    fi
  fi

  echo -e "${green}正在进行运行配置...${plain}"
  read -p "设置 PORT（程序访问端口）为 [回车默认: 9520]: " input_port
  PORT=${input_port:-9520}

  read -p "设置 DB_HOST（数据库地址）为 [回车默认: 127.0.0.1]: " input_db_host
  DB_HOST=${input_db_host:-127.0.0.1}

  read -p "设置 DB_PORT（数据库端口）为 [回车默认: 3306]: " input_db_port
  DB_PORT=${input_db_port:-3306}

  read -p "设置 DB_USER（数据库用户名）为 [回车默认: root]: " input_db_user
  DB_USER=${input_db_user:-root}

  read -p "设置 DB_PASS（数据库密码）为 [回车默认: 空]: " input_db_pass
  DB_PASS=${input_db_pass:-}

  read -p "设置 DB_DATABASE（数据库名）为: " input_db_database
  DB_DATABASE=${input_db_database:-}
  while [ -z "$DB_DATABASE" ]; do
      read -p "数据库名不能为空，请重新输入: " input_db_database
      DB_DATABASE=${input_db_database:-}
  done

  read -p "设置 REDIS_PORT（Redis端口）为 [回车默认: 6379]: " input_redis_port
  REDIS_PORT=${input_redis_port:-6379}

  read -p "设置 REDIS_HOST（Redis地址）为 [回车默认: 127.0.0.1]: " input_redis_host
  REDIS_HOST=${input_redis_host:-127.0.0.1}

  read -p "设置 REDIS_PASSWORD（Redis密码）为 [回车默认: 空]: " input_redis_password
  REDIS_PASSWORD=${input_redis_password:-}

  read -p "设置 REDIS_DB（Redis数据库）为 [回车默认: 0]: " input_redis_db
  REDIS_DB=${input_redis_db:-0}

  # 注意：LICENSE_KEY 在 prompt_for_license_key 函数中单独处理

  cat >"$ENV_FILE" <<EOF
# server base
PORT=$PORT
PREFIX=/docs
APIPREFIX=/api

# MySQL
DB_HOST=$DB_HOST
DB_PORT=$DB_PORT
DB_USER=$DB_USER
DB_PASS=$DB_PASS
DB_DATABASE=$DB_DATABASE
DB_SYNC=true

# Redis
REDIS_PORT=$REDIS_PORT
REDIS_HOST=$REDIS_HOST
REDIS_PASSWORD=$REDIS_PASSWORD
REDIS_DB=$REDIS_DB

# 是否测试环境 (部署脚本默认设置为 false)
ISDEV=FALSE

NAMESPACE=AIWEB

# 授权码 (需要用户手动填写或通过脚本提示输入)
LICENSE_KEY=
EOF
  echo ".env 文件创建/覆盖完成。"
}

prompt_for_license_key() {
  if [ -f "$ENV_FILE" ]; then
    # 检查 .env 文件中是否存在 LICENSE_KEY 并且不为空
    if grep -q "^LICENSE_KEY=" "$ENV_FILE" && [ -n "$(grep '^LICENSE_KEY=' "$ENV_FILE" | cut -d '=' -f2-)" ]; then
      echo -e "${green}.env 文件中已存在有效的 LICENSE_KEY。${plain}"
      return
    fi
  fi

  echo -e "${red}授权码 (LICENSE_KEY) 未配置或为空。${plain}"
  echo "请联系开发者，提供您的 机器码 (Node.js 部署) 或 实例ID (Docker 部署，查看容器日志获取) 以获取授权码。"
  read -p "请输入获取到的 LICENSE_KEY: " input_license_key
  while [ -z "$input_license_key" ]; do
      read -p "LICENSE_KEY 不能为空，请重新输入: " input_license_key
  done

  # 更新 .env 文件中的 LICENSE_KEY
  if [ -f "$ENV_FILE" ]; then
      # 如果存在 LICENSE_KEY 行，则替换它
      if grep -q "^LICENSE_KEY=" "$ENV_FILE"; then
          sed -i.bak "s/^LICENSE_KEY=.*/LICENSE_KEY=${input_license_key}/" "$ENV_FILE"
      else
          # 如果不存在，则追加到文件末尾
          echo "LICENSE_KEY=${input_license_key}" >> "$ENV_FILE"
      fi
  else
      echo -e "${red}错误：.env 文件不存在，无法写入 LICENSE_KEY。请先进行全新安装。${plain}"
      exit 1
  fi
  echo -e "${green}LICENSE_KEY 已更新到 .env 文件。${plain}"
}

select_npm_mirror() {
  declare -a NPM_MIRRORS=(
    "淘宝镜像" "https://registry.npmmirror.com"
    "阿里云镜像" "https://npm.aliyun.com"
    "腾讯云镜像" "https://mirrors.cloud.tencent.com/npm/"
    "华为云镜像" "https://mirrors.huaweicloud.com/repository/npm/"
    "网易镜像" "https://mirrors.163.com/npm/"
    "中科院大学开源镜像站" "http://mirrors.ustc.edu.cn/"
    "清华大学开源镜像站" "https://mirrors.tuna.tsinghua.edu.cn/"
  )

  echo -e "${green}请选择要使用的 npm 源：${plain}"

  for ((i=0; i<${#NPM_MIRRORS[@]}; i+=2)); do
    echo "$((i / 2 + 1))) ${NPM_MIRRORS[i]}"
  done

  echo "8) 不使用国内源"
  echo "9) 退出"

  read -p "请输入数字（1-9）[默认 1]: " selection
  selection=${selection:-1}

  case $selection in
  1 | 2 | 3 | 4 | 5 | 6 | 7)
    echo "设置 npm 源为：${NPM_MIRRORS[$(($selection * 2 - 1))]} ..."
    $package_manager config set registry ${NPM_MIRRORS[$(($selection * 2 - 1))]} || echo -e "${red}设置 npm 源失败，请检查 $package_manager 是否能正常工作。${plain}"
    echo "设置完成."
    ;;
  8)
    echo "您选择了不使用国内源."
    $package_manager config delete registry || echo -e "${red}删除 npm 源配置失败，可能之前未设置。${plain}"
    ;;
  9)
    echo "退出设置."
    exit
    ;;
  *)
    echo "无效选择，使用默认淘宝源."
    $package_manager config set registry https://registry.npmmirror.com
    ;;
  esac
}
# --- End Helper Functions --- #

# Docker-compose 启动任务
start_compose() {
  echo "配置阶段完成，启动docker-compose up -d。"
  docker-compose up -d
  echo "已启动docker-compose并使用默认配置启动了以下服务："
  docker-compose ps
}

# Docker-compose 配置任务
config_compose() {
  echo "开始 Docker-compose 部署配置……"
  # source .env.docker # 不再使用 .env.docker，统一使用 .env

  # 检查 .env 文件是否存在，如果不存在则创建基础的
  if [ ! -f "$ENV_FILE" ]; then
      echo ".env 文件不存在，正在创建基础 .env 文件..."
      # 可以只创建包含必要 Docker 相关变量的，或者调用 create_env_file 创建完整的
      create_env_file # 创建完整的 .env，包含数据库等信息，即使 Docker 内部可能不用
  fi

  # 提示输入 LICENSE_KEY
  prompt_for_license_key

  read -p "是否需要修改 Docker 映射端口 [默认 9520]？ (y/n，输入q退出) [默认 n]: " change_port
  change_port=${change_port:-n}

  if [[ $change_port == "q" ]]; then
    echo "退出脚本"
    exit
  fi

  if [[ $change_port == "y" ]]; then
    current_port=$(grep -oP '"[0-9]+:9520"' $CONFIG_FILE | grep -oP '[0-9]+(?=:)' || echo "9520")
    read -p "当前映射端口为 $current_port，请输入新的端口: " NEW_CHATGPT_PORT
    while check_port $NEW_CHATGPT_PORT; do # 复用 check_port 函数
      read -p "端口 $NEW_CHATGPT_PORT 被占用，请重新输入端口: " NEW_CHATGPT_PORT
    done
    echo -e "${green}端口 $NEW_CHATGPT_PORT 可用。正在更新 $CONFIG_FILE ...${plain}"
    sed -i.bak "s/\"[0-9]*:9520\"/\"$NEW_CHATGPT_PORT:9520\"/" $CONFIG_FILE
    echo "$CONFIG_FILE 已更新。"
  fi

  start_compose # 调用启动函数

  echo -e "${green}Docker-compose 部署启动完成！${plain}"
  echo -e "${red}重要提示：${plain}"
  echo -e "  - 请确保已在 docker-compose.yml 或相关配置中将持久卷正确挂载到容器的 /data 目录。"
  echo -e "  - 如果应用启动后提示授权失败，请使用以下命令查看容器日志以获取实例 ID:"
  echo -e "    docker logs <容器名称或ID>"
  echo -e "  - 将获取到的实例 ID 提供给开发者以获取正确的 LICENSE_KEY，并更新到项目根目录的 .env 文件中。"
  echo -e "  - 更新 .env 文件后，需要重新运行此脚本选择"Docker 原地更新"将最新的 .env 文件复制到容器内并重启应用，或者手动执行以下命令："
  echo -e "    docker cp .env <容器名称或ID>:/app/.env"
  echo -e "    docker exec <容器名称或ID> pm2 restart 99AI" # 假设 pm2 应用名为 99AI
}

# Docker-compose 原地更新任务 (新 - 假设用户已获取包含 dist 的新代码包)
docker_update_inplace() {
    echo -e "${green}开始 Docker 原地更新 (使用已有代码/构建产物)...${plain}"
    echo -e "${yellow}此操作将直接修改正在运行的容器，请谨慎使用。${plain}"
    echo -e "${yellow}强烈建议在执行前备份容器的持久卷 ('$DOCKER_INSTANCE_ID_DIR') 和数据库！${plain}"
    echo -e "${red}确保容器已使用持久卷挂载到 '$DOCKER_INSTANCE_ID_DIR' 目录以保证授权稳定。${plain}"
    read -p "按 Enter 键继续，或按 Ctrl+C 取消..."

    # 获取容器名称或ID
    running_containers=$(docker ps --format "{{.Names}} ({{.ID}})")
    if [ -z "$running_containers" ]; then
        echo -e "${red}错误：未找到正在运行的 Docker 容器。请先部署。${plain}"
        exit 1
    fi
    echo "当前运行的容器:"
    echo "$running_containers"
    read -p "请输入要更新的容器名称或 ID: " container_name_or_id
    while [ -z "$container_name_or_id" ]; do
        read -p "容器名称或 ID 不能为空，请重新输入: " container_name_or_id
    done
    if ! docker ps -q --filter "name=${container_name_or_id}" --filter "status=running" | grep -q .; then
      if ! docker ps -q --filter "id=${container_name_or_id}" --filter "status=running" | grep -q .; then
        echo -e "${red}错误：未找到正在运行的容器 '$container_name_or_id'。${plain}"
        exit 1
      fi
    fi
    echo -e "${green}找到运行中的容器: $container_name_or_id ${plain}"

    prompt_for_license_key

    echo "正在复制更新文件到容器..."
    container_app_dir="$DOCKER_CONTAINER_APP_DIR"
    if [ ! -d "${SERVICE_DIR}/dist" ]; then
        echo -e "${red}错误：未在当前目录找到 'dist' 构建产物目录。请确保将包含 dist 的代码包解压到当前位置。${plain}"
        exit 1
    fi
    files_to_copy=("dist" "$ENV_FILE" "keys/aes.key" "keys/root-pub.pem" "pm2.conf.json" "package.json" "pnpm-lock.yaml")

    copy_errors=0
    for item in "${files_to_copy[@]}"; do
        source_path="${SERVICE_DIR}/${item}"
        target_path="${container_name_or_id}:${container_app_dir}/${item}"
        if [ -e "$source_path" ]; then
            echo "  复制: $source_path -> ${container_app_dir}/${item}"
            if [ -d "$source_path" ]; then
                 docker exec "$container_name_or_id" rm -rf "${container_app_dir}/${item}" || echo -e "${yellow}警告：无法删除容器内旧目录 ${container_app_dir}/${item} (可能不存在)，继续...${plain}"
            else
                 docker exec "$container_name_or_id" rm -f "${container_app_dir}/${item}" || echo -e "${yellow}警告：无法删除容器内旧文件 ${container_app_dir}/${item} (可能不存在)，继续...${plain}"
            fi
            # 使用 -a 选项保留文件属性，可能更有用
            docker cp -a "$source_path" "$target_path"
            if [ $? -ne 0 ]; then
                echo -e "${red}复制 $item 失败。${plain}"
                copy_errors=$((copy_errors + 1))
            fi
            # 验证复制（对关键文件）
            if [[ "$item" == "dist" || "$item" == "$ENV_FILE" ]]; then
                 if ! docker exec "$container_name_or_id" ls "${container_app_dir}/${item}" > /dev/null 2>&1; then
                     echo -e "${red}验证失败：$item 未能成功复制到容器内！${plain}"
                     copy_errors=$((copy_errors + 1))
                 fi
            fi
        else
            if [ "$item" == "$ENV_FILE" ]; then
                 echo -e "${yellow}警告：宿主机未找到 $ENV_FILE 文件，容器内的配置将不会被覆盖。${plain}"
            # dist 目录不存在是致命错误，上面已检查
            #elif [ "$item" == "dist" ]; then ...
            elif [[ "$item" == *"lock.yaml" ]]; then
                echo -e "${yellow}警告：未找到 lock 文件，容器内依赖可能不会精确更新。${plain}"
            else
                 echo -e "${yellow}警告：源文件/目录 $source_path 不存在，跳过复制。${plain}"
            fi
        fi
    done

    if [ $copy_errors -ne 0 ]; then
        echo -e "${red}文件复制过程中出现错误，请检查上面的日志。更新中止。${plain}"
        exit 1
    fi
    echo -e "${green}文件复制完成。${plain}"

    echo "正在容器内运行 pnpm install..."
    # 使用 --no-frozen-lockfile 允许根据 package.json 更新，因为 lock 文件可能来自不同环境
    # 或者坚持 --frozen-lockfile 如果 lock 文件确定是兼容的
    docker exec "$container_name_or_id" pnpm install --prod --frozen-lockfile
    if [ $? -ne 0 ]; then
        echo -e "${red}在容器内运行 pnpm install 失败。请检查容器环境和网络。${plain}"
        # 这里不退出，允许尝试重启
    fi

    echo "正在容器内重启应用 (pm2 restart $PM2_APP_NAME)..."
    docker exec "$container_name_or_id" pm2 restart "$PM2_APP_NAME"
    if [ $? -ne 0 ]; then
        echo -e "${red}在容器内重启 PM2 应用失败。请尝试手动进入容器检查 ('docker exec -it $container_name_or_id bash')。${plain}"
    else
        echo -e "${green}应用已在容器内重启。${plain}"
    fi

    echo -e "${green}Docker 原地更新完成！${plain}"
}

# 选择操作
echo "请选择操作："
echo "1. Node.js 全新部署"
echo "2. Node.js 升级 (git pull, install, build, restart)"
echo "3. Docker-compose 部署 (首次启动)"
echo "4. Docker 原地更新 (复制文件到运行中容器并重启)"

read -p "请输入数字（1-4，输入q退出）[默认: 1]: " operation_choice
operation_choice=${operation_choice:-1}

if [[ "$operation_choice" == "q" ]]; then
  echo "退出脚本"
  exit
fi

# 在执行具体操作前检查依赖
check_dependencies # 移到这里，确保选择后才检查依赖

case $operation_choice in
  1)
    echo "Node.js 全新部署选择"
    node_deploy_new
    ;;
  2)
    echo "Node.js 升级选择"
    node_deploy_update
    ;;
  3)
    echo "Docker-compose 部署选择"
    config_compose
    ;;
  4)
    echo "Docker 原地更新选择"
    docker_update_inplace # 调用新的更新函数
    ;;
  *)
    echo "无效选择，退出。"
    # quit # quit 命令可能不存在，直接退出
    exit 1
    ;;
esac

# 部署/更新成功后的统一消息（如果执行到这里说明没有中途 exit）
echo -e "=================================================================="
echo -e "\033[32m操作成功完成!\033[0m"
echo -e "=================================================================="
