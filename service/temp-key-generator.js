const aesKey = 'V3jo4xSjPZH5k3DajA3D9Hc4NoJgNEt9I1rVNhq6VLM=';
const crypto = require('crypto');

function splitAndEncodeKey(aesKey) {
    // 确保密钥有足够长度
    if (!aesKey || aesKey.length < 32) {
        throw new Error('AES密钥长度不足，无法安全分割');
    }

    // 将密钥分成四个部分
    const keyLength = aesKey.length;
    const segmentLength = Math.floor(keyLength / 4);

    const segment1 = aesKey.substring(0, segmentLength);
    const segment2 = aesKey.substring(segmentLength, segmentLength * 2);
    const segment3 = aesKey.substring(segmentLength * 2, segmentLength * 3);
    const segment4 = aesKey.substring(segmentLength * 3);

    // 以不同方式编码各个片段
    const encodedSegment1 = Buffer.from(segment1).toString('base64');
    const encodedSegment2 = Buffer.from(segment2).toString('base64');

    // 将segment3转换为ASCII码数组并增加偏移
    const asciiSegment3 = Array.from(segment3).map(char => char.charCodeAt(0) + 5);

    // 将segment4转换为十六进制
    const hexSegment4 = Array.from(segment4)
        .map(char => char.charCodeAt(0).toString(16))
        .join('');

    return {
        segment1Base64: encodedSegment1,
        segment2Base64: encodedSegment2,
        segment3Ascii: asciiSegment3,
        segment4Hex: hexSegment4
    };
}

const encodedKeyParts = splitAndEncodeKey(aesKey);
console.log(JSON.stringify(encodedKeyParts, null, 2));
